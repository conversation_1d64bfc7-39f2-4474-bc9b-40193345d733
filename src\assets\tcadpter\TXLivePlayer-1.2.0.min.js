!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).TXLivePlayer=t()}(this,(function(){"use strict";function e(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t=function(){return t=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},t.apply(this,arguments)};function r(e,t,r,n){return new(r||(r=Promise))((function(i,o){function s(e){try{c(n.next(e))}catch(e){o(e)}}function a(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}c((n=n.apply(e,t||[])).next())}))}function n(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}function i(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function o(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function s(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var a={exports:{}};!function(e,t){!function(e){var t="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==t&&t,r={searchParams:"URLSearchParams"in t,iterable:"Symbol"in t&&"iterator"in Symbol,blob:"FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(e){return!1}}(),formData:"FormData"in t,arrayBuffer:"ArrayBuffer"in t};function n(e){return e&&DataView.prototype.isPrototypeOf(e)}if(r.arrayBuffer)var i=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],o=ArrayBuffer.isView||function(e){return e&&i.indexOf(Object.prototype.toString.call(e))>-1};function s(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||""===e)throw new TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function a(e){return"string"!=typeof e&&(e=String(e)),e}function c(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return r.iterable&&(t[Symbol.iterator]=function(){return t}),t}function d(e){this.map={},e instanceof d?e.forEach((function(e,t){this.append(t,e)}),this):Array.isArray(e)?e.forEach((function(e){this.append(e[0],e[1])}),this):e&&Object.getOwnPropertyNames(e).forEach((function(t){this.append(t,e[t])}),this)}function u(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function l(e){return new Promise((function(t,r){e.onload=function(){t(e.result)},e.onerror=function(){r(e.error)}}))}function p(e){var t=new FileReader,r=l(t);return t.readAsArrayBuffer(e),r}function f(e){var t=new FileReader,r=l(t);return t.readAsText(e),r}function h(e){for(var t=new Uint8Array(e),r=new Array(t.length),n=0;n<t.length;n++)r[n]=String.fromCharCode(t[n]);return r.join("")}function m(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function v(){return this.bodyUsed=!1,this._initBody=function(e){this.bodyUsed=this.bodyUsed,this._bodyInit=e,e?"string"==typeof e?this._bodyText=e:r.blob&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:r.formData&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:r.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():r.arrayBuffer&&r.blob&&n(e)?(this._bodyArrayBuffer=m(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):r.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(e)||o(e))?this._bodyArrayBuffer=m(e):this._bodyText=e=Object.prototype.toString.call(e):this._bodyText="",this.headers.get("content-type")||("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):r.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},r.blob&&(this.blob=function(){var e=u(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var e=u(this);return e||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}return this.blob().then(p)}),this.text=function(){var e=u(this);if(e)return e;if(this._bodyBlob)return f(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(h(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},r.formData&&(this.formData=function(){return this.text().then(C)}),this.json=function(){return this.text().then(JSON.parse)},this}d.prototype.append=function(e,t){e=s(e),t=a(t);var r=this.map[e];this.map[e]=r?r+", "+t:t},d.prototype.delete=function(e){delete this.map[s(e)]},d.prototype.get=function(e){return e=s(e),this.has(e)?this.map[e]:null},d.prototype.has=function(e){return this.map.hasOwnProperty(s(e))},d.prototype.set=function(e,t){this.map[s(e)]=a(t)},d.prototype.forEach=function(e,t){for(var r in this.map)this.map.hasOwnProperty(r)&&e.call(t,this.map[r],r,this)},d.prototype.keys=function(){var e=[];return this.forEach((function(t,r){e.push(r)})),c(e)},d.prototype.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),c(e)},d.prototype.entries=function(){var e=[];return this.forEach((function(t,r){e.push([r,t])})),c(e)},r.iterable&&(d.prototype[Symbol.iterator]=d.prototype.entries);var y=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function g(e){var t=e.toUpperCase();return y.indexOf(t)>-1?t:e}function b(e,t){if(!(this instanceof b))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var r=(t=t||{}).body;if(e instanceof b){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new d(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,r||null==e._bodyInit||(r=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",!t.headers&&this.headers||(this.headers=new d(t.headers)),this.method=g(t.method||this.method||"GET"),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&r)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(r),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==t.cache&&"no-cache"!==t.cache)){var n=/([?&])_=[^&]*/;if(n.test(this.url))this.url=this.url.replace(n,"$1_="+(new Date).getTime());else{var i=/\?/;this.url+=(i.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}}function C(e){var t=new FormData;return e.trim().split("&").forEach((function(e){if(e){var r=e.split("="),n=r.shift().replace(/\+/g," "),i=r.join("=").replace(/\+/g," ");t.append(decodeURIComponent(n),decodeURIComponent(i))}})),t}function _(e){var t=new d;return e.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(e){return 0===e.indexOf("\n")?e.substr(1,e.length):e})).forEach((function(e){var r=e.split(":"),n=r.shift().trim();if(n){var i=r.join(":").trim();t.append(n,i)}})),t}function T(e,t){if(!(this instanceof T))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText=void 0===t.statusText?"":""+t.statusText,this.headers=new d(t.headers),this.url=t.url||"",this._initBody(e)}b.prototype.clone=function(){return new b(this,{body:this._bodyInit})},v.call(b.prototype),v.call(T.prototype),T.prototype.clone=function(){return new T(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new d(this.headers),url:this.url})},T.error=function(){var e=new T(null,{status:0,statusText:""});return e.type="error",e};var S=[301,302,303,307,308];T.redirect=function(e,t){if(-1===S.indexOf(t))throw new RangeError("Invalid status code");return new T(null,{status:t,headers:{location:e}})},e.DOMException=t.DOMException;try{new e.DOMException}catch(t){e.DOMException=function(e,t){this.message=e,this.name=t;var r=Error(e);this.stack=r.stack},e.DOMException.prototype=Object.create(Error.prototype),e.DOMException.prototype.constructor=e.DOMException}function E(n,i){return new Promise((function(o,s){var c=new b(n,i);if(c.signal&&c.signal.aborted)return s(new e.DOMException("Aborted","AbortError"));var u=new XMLHttpRequest;function l(){u.abort()}function p(e){try{return""===e&&t.location.href?t.location.href:e}catch(t){return e}}u.onload=function(){var e={status:u.status,statusText:u.statusText,headers:_(u.getAllResponseHeaders()||"")};e.url="responseURL"in u?u.responseURL:e.headers.get("X-Request-URL");var t="response"in u?u.response:u.responseText;setTimeout((function(){o(new T(t,e))}),0)},u.onerror=function(){setTimeout((function(){s(new TypeError("Network request failed"))}),0)},u.ontimeout=function(){setTimeout((function(){s(new TypeError("Network request failed"))}),0)},u.onabort=function(){setTimeout((function(){s(new e.DOMException("Aborted","AbortError"))}),0)},u.open(c.method,p(c.url),!0),"include"===c.credentials?u.withCredentials=!0:"omit"===c.credentials&&(u.withCredentials=!1),"responseType"in u&&(r.blob?u.responseType="blob":r.arrayBuffer&&c.headers.get("Content-Type")&&-1!==c.headers.get("Content-Type").indexOf("application/octet-stream")&&(u.responseType="arraybuffer")),!i||"object"!=typeof i.headers||i.headers instanceof d?c.headers.forEach((function(e,t){u.setRequestHeader(t,e)})):Object.getOwnPropertyNames(i.headers).forEach((function(e){u.setRequestHeader(e,a(i.headers[e]))})),c.signal&&(c.signal.addEventListener("abort",l),u.onreadystatechange=function(){4===u.readyState&&c.signal.removeEventListener("abort",l)}),u.send(void 0===c._bodyInit?null:c._bodyInit)}))}E.polyfill=!0,t.fetch||(t.fetch=E,t.Headers=d,t.Request=b,t.Response=T),e.Headers=d,e.Request=b,e.Response=T,e.fetch=E,Object.defineProperty(e,"__esModule",{value:!0})}(t)}(0,a.exports);let c=!0,d=!0;function u(e,t,r){const n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)}function l(e,t,r){if(!e.RTCPeerConnection)return;const n=e.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return i.apply(this,arguments);const o=e=>{const t=r(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,o),i.apply(this,[e,o])};const o=n.removeEventListener;n.removeEventListener=function(e,r){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(r))return o.apply(this,arguments);const n=this._eventMap[t].get(r);return this._eventMap[t].delete(r),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function p(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(c=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function f(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(d=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function h(){if("object"==typeof window){if(c)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function m(e,t){d&&console.warn(e+" is deprecated, please use "+t+" instead.")}function v(e){return"[object Object]"===Object.prototype.toString.call(e)}function y(e){return v(e)?Object.keys(e).reduce((function(t,r){const n=v(e[r]),i=n?y(e[r]):e[r],o=n&&!Object.keys(i).length;return void 0===i||o?t:Object.assign(t,{[r]:i})}),{}):e}function g(e,t,r){t&&!r.has(t.id)&&(r.set(t.id,t),Object.keys(t).forEach((n=>{n.endsWith("Id")?g(e,e.get(t[n]),r):n.endsWith("Ids")&&t[n].forEach((t=>{g(e,e.get(t),r)}))})))}function b(e,t,r){const n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const o=[];return e.forEach((e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)})),o.forEach((t=>{e.forEach((r=>{r.type===n&&r.trackId===t.id&&g(e,r,i)}))})),i}const C=h;function _(e,t){const r=e&&e.navigator;if(!r.mediaDevices)return;const n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach((r=>{if("require"===r||"advanced"===r||"mediaSource"===r)return;const n="object"==typeof e[r]?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let e={};"number"==typeof n.ideal?(e[i("min",r)]=n.ideal,t.optional.push(e),e={},e[i("max",r)]=n.ideal,t.optional.push(e)):(e[i("",r)]=n.ideal,t.optional.push(e))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",r)]=n.exact):["min","max"].forEach((e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,r)]=n[e])}))})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const s=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!r.mediaDevices.getSupportedConstraints||!r.mediaDevices.getSupportedConstraints().facingMode||s)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return r.mediaDevices.enumerateDevices().then((r=>{let s=(r=r.filter((e=>"videoinput"===e.kind))).find((e=>t.some((t=>e.label.toLowerCase().includes(t)))));return!s&&r.length&&t.includes("back")&&(s=r[r.length-1]),s&&(e.video.deviceId=o.exact?{exact:s.deviceId}:{ideal:s.deviceId}),e.video=n(e.video),C("chrome: "+JSON.stringify(e)),i(e)}))}e.video=n(e.video)}return C("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(r.getUserMedia=function(e,t,n){i(e,(e=>{r.webkitGetUserMedia(e,t,(e=>{n&&n(o(e))}))}))}.bind(r),r.mediaDevices.getUserMedia){const e=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(t){return i(t,(t=>e(t).then((e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach((e=>{e.stop()})),new DOMException("","NotFoundError");return e}),(e=>Promise.reject(o(e))))))}}}function T(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function S(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",(r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===r.track.id)):{track:r.track};const i=new Event("track");i.track=r.track,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)})),t.stream.getTracks().forEach((r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===r.id)):{track:r};const i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else l(e,"track",(e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e)))}function E(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){let i=r.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach((e=>{this._senders.push(t(this,e))}))};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach((e=>{const t=this._senders.find((t=>t.track===e));t&&this._senders.splice(this._senders.indexOf(t),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function R(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,r,n]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach((e=>{const r={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((t=>{r[t]=e.stat(t)})),t[r.id]=r})),t},o=function(e){return new Map(Object.keys(e).map((t=>[t,e[t]])))};if(arguments.length>=2){const n=function(e){r(o(i(e)))};return t.apply(this,[n,e])}return new Promise(((e,r)=>{t.apply(this,[function(t){e(o(i(t)))},r])})).then(r,n)}}function P(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>b(t,e.track,!0)))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),l(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>b(t,e.track,!1)))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,r,n;return this.getSenders().forEach((r=>{r.track===e&&(t?n=!0:t=r)})),this.getReceivers().forEach((t=>(t.track===e&&(r?n=!0:r=t),t.track===e))),n||t&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function w(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((e=>this._shimmedLocalStreams[e][0]))};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){if(!r)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const n=t.apply(this,arguments);return this._shimmedLocalStreams[r.id]?-1===this._shimmedLocalStreams[r.id].indexOf(n)&&this._shimmedLocalStreams[r.id].push(n):this._shimmedLocalStreams[r.id]=[r,n],n};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")}));const t=this.getSenders();r.apply(this,arguments);const n=this.getSenders().filter((e=>-1===t.indexOf(e)));this._shimmedLocalStreams[e.id]=[e].concat(n)};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],n.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((t=>{const r=this._shimmedLocalStreams[t].indexOf(e);-1!==r&&this._shimmedLocalStreams[t].splice(r,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]})),i.apply(this,arguments)}}function M(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return w(e);const r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map((e=>this._reverseStreams[e.id]))};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){const r=new e.MediaStream(t.getTracks());this._streams[t.id]=r,this._reverseStreams[r.id]=t,t=r}n.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(new RegExp(i.id,"g"),n.id)})),new RTCSessionDescription({type:t.type,sdp:r})}function s(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(new RegExp(n.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:r})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,r){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find((e=>e===t)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find((e=>e.track===t));if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[r.id];if(o)o.addTrack(t),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const n=new e.MediaStream([t]);this._streams[r.id]=n,this._reverseStreams[n.id]=r,this.addStream(n)}return this.getSenders().find((e=>e.track===t))},["createOffer","createAnswer"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?r.apply(this,[t=>{const r=o(this,t);e[0].apply(null,[r])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):r.apply(this,arguments).then((e=>o(this,e)))}};e.RTCPeerConnection.prototype[t]=n[t]}));const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=s(this,arguments[0]),a.apply(this,arguments)):a.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach((r=>{this._streams[r].getTracks().find((t=>e.track===t))&&(t=this._streams[r])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function k(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}))}function D(e,t){l(e,"negotiationneeded",(e=>{const r=e.target;if(!(t.version<72||r.getConfiguration&&"plan-b"===r.getConfiguration().sdpSemantics)||"stable"===r.signalingState)return e}))}var A=Object.freeze({__proto__:null,shimMediaStream:T,shimOnTrack:S,shimGetSendersWithDtmf:E,shimGetStats:R,shimSenderReceiverGetStats:P,shimAddTrackRemoveTrackWithNative:w,shimAddTrackRemoveTrack:M,shimPeerConnection:k,fixNegotiationNeeded:D,shimGetUserMedia:_,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(r){return t(r).then((t=>{const n=r.video&&r.video.width,i=r.video&&r.video.height,o=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},n&&(r.video.mandatory.maxWidth=n),i&&(r.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(r)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});function O(e,t){const r=e&&e.navigator,n=e&&e.MediaStreamTrack;if(r.getUserMedia=function(e,t,n){m("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){const e=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},t=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(r){return"object"==typeof r&&"object"==typeof r.audio&&(r=JSON.parse(JSON.stringify(r)),e(r.audio,"autoGainControl","mozAutoGainControl"),e(r.audio,"noiseSuppression","mozNoiseSuppression")),t(r)},n&&n.prototype.getSettings){const t=n.prototype.getSettings;n.prototype.getSettings=function(){const r=t.apply(this,arguments);return e(r,"mozAutoGainControl","autoGainControl"),e(r,"mozNoiseSuppression","noiseSuppression"),r}}if(n&&n.prototype.applyConstraints){const t=n.prototype.applyConstraints;n.prototype.applyConstraints=function(r){return"audio"===this.kind&&"object"==typeof r&&(r=JSON.parse(JSON.stringify(r)),e(r,"autoGainControl","mozAutoGainControl"),e(r,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[r])}}}}function L(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function x(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}));const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,o]=arguments;return n.apply(this,[e||null]).then((e=>{if(t.version<53&&!i)try{e.forEach((e=>{e.type=r[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach(((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))}))}return e})).then(i,o)}}function I(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function F(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),l(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function N(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){m("removeStream","removeTrack"),this.getSenders().forEach((t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)}))})}function j(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function B(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];const e=arguments[1],r=e&&"sendEncodings"in e;r&&e.sendEncodings.forEach((e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const n=t.apply(this,arguments);if(r){const{sender:t}=n,r=t.getParameters();(!("encodings"in r)||1===r.encodings.length&&0===Object.keys(r.encodings[0]).length)&&(r.encodings=e.sendEncodings,t.sendEncodings=e.sendEncodings,this.setParametersPromises.push(t.setParameters(r).then((()=>{delete t.sendEncodings})).catch((()=>{delete t.sendEncodings}))))}return n})}function V(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function U(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function G(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}var z=Object.freeze({__proto__:null,shimOnTrack:L,shimPeerConnection:x,shimSenderGetStats:I,shimReceiverGetStats:F,shimRemoveStream:N,shimRTCDataChannel:j,shimAddTransceiver:B,shimGetParameters:V,shimCreateOffer:U,shimCreateAnswer:G,shimGetUserMedia:O,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(r){if(!r||!r.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===r.video?r.video={mediaSource:t}:r.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(r)})}});function W(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((r=>t.call(this,r,e))),e.getVideoTracks().forEach((r=>t.call(this,r,e)))},e.RTCPeerConnection.prototype.addTrack=function(e,...r){return r&&r.forEach((e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const r=e.getTracks();this.getSenders().forEach((e=>{r.includes(e.track)&&this.removeTrack(e)}))})}}function Y(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach((e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)}))})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const r=new Event("addstream");r.stream=t,e.dispatchEvent(r)}))}),t.apply(e,arguments)}}}function q(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i};let a=function(e,t,r){const n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=a,a=function(e,t,r){const n=o.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=a,a=function(e,t,r){const n=s.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=a}function H(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,r=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>r(J(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,r,n){t.mediaDevices.getUserMedia(e).then(r,n)}.bind(t))}function J(e){return e&&void 0!==e.video?Object.assign({},e,{video:y(e.video)}):e}function Q(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,r){if(e&&e.iceServers){const t=[];for(let r=0;r<e.iceServers.length;r++){let n=e.iceServers[r];!n.hasOwnProperty("urls")&&n.hasOwnProperty("url")?(m("RTCIceServer.url","RTCIceServer.urls"),n=JSON.parse(JSON.stringify(n)),n.urls=n.url,delete n.url,t.push(n)):t.push(e.iceServers[r])}e.iceServers=t}return new t(e,r)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function K(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Z(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find((e=>"audio"===e.receiver.track.kind));!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const r=this.getTransceivers().find((e=>"video"===e.receiver.track.kind));!1===e.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveVideo||r||this.addTransceiver("video")}return t.apply(this,arguments)}}function X(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var $=Object.freeze({__proto__:null,shimLocalStreamsAPI:W,shimRemoteStreamsAPI:Y,shimCallbacksAPI:q,shimGetUserMedia:H,shimConstraints:J,shimRTCIceServerUrls:Q,shimTrackEventTransceiver:K,shimCreateOfferLegacy:Z,shimAudioContext:X}),ee={exports:{}};!function(e){const t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((e=>e.trim()))},t.splitSections=function(e){return e.split("\nm=").map(((e,t)=>(t>0?"m="+e:e).trim()+"\r\n"))},t.getDescription=function(e){const r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){const r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter((e=>0===e.indexOf(r)))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const r={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":r.relatedAddress=t[e+1];break;case"rport":r.relatedPort=parseInt(t[e+1],10);break;case"tcptype":r.tcpType=t[e+1];break;case"ufrag":r.ufrag=t[e+1],r.usernameFragment=t[e+1];break;default:void 0===r[t[e]]&&(r[t[e]]=t[e+1])}return r},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const r=e.component;"rtp"===r?t.push(1):"rtcp"===r?t.push(2):t.push(r),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){let t=e.substr(9).split(" ");const r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){const t={};let r;const n=e.substr(e.indexOf(" ")+1).split(";");for(let e=0;e<n.length;e++)r=n[e].trim().split("="),t[r[0].trim()]=r[1];return t},t.writeFmtp=function(e){let t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const n=[];Object.keys(e.parameters).forEach((t=>{void 0!==e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)})),t+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((e=>{t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substr(t+1,n-t-1),r.value=e.substr(n+1)):r.attribute=e.substr(t+1),r},t.parseSsrcGroup=function(e){const t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((e=>parseInt(e,10)))}},t.getMid=function(e){const r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substr(6)},t.parseFingerprint=function(e){const t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,r){return{role:"auto",fingerprints:t.matchPrefix(e+r,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let r="a=setup:"+t+"\r\n";return e.fingerprints.forEach((e=>{r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),r},t.parseCryptoLine=function(e){const t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){return t.matchPrefix(e+r,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,r){const n=t.matchPrefix(e+r,"a=ice-ufrag:")[0],i=t.matchPrefix(e+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substr(12),password:i.substr(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" ");for(let i=3;i<n.length;i++){const o=n[i],s=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(s){const n=t.parseRtpMap(s),i=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(n.parameters=i.length?t.parseFmtp(i[0]):{},n.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),r.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(n.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach((e=>{r.headerExtensions.push(t.parseExtmap(e))})),r},t.writeRtpDescription=function(e,r){let n="";n+="m="+e+" ",n+=r.codecs.length>0?"9":"0",n+=" UDP/TLS/RTP/SAVPF ",n+=r.codecs.map((e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType)).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach((e=>{n+=t.writeRtpMap(e),n+=t.writeFmtp(e),n+=t.writeRtcpFb(e)}));let i=0;return r.codecs.forEach((e=>{e.maxptime>i&&(i=e.maxptime)})),i>0&&(n+="a=maxptime:"+i+"\r\n"),r.headerExtensions&&r.headerExtensions.forEach((e=>{n+=t.writeExtmap(e)})),n},t.parseRtpEncodingParameters=function(e){const r=[],n=t.parseRtpParameters(e),i=-1!==n.fecMechanisms.indexOf("RED"),o=-1!==n.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute)),a=s.length>0&&s[0].ssrc;let c;const d=t.matchPrefix(e,"a=ssrc-group:FID").map((e=>e.substr(17).split(" ").map((e=>parseInt(e,10)))));d.length>0&&d[0].length>1&&d[0][0]===a&&(c=d[0][1]),n.codecs.forEach((e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:a,codecPayloadType:parseInt(e.parameters.apt,10)};a&&c&&(t.rtx={ssrc:c}),r.push(t),i&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:a,mechanism:o?"red+ulpfec":"red"},r.push(t))}})),0===r.length&&a&&r.push({ssrc:a});let u=t.matchPrefix(e,"b=");return u.length&&(u=0===u[0].indexOf("b=TIAS:")?parseInt(u[0].substr(7),10):0===u[0].indexOf("b=AS:")?1e3*parseInt(u[0].substr(5),10)*.95-16e3:void 0,r.forEach((e=>{e.maxBitrate=u}))),r},t.parseRtcpParameters=function(e){const r={},n=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute))[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);const i=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;const o=t.matchPrefix(e,"a=rtcp-mux");return r.mux=o.length>0,r},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let r;const n=t.matchPrefix(e,"a=msid:");if(1===n.length)return r=n[0].substr(7).split(" "),{stream:r[0],track:r[1]};const i=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"msid"===e.attribute));return i.length>0?(r=i[0].value.split(" "),{stream:r[0],track:r[1]}):void 0},t.parseSctpDescription=function(e){const r=t.parseMLine(e),n=t.matchPrefix(e,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substr(19),10)),isNaN(i)&&(i=65536);const o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substr(12),10),protocol:r.fmt,maxMessageSize:i};const s=t.matchPrefix(e,"a=sctpmap:");if(s.length>0){const e=s[0].substr(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:i}}},t.writeSctpDescription=function(e,t){let r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,r,n){let i;const o=void 0!==r?r:2;i=e||t.generateSessionId();return"v=0\r\no="+(n||"thisisadapterortc")+" "+i+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,r){const n=t.splitLines(e);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substr(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const r=t.splitLines(e)[0].substr(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},t.parseOLine=function(e){const r=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const r=t.splitLines(e);for(let e=0;e<r.length;e++)if(r[e].length<2||"="!==r[e].charAt(1))return!1;return!0},e.exports=t}(ee);var te=ee.exports,re=Object.freeze(e({__proto__:null,default:te},[ee.exports]));function ne(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const r=new t(e),n=te.parseCandidate(e.candidate),i=Object.assign(r,n);return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,l(e,"icecandidate",(t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t)))}function ie(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const r=function(e){if(!e||!e.sdp)return!1;const t=te.splitSections(e.sdp);return t.shift(),t.some((e=>{const t=te.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))},n=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const r=parseInt(t[1],10);return r!=r?-1:r},i=function(e){let r=65536;return"firefox"===t.browser&&(r=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),r},o=function(e,r){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);const i=te.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?n=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==r&&(n=2147483637),n},s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){const e=n(arguments[0]),t=i(e),r=o(arguments[0],e);let s;s=0===t&&0===r?Number.POSITIVE_INFINITY:0===t||0===r?Math.max(t,r):Math.min(t,r);const a={};Object.defineProperty(a,"maxMessageSize",{get:()=>s}),this._sctp=a}return s.apply(this,arguments)}}function oe(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const r=e.send;e.send=function(){const n=arguments[0],i=n.length||n.size||n.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return r.apply(e,arguments)}}const r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=r.apply(this,arguments);return t(e,this),e},l(e,"datachannel",(e=>(t(e.channel,e.target),e)))}function se(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((e=>{const r=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}}))}function ae(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const r=t.sdp.split("\n").filter((e=>"a=extmap-allow-mixed"!==e.trim())).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:r}):t.sdp=r}return r.apply(this,arguments)}}function ce(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const r=e.RTCPeerConnection.prototype.addIceCandidate;r&&0!==r.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function de(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const r=e.RTCPeerConnection.prototype.setLocalDescription;r&&0!==r.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return r.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return r.apply(this,[e]);const t="offer"===e.type?this.createOffer:this.createAnswer;return t.apply(this).then((e=>r.apply(this,[e])))})}var ue=Object.freeze({__proto__:null,shimRTCIceCandidate:ne,shimMaxMessageSize:ie,shimSendThrowTypeError:oe,shimConnectionState:se,removeExtmapAllowMixed:ae,shimAddIceCandidateNullOrEmpty:ce,shimParameterlessSetLocalDescription:de});const le=function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const r=h,n=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator)return t.browser="Not a browser.",t;const{navigator:r}=e;if(r.mozGetUserMedia)t.browser="firefox",t.version=u(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=u(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!r.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=u(r.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),i={browserDetails:n,commonShim:ue,extractVersion:u,disableLog:p,disableWarnings:f,sdp:re};switch(n.browser){case"chrome":if(!A||!k||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),i;if(null===n.version)return r("Chrome shim can not determine version, not shimming."),i;r("adapter.js shimming chrome."),i.browserShim=A,ce(e,n),de(e),_(e,n),T(e),k(e,n),S(e),M(e,n),E(e),R(e),P(e),D(e,n),ne(e),se(e),ie(e,n),oe(e),ae(e,n);break;case"firefox":if(!z||!x||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),i;r("adapter.js shimming firefox."),i.browserShim=z,ce(e,n),de(e),O(e,n),x(e,n),L(e),N(e),I(e),F(e),j(e),B(e),V(e),U(e),G(e),ne(e),se(e),ie(e,n),oe(e);break;case"safari":if(!$||!t.shimSafari)return r("Safari shim is not included in this adapter release."),i;r("adapter.js shimming safari."),i.browserShim=$,ce(e,n),de(e),Q(e),Z(e),q(e),W(e),Y(e),K(e),H(e),X(e),ne(e),ie(e,n),oe(e),ae(e,n);break;default:r("Unsupported browser!")}return i}({window:"undefined"==typeof window?void 0:window});var pe={exports:{}};!function(e,t){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){t.__esModule=!0,t.default=void 0;var n=r(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var i=e.getVersionPrecision(t),o=e.getVersionPrecision(r),s=Math.max(i,o),a=0,c=e.map([t,r],(function(t){var r=s-e.getVersionPrecision(t),n=t+new Array(r+1).join(".0");return e.map(n.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(n&&(a=s-Math.min(i,o)),s-=1;s>=a;){if(c[0][s]>c[1][s])return 1;if(c[0][s]===c[1][s]){if(s===a)return 0;s-=1}else if(c[0][s]<c[1][s])return-1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var i=e[r];if(t(i,r))return i}},e.assign=function(e){for(var t,r,n=e,i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];if(Object.assign)return Object.assign.apply(Object,[e].concat(o));var a=function(){var e=o[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){n[t]=e[t]}))};for(t=0,r=o.length;t<r;t+=1)a();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,r){t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(91))&&n.__esModule?n:{default:n},o=r(18);function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function(){function e(){}var t,r,n;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},t=e,n=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],(r=null)&&s(t.prototype,r),n&&s(t,n),e}();t.default=a,e.exports=t.default},91:function(e,t,r){t.__esModule=!0,t.default=void 0;var n=c(r(92)),i=c(r(93)),o=c(r(94)),s=c(r(95)),a=c(r(17));function c(e){return e&&e.__esModule?e:{default:e}}var d=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,i={},o=0;if(Object.keys(e).forEach((function(t){var s=e[t];"string"==typeof s?(i[t]=s,o+=1):"object"==typeof s&&(r[t]=s,n+=1)})),n>0){var s=Object.keys(r),c=a.default.find(s,(function(e){return t.isOS(e)}));if(c){var d=this.satisfies(r[c]);if(void 0!==d)return d}var u=a.default.find(s,(function(e){return t.isPlatform(e)}));if(u){var l=this.satisfies(r[u]);if(void 0!==l)return l}}if(o>0){var p=Object.keys(i),f=a.default.find(p,(function(e){return t.isBrowser(e,!0)}));if(void 0!==f)return this.compareVersion(i[f])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=a.default.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(a.default.compareVersions(i,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=d,e.exports=t.default},92:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=/version\/(\d+(\.?_?\d+)+)/i,s=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=s,e.exports=t.default},93:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),s=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:o.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=i.default.getWindowsVersionName(t);return{name:o.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:o.OS_MAP.iOS},r=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=i.default.getMacOSVersionName(t),n={name:o.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=i.default.getAndroidVersionName(t),n={name:o.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:o.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:o.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.PlayStation4,version:t}}}];t.default=s,e.exports=t.default},94:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),s=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];t.default=s,e.exports=t.default},95:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),s=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:o.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:o.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:o.ENGINE_MAP.Trident},r=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:o.ENGINE_MAP.Presto},r=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:o.ENGINE_MAP.Gecko},r=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:o.ENGINE_MAP.WebKit},r=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=s,e.exports=t.default}})}(pe);var fe,he,me,ve,ye,ge,be=s(pe.exports),Ce="max-width:100%;max-height:100%;object-fit:contain;display:block;margin:0 auto;";!function(e){e[e.DISCONNECTED=0]="DISCONNECTED",e[e.CONNECTING=1]="CONNECTING",e[e.ESTABLISHED=2]="ESTABLISHED"}(fe||(fe={})),function(e){e[e.FAILURE=-1]="FAILURE",e[e.SUCCESS=0]="SUCCESS"}(he||(he={})),function(e){e[e.NEED_RECONNECT=-1]="NEED_RECONNECT",e[e.MANUAL_CLOSE=0]="MANUAL_CLOSE"}(me||(me={})),function(e){e[e.SEND=0]="SEND",e[e.RECEIVE=1]="RECEIVE"}(ve||(ve={})),function(e){e[e.INIT=0]="INIT",e[e.PLAYING=1]="PLAYING",e[e.WAITING=2]="WAITING"}(ye||(ye={})),function(e){e[e.PLAY_EVT_STREAM_BEGIN=1001]="PLAY_EVT_STREAM_BEGIN",e[e.PLAY_EVT_SERVER_CONNECTED=1002]="PLAY_EVT_SERVER_CONNECTED",e[e.PLAY_EVT_PLAY_BEGIN=1003]="PLAY_EVT_PLAY_BEGIN",e[e.PLAY_EVT_PLAY_STOP=1004]="PLAY_EVT_PLAY_STOP",e[e.PLAY_EVT_SERVER_RECONNECT=1005]="PLAY_EVT_SERVER_RECONNECT",e[e.PLAY_EVT_STREAM_EMPTY=1006]="PLAY_EVT_STREAM_EMPTY",e[e.PLAY_EVT_REQUEST_PULL_BEGIN=1007]="PLAY_EVT_REQUEST_PULL_BEGIN",e[e.PLAY_EVT_REQUEST_PULL_SUCCESS=1008]="PLAY_EVT_REQUEST_PULL_SUCCESS",e[e.PLAY_EVT_PLAY_WAITING_BEGIN=1009]="PLAY_EVT_PLAY_WAITING_BEGIN",e[e.PLAY_EVT_PLAY_WAITING_STOP=1010]="PLAY_EVT_PLAY_WAITING_STOP",e[e.PLAY_ERR_WEBRTC_FAIL=-2001]="PLAY_ERR_WEBRTC_FAIL",e[e.PLAY_ERR_REQUEST_PULL_FAIL=-2002]="PLAY_ERR_REQUEST_PULL_FAIL",e[e.PLAY_ERR_PLAY_FAIL=-2003]="PLAY_ERR_PLAY_FAIL",e[e.PLAY_ERR_SERVER_DISCONNECT=-2004]="PLAY_ERR_SERVER_DISCONNECT",e[e.PLAY_ERR_DECODE_FAIL=-2005]="PLAY_ERR_DECODE_FAIL"}(ge||(ge={}));var _e,Te,Se,Ee=/tbs\/(\d+) /i,Re=/OS (\d+)_(\d+)_?(\d+)?/,Pe=Ee.test(navigator.userAgent),we=/firefox\/(\d+)\./i.test(navigator.userAgent),Me=we,ke=/UCBrowser\/(\d+)\./i.test(navigator.userAgent),De=/safari\/(\d+)\./i.test(navigator.userAgent)&&!/chrome\/(\d+)\./i.test(navigator.userAgent),Ae=/iPhone|iPad|iOS/i.test(navigator.userAgent),Oe=Ae,Le=function(){var e=navigator.userAgent.match(Re);return e&&[parseInt(e[1],10),parseInt(e[2],10),parseInt(e[3]||"0",10)]||[]},xe="https://overseas-webrtc.liveplay.myqcloud.com/webrtc/v1",Ie=function(e,t){return fetch(e,{body:JSON.stringify(t),cache:"no-cache",credentials:"same-origin",headers:{"content-type":"application/json"},method:"POST",mode:"cors"}).then((function(e){return e.json()}))},Fe=function(e){return r(void 0,void 0,void 0,(function(){var t,r,i;return n(this,(function(n){switch(n.label){case 0:return xe+"/stopstream",[4,Ie("https://overseas-webrtc.liveplay.myqcloud.com/webrtc/v1/stopstream",e)];case 1:if(t=n.sent(),r=t.errcode,i=t.errmsg,0!==r)throw new Error("stop stream failed, errCode:"+r+", errmsg:"+i);return[2,t]}}))}))},Ne=function(e){return r(void 0,void 0,void 0,(function(){var t,r,i,o,s,a,c;return n(this,(function(n){switch(n.label){case 0:return t=e.streamurl,r=e.sessionid,i=e.localsdp,[4,Ie(t,{version:"v1.0",sessionId:r,localSdp:i})];case 1:if(o=n.sent(),s=o.code,a=o.message,c=o.remoteSdp,200!==s)throw new Error("errCode:"+s+", errMsg:"+a);return[2,{remoteSdp:c,svrSig:null}]}}))}))},je={},Be=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],Ve=Be[0];try{for(var Ue=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(Be),Ge=Ue.next();!Ge.done;Ge=Ue.next()){var ze=Ge.value;if(ze[1]in document){Se=ze;break}}}catch(e){_e={error:e}}finally{try{Ge&&!Ge.done&&(Te=Ue.return)&&Te.call(Ue)}finally{if(_e)throw _e.error}}if(Se)for(var We=0;We<Se.length;We++)je[Ve[We]]=Se[We];var Ye=function(e){if("function"==typeof e.webkitEnterFullScreen){var t=navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1},qe=function(){function e(){this.consoleEnabled=!1}return e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.consoleEnabled&&console.log.apply(console,o([],i(e)))},e.prototype.enableConsole=function(e){this.consoleEnabled=e},e}(),He=new qe,Je=function(){function e(){this.peerConnection=null,this.clientSideDescription=null,this.serverSideDescription=null,this.connectStatus=fe.DISCONNECTED,this.connectDirection=ve.RECEIVE,this.negotiating=!1,this.onAddTrack=null,this.onConnect=null,this.onDisconnect=null,this.onSetLocalDescription=null,this.onError=null,this.onTrack=this.onTrack.bind(this),this.onIceConnectionStateChange=this.onIceConnectionStateChange.bind(this),this.onIceCandidate=this.onIceCandidate.bind(this),this.onSignalingStateChange=this.onSignalingStateChange.bind(this),this.onConnectionStateChange=this.onConnectionStateChange.bind(this),this.onNegotiationNeeded=this.onNegotiationNeeded.bind(this)}return e.prototype.init=function(e){var t=void 0===e?{}:e,r=t.onAddTrack,n=void 0===r?null:r,i=t.onConnect,o=void 0===i?null:i,s=t.onDisconnect,a=void 0===s?null:s,c=t.onSetLocalDescription,d=void 0===c?null:c,u=t.onError,l=void 0===u?null:u;this.onAddTrack=n,this.onConnect=o,this.onDisconnect=a,this.onSetLocalDescription=d,this.onError=l},e.prototype.initWebRTCConnect=function(e){var t=void 0===e?{}:e,i=t.config,o=t.direction,s=void 0===o?ve.RECEIVE:o,a=t.stream;return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return He.log("CloudWebRTC init"),this.peerConnection?(He.log("Call disconnected before connect"),[2]):(this.connectDirection=s,[4,this.createWebRTCConnect(i,a)]);case 1:return e.sent(),[2]}}))}))},e.prototype.connect=function(e){He.log("connection connect->",e),e&&this.onAnswer(e)},e.prototype.disconnect=function(e){var t,r=void 0===e?{}:e,n=r.msg,i=void 0===n?"":n,o=r.code,s=void 0===o?me.MANUAL_CLOSE:o;He.log("connection disconnect->",s,i),this.connectStatus!==fe.DISCONNECTED&&(this.connectStatus=fe.DISCONNECTED,this.negotiating=!1,this.peerConnection&&(this.peerConnection.removeEventListener("track",this.onTrack),this.peerConnection.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.removeEventListener("icecandidate",this.onIceCandidate),this.peerConnection.removeEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.removeEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.removeEventListener("negotiationneeded",this.onNegotiationNeeded),this.peerConnection.close(),this.peerConnection=null),this.clientSideDescription=null,this.serverSideDescription=null,s===me.NEED_RECONNECT&&He.log("Please try to reconnect"),null===(t=this.onDisconnect)||void 0===t||t.call(this,{code:s,msg:i}))},e.prototype.getClientSideDescription=function(){return this.clientSideDescription||He.log("webrtc is not initialized"),this.clientSideDescription},e.prototype.getConnectStatus=function(){return this.connectStatus},e.prototype.getStats=function(){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return this.peerConnection?[4,this.peerConnection.getStats(null)]:[2];case 1:return[2,e.sent()]}}))}))},e.prototype.createWebRTCConnect=function(e,i){var o,s;return r(this,void 0,void 0,(function(){var r,a=this;return n(this,(function(n){switch(n.label){case 0:try{r=t({iceServers:[],sdpSemantics:"unified-plan",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"},null!==(o=e.connection)&&void 0!==o?o:{}),this.peerConnection=new RTCPeerConnection(r)}catch(e){return He.log(e),"ReferenceError"===e.name&&e.message.includes("RTCPeerConnection")&&(He.log("Not support WebRTC"),null===(s=this.onError)||void 0===s||s.call(this,"WebRTC is not supported")),[2]}return this.connectStatus=fe.CONNECTING,this.negotiating=!1,this.peerConnection.addEventListener("track",this.onTrack),this.peerConnection.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.addEventListener("icecandidate",this.onIceCandidate),this.peerConnection.addEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.addEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.addEventListener("negotiationneeded",this.onNegotiationNeeded),this.connectDirection===ve.SEND&&i instanceof MediaStream&&i.getTracks().forEach((function(e){a.peerConnection.addTrack(e,i)})),this.connectDirection!==ve.RECEIVE?[3,2]:[4,this.createOffer(e.offer)];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))},e.prototype.onTrack=function(e){var t;He.log("onTrack",e),He.log("on "+e.track.kind+" track",e.streams,e.track),null===(t=this.onAddTrack)||void 0===t||t.call(this,e.track)},e.prototype.onIceConnectionStateChange=function(e){switch(He.log("onIceConnectionStateChange",this.peerConnection.iceConnectionState,e),this.peerConnection.iceConnectionState){case"failed":case"disconnected":He.log("Connection disconnected, please try again"),this.disconnect({code:me.NEED_RECONNECT,msg:"Connection disconnected, please try again"})}},e.prototype.onIceCandidate=function(e){He.log("onIceCandidate",e)},e.prototype.onSignalingStateChange=function(e){He.log("onSignalingStateChange",this.peerConnection.signalingState,e)},e.prototype.onConnectionStateChange=function(e){var t;switch(He.log("onConnectionStateChange",this.peerConnection.connectionState,e),this.peerConnection.connectionState){case"failed":case"disconnected":He.log("Connection disconnected, please try again"),this.disconnect({code:me.NEED_RECONNECT,msg:"Connection disconnected, please try again"});break;case"connected":He.log("Connection connected"),this.connectStatus=fe.ESTABLISHED,null===(t=this.onConnect)||void 0===t||t.call(this,{code:he.SUCCESS,msg:"Connection connected"})}},e.prototype.onNegotiationNeeded=function(e){if(He.log("onNegotiationNeeded",e),this.connectDirection!==ve.RECEIVE)try{if(this.negotiating||"stable"!==this.peerConnection.signalingState)return;this.negotiating=!0,this.createOffer()}catch(e){He.log("onNegotiationNeeded error",e)}finally{this.negotiating=!1}},e.prototype.createOffer=function(e){var i;return void 0===e&&(e={}),r(this,void 0,void 0,(function(){var r,o,s,a,c,d,u;return n(this,(function(n){switch(n.label){case 0:r=e.offerToReceiveVideo,o=e.offerToReceiveAudio,s=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}(e,["offerToReceiveVideo","offerToReceiveAudio"]),a=t({voiceActivityDetection:!1},s),this.connectDirection===ve.RECEIVE&&(c=!!this.peerConnection.addTransceiver,He.log("peerConnection "+(c?"":"no")+" addTransceiver"),c?(o&&this.peerConnection.addTransceiver("audio",{direction:"recvonly"}),r&&this.peerConnection.addTransceiver("video",{direction:"recvonly"})):a=t(t({},a),{offerToReceiveVideo:r,offerToReceiveAudio:o})),n.label=1;case 1:return n.trys.push([1,4,,5]),d=this.onOffer,[4,this.peerConnection.createOffer(a)];case 2:return[4,d.apply(this,[n.sent()])];case 3:return n.sent(),[3,5];case 4:return u=n.sent(),He.log("create offer error",u),this.disconnect({code:me.NEED_RECONNECT,msg:"create offer is failed"}),null===(i=this.onError)||void 0===i||i.call(this,"create offer is failed"),[3,5];case 5:return[2]}}))}))},e.prototype.onOffer=function(e){var t,s;return r(this,void 0,void 0,(function(){var r,a,c;return n(this,(function(n){switch(n.label){case 0:He.log("onOffer->",e),r=e.sdp.split("\r\n").map((function(e){return e.includes("a=fmtp:111")?e+";stereo=1":e})),a=r.join("\r\n"),this.connectDirection===ve.RECEIVE&&(a=function(e){if(!Me)return e;var t=e.split("\r\n"),r=[],n=[];t.forEach((function(e){var t=e.toLowerCase();t.includes("a=rtpmap")&&t.includes("h264")&&r.push(e)})),r.length>1&&n.push.apply(n,o([],i(r.slice(1))));var s=n.map((function(e){var t=/a=rtpmap:(\d+)\s/.exec(e);return t&&t.length>1?t[1]:null})).filter((function(e){return null!==e})),a=[];return t.forEach((function(e){var t=e;if(e.includes("a=setup")&&(t="a=setup:passive"),(e.includes("m=audio")||e.includes("m=video"))&&(t=e.split(" ").filter((function(e,t){return t<3||!s.includes(e)})).join(" ")),e.includes("a=fmtp")||e.includes("a=rtcp-fb")||e.includes("a=rtpmap")){var r=/a=(?:fmtp|rtcp-fb|rtpmap):(\d+)\s/.exec(e);if(r&&r.length>1&&s.includes(r[1]))return}a.push(t)})),a.join("\r\n")}(a)),e.sdp=a,He.log("onOffer modify->",e),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.peerConnection.setLocalDescription(e)];case 2:return n.sent(),this.clientSideDescription=this.peerConnection.localDescription,null===(t=this.onSetLocalDescription)||void 0===t||t.call(this,this.clientSideDescription),[3,4];case 3:return c=n.sent(),He.log("setLocalDescription error",c,this.clientSideDescription),null===(s=this.onError)||void 0===s||s.call(this,"set local sdp is failed"),[3,4];case 4:return[2]}}))}))},e.prototype.onAnswer=function(e){var t;return r(this,void 0,void 0,(function(){var r;return n(this,(function(n){switch(n.label){case 0:He.log("onAnswer->",e),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.peerConnection.setRemoteDescription(new RTCSessionDescription(e))];case 2:return n.sent(),this.serverSideDescription=this.peerConnection.remoteDescription,He.log("remoteDescription",this.peerConnection.remoteDescription),[3,4];case 3:return r=n.sent(),He.log("setRemoteDescription error",r,e),null===(t=this.onError)||void 0===t||t.call(this,"set remote sdp is failed"),[3,4];case 4:return[2]}}))}))},e}(),Qe=function(){function e(){this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null,this.timeMark=new Map,this.commonData={},this.initCommonData(),this.reportData=t({},this.commonData),this.tempData={}}return e.prototype.setData=function(e,t,r){void 0===r&&(r=!1),this.reportData[e]=t,r&&(this.commonData[e]=t)},e.prototype.setTempData=function(e,t){this.tempData[e]=t},e.prototype.getTempData=function(e){return this.tempData[e]},e.prototype.startReport=function(e){var r;switch(e){case"start":this.setData("uint32_data_type",1),this.setData("uint32_command",40101);break;case"interval":this.setData("uint32_data_type",2),this.setData("uint32_command",40100);break;case"stop":this.setData("uint32_data_type",1),this.setData("uint32_command",40102)}var n=(new Date).getTime();this.setData("uint64_data_time",Math.round(n/1e3)),null===(r=this.onReport)||void 0===r||r.call(this,this.reportData),this.reportData=t({},this.commonData)},e.prototype.setHandler=function(e){this.onReport=e},e.prototype.markTime=function(e){var t=window.performance?window.performance.now():(new Date).getTime();this.timeMark.set(e,t)},e.prototype.measureTime=function(e,t,r,n){if(void 0===n&&(n=!1),this.timeMark.has(t)&&this.timeMark.has(r)){var i=Math.round(this.timeMark.get(r)-this.timeMark.get(t));this.setData(e,i),n&&this.setTempData(e,i)}},e.prototype.clearData=function(){this.timeMark?this.timeMark.clear():this.timeMark=new Map,this.commonData={},this.initCommonData(),this.reportData=t({},this.commonData),this.tempData={}},e.prototype.destroy=function(){var e;null===(e=this.timeMark)||void 0===e||e.clear(),this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null},e.prototype.initCommonData=function(){var e=window.navigator.userAgent,t=be.parse(e),r=t.platform,n=t.os,i=t.browser;this.commonData.str_device_type=r.model||r.vendor||"",this.commonData.str_os_info=((n.name||"")+" "+(n.version||"")).trim(),this.commonData.str_browser_model=i.name||"",this.commonData.str_browser_version=i.version||"",this.commonData.str_user_agent=e,this.commonData.u32_link_type=4,this.commonData.u32_channel_type=3},e}(),Ke=function(){function e(){this.playerView=null,this.playUrl=null,this.isVideoExisted=!1,this.webrtcConnection=null,this.svrSig=null,this.stream=null,this.connectRetry={maxNum:3,curNum:0,delay:1},this.timeoutId=null,this.intervalId=null,this.docOrigOverflow=null,this.listener={onPlayEvent:null,onPlayStats:null,onPlayReport:null},this.lastStatsReport=null,this.streamEmptyCount=0,this.streamDecodeFailCount=0,this.offerToReceive={video:!0,audio:!0},this.playingStatus=ye.INIT,this.report=null,this.report=new Qe,this.onAddTrack=this.onAddTrack.bind(this),this.onSetLocalDescription=this.onSetLocalDescription.bind(this),this.onConnect=this.onConnect.bind(this),this.onDisconnect=this.onDisconnect.bind(this),this.onError=this.onError.bind(this),this.onStats=this.onStats.bind(this);var e=le.browserDetails,t=e.browser,r=e.version;He.log("browser is "+t+", version is "+r)}return e.checkSupport=function(e){r(void 0,void 0,void 0,(function(){var e,t,i;return n(this,(function(o){switch(o.label){case 0:return e=!1,["RTCPeerConnection","webkitRTCPeerConnection"].forEach((function(t){e||t in window&&(e=!0)})),Pe||(ke&&Ae||De&&Ae&&(0===(t=Le()).length||t[0]<11||11===t[0]&&t[1]<1||11===t[0]&&1===t[1]&&t[2]<2))&&(e=!1),[4,r(void 0,void 0,void 0,(function(){var e,t,r,i;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),e=new RTCPeerConnection({iceServers:[],sdpSemantics:"unified-plan"}),t={},e.addTransceiver?(e.addTransceiver("audio",{direction:"recvonly"}),e.addTransceiver("video",{direction:"recvonly"})):t={offerToReceiveVideo:!0,offerToReceiveAudio:!0},[4,e.createOffer(t)];case 1:return r=n.sent(),i=r.sdp.toLowerCase().indexOf("h264")>-1,e.close(),[2,i];case 2:return n.sent(),[2,!1];case 3:return[2]}}))}))];case 1:return i=o.sent(),[2,{support:e,isTbs:Pe,tbsVersion:Pe?(s=navigator.userAgent,a=Ee,c=1,d=s.match(a),d&&d.length>=c&&parseInt(d[c],10)):null,isFirefox:we,isSafari:De,isIOS:Ae,iOSVersion:Ae?Le().join("."):null,h264Support:i}]}var s,a,c,d}))})).then((function(t){null==e||e(t)}))},e.prototype.setConfig=function(e){var t=e.connectRetryCount,r=e.connectRetryDelay,n=e.receiveVideo,i=e.receiveAudio,o=e.showLog;void 0!==t&&("number"==typeof t&&t>=0&&t<=10?this.connectRetry.maxNum=t:He.log("connectRetryCount must be a number between 0 and 10")),void 0!==r&&("number"==typeof r&&r>=0&&r<=10?this.connectRetry.delay=r:He.log("connectRetryDelay must be a number between 0 and 10")),void 0!==n&&(this.offerToReceive.video=!!n),void 0!==i&&(this.offerToReceive.audio=!!i),void 0!==o&&He.enableConsole(!!o)},e.prototype.setPlayListener=function(e){var t=e.onPlayEvent,r=e.onPlayStats,n=e.onPlayReport;void 0!==t&&("function"==typeof t?this.listener.onPlayEvent=t:He.log("onPlayEvent must be function")),void 0!==r&&("function"==typeof r?this.listener.onPlayStats=r:He.log("onPlayStats must be function")),void 0!==n&&("function"==typeof n?(this.listener.onPlayReport=n,this.report.setHandler(n)):He.log("onPlayReport must be function"))},e.prototype.setPlayerView=function(e){if(this.playerView)He.log("player view is existed");else{var t="string"==typeof e?document.getElementById(e):e;if(t&&(t instanceof HTMLDivElement||t instanceof HTMLVideoElement))if(t instanceof HTMLVideoElement)this.playerView=t;else{var r=document.createElement("video");r.autoplay=!0,r.muted=!0,r.controls=!0,r.playsInline=!0,r.setAttribute("webkit-playsinline",""),r.setAttribute("x5-playsinline",""),r.setAttribute("style",Ce),t.appendChild(r),this.playerView=r}else He.log("Require container element id or HTMLDivElement")}},e.prototype.startPlay=function(e){if(this.playerView){var t=function(e){var t=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_]+)(?:\/)([^?#]*)(?:\?*)(?:[^?#]*)/.exec(e);if(t)return t[1];var r=/^(?:https?:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_]+)(?:\/)([^?#]*)(?:\.sdp)(?:\?*)(?:[^?#]*)/.exec(e);return r?r[1]:null}(e);if(null!==t)if(this.isVideoExisted)He.log("Video is existed, please stop playing first");else if(this.offerToReceive.video||this.offerToReceive.audio){var r=(new Date).getTime();this.report.clearData(),this.report.setData("u64_timestamp",r),this.report.setTempData("pull_start_time",r),this.report.markTime("pull_start"),this.report.setData("bytes_stream_id",t,!0),this.report.setData("str_stream_url",e,!0),this.playUrl=e,this.streamEmptyCount=0,this.streamDecodeFailCount=0,this.playingStatus=ye.INIT,this.webrtcConnection||(this.webrtcConnection=new Je,this.webrtcConnection.init({onAddTrack:this.onAddTrack,onSetLocalDescription:this.onSetLocalDescription,onConnect:this.onConnect,onDisconnect:this.onDisconnect,onError:this.onError})),this.webrtcConnection.initWebRTCConnect({config:{connection:{},offer:{offerToReceiveVideo:this.offerToReceive.video,offerToReceiveAudio:this.offerToReceive.audio}}})}else He.log("Both receiveVideo and receiveAudio are false");else He.log("Play url is not correct")}else He.log("Please set player view first")},e.prototype.stopPlay=function(e){var t,i,o;return void 0===e&&(e=!0),r(this,void 0,void 0,(function(){var r;return n(this,(function(n){switch(n.label){case 0:if(!this.isVideoExisted)return He.log("Video is not existed"),[2];if(this.isVideoExisted=!1,this.timeoutId&&(window.clearTimeout(this.timeoutId),this.timeoutId=null,this.connectRetry.curNum=0),this.intervalId&&(window.clearInterval(this.intervalId),this.intervalId=null),!this.playUrl||!this.svrSig)return[3,5];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,Fe({streamurl:this.playUrl,svrsig:this.svrSig})];case 2:return n.sent(),[3,4];case 3:return r=n.sent(),He.log(r.message||""),[3,4];case 4:this.playUrl=null,this.svrSig=null,n.label=5;case 5:return[4,this.startReport("stop")];case 6:return n.sent(),this.webrtcConnection.disconnect(),e&&(null===(t=this.stream)||void 0===t||t.getTracks().forEach((function(e){e.stop()}))),this.stream=null,this.playerView&&(this.playerView.pause(),e&&(this.playerView.srcObject=null,this.playerView.load())),null===(o=(i=this.listener).onPlayEvent)||void 0===o||o.call(i,ge.PLAY_EVT_PLAY_STOP),[2]}}))}))},e.prototype.isPlaying=function(){return this.isVideoExisted&&this.isVideoPlaying()},e.prototype.pause=function(){this.playerView&&this.isVideoExisted&&this.isVideoPlaying()&&this.playerView.pause()},e.prototype.resume=function(){return r(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:if(!this.playerView||!this.isVideoExisted||this.isVideoPlaying())return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return t.sent(),[3,4];case 3:return e=t.sent(),He.log("resume failed",e),[3,4];case 4:return[2]}}))}))},e.prototype.setMute=function(e){this.playerView&&(this.playerView.muted=!!e)},e.prototype.setVolume=function(e){"number"==typeof e&&e>=0&&e<=100?this.playerView&&(this.playerView.volume=e/100):He.log("volume must be a number between 0 and 100")},e.prototype.setControls=function(e){this.playerView&&(this.playerView.controls=!!e)},e.prototype.setFullscreen=function(e){this.playerView&&(e?this.requestFullscreen():this.exitFullscreen())},e.prototype.getVideoElement=function(){return this.playerView},e.prototype.destroy=function(){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return this.timeoutId&&(window.clearTimeout(this.timeoutId),this.timeoutId=null),this.intervalId&&(window.clearInterval(this.intervalId),this.intervalId=null),this.isVideoExisted&&this.webrtcConnection?[4,this.startReport("stop")]:[3,2];case 1:e.sent(),e.label=2;case 2:return this.stream&&(this.stream.getTracks().forEach((function(e){e.stop()})),this.stream=null),this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.parentElement.removeChild(this.playerView),this.playerView=null),this.webrtcConnection&&(this.webrtcConnection.disconnect(),this.webrtcConnection=null,this.lastStatsReport=null),this.report&&(this.report.destroy(),this.report=null),[2]}}))}))},e.prototype.isVideoPlaying=function(){return!(!this.playerView||!1!==this.playerView.paused)},e.prototype.requestFullscreen=function(){var e=je;try{e.requestFullscreen?this.playerView[e.requestFullscreen]({navigationUI:"hide"}):Ye(this.playerView)?this.playerView.webkitEnterFullScreen():this.enterFullWindow()}catch(e){He.log("enter full screen failed, ",e)}},e.prototype.exitFullscreen=function(){var e,t=je;try{if(t.requestFullscreen){var r=document[t.exitFullscreen]();null===(e=null==r?void 0:r.catch)||void 0===e||e.call(r,(function(e){return He.log("exit full screen failed, ",null==e?void 0:e.message)}))}else Ye(this.playerView)?this.playerView.webkitExitFullScreen():this.exitFullWindow()}catch(e){He.log("exit full screen failed, ",e)}},e.prototype.enterFullWindow=function(){this.docOrigOverflow=document.documentElement.style.overflow,document.documentElement.style.overflow="hidden",this.playerView.setAttribute("style","position:fixed;overflow:hidden;z-index:9999;left:0;top:0;bottom:0;right:0;width:100% !important;height:100% !important;padding-top:0 !important;background-color:#000;")},e.prototype.exitFullWindow=function(){document.documentElement.style.overflow=this.docOrigOverflow,this.playerView.setAttribute("style",Ce)},e.prototype.onAddStream=function(e){var t,i,o,s;return r(this,void 0,void 0,(function(){var a,c,d,u,l=this;return n(this,(function(p){switch(p.label){case 0:if(null===(i=(t=this.listener).onPlayEvent)||void 0===i||i.call(t,ge.PLAY_EVT_STREAM_BEGIN),!this.playerView)return[2];if(a=function(){l.playerView.removeEventListener("canplay",a),l.report.markTime("video_play"),l.offerToReceive.video?(l.report.measureTime("u32_first_video_decode_time","pull_start","video_play"),l.report.measureTime("u32_first_i_frame","pull_start","video_play",!0)):(l.report.setData("u32_first_video_decode_time",0),l.report.setData("u32_first_i_frame",0),l.report.setTempData("u32_first_i_frame",0)),l.offerToReceive.audio?l.report.measureTime("u32_first_audio_render_time","pull_start","video_play"):l.report.setData("u32_first_audio_render_time",0),l.playerView.autoplay||l.startReport("start")},this.playerView.addEventListener("canplay",a),this.playerView.srcObject=e,this.playerView.load(),this.isVideoExisted=!0,!this.playerView.autoplay)return[2];p.label=1;case 1:return p.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return p.sent(),c=Le(),Oe&&c.length>0&&13===c[0]?(this.playerView.pause(),d=function(){return r(l,void 0,void 0,(function(){var e,t,r;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.playerView.play()];case 1:return n.sent(),He.log("play ok ios"),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,ge.PLAY_EVT_PLAY_BEGIN),this.startReport("start"),[3,3];case 2:return e=n.sent(),He.log("play failed",e),this.onPlayError(ge.PLAY_ERR_PLAY_FAIL,e.toString()),[3,3];case 3:return[2]}}))}))},window.setTimeout((function(){d()}),200)):(He.log("play ok"),null===(s=(o=this.listener).onPlayEvent)||void 0===s||s.call(o,ge.PLAY_EVT_PLAY_BEGIN),this.startReport("start")),[3,4];case 3:return u=p.sent(),He.log("play failed",u),this.onPlayError(ge.PLAY_ERR_PLAY_FAIL,u.toString()),[3,4];case 4:return[2]}}))}))},e.prototype.onAddTrack=function(e){"video"===e.kind?(this.report.markTime("video_down"),this.report.measureTime("u32_first_frame_down","pull_start","video_down")):"audio"===e.kind&&(this.report.markTime("audio_down"),this.report.measureTime("u32_first_audio_frame_down","pull_start","audio_down")),this.stream?this.stream.addTrack(e):(this.stream=new MediaStream,this.stream.addTrack(e),this.onAddStream(this.stream)),He.log("track length: "+this.stream.getTracks().length)},e.prototype.onSetLocalDescription=function(e){var t,i,o,s;return r(this,void 0,void 0,(function(){var a,c,d,u,l,p,f;return n(this,(function(h){switch(h.label){case 0:return h.trys.push([0,5,,6]),a=((e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e));for(;e--;){let n=63&r[e];t+=n<36?n.toString(36):n<62?(n-26).toString(36).toUpperCase():n<63?"_":"-"}return t})(),c=be.parse(window.navigator.userAgent).browser,d="TXLivePlayer_"+(c.name||"other"),null===(i=(t=this.listener).onPlayEvent)||void 0===i||i.call(t,ge.PLAY_EVT_REQUEST_PULL_BEGIN,{localSdp:e}),this.report.setData("str_session_id",a,!0),this.report.setData("bytes_token",a,!0),this.report.markTime("request_start"),u=null,this.playUrl.startsWith("webrtc")?[4,(m={streamurl:this.playUrl,sessionid:a,clientinfo:d,localsdp:e},r(void 0,void 0,void 0,(function(){var e,t,r,i,o;return n(this,(function(n){switch(n.label){case 0:return[4,Ie("https://overseas-webrtc.liveplay.myqcloud.com/webrtc/v1/pullstream",m)];case 1:if(e=n.sent(),t=e.errcode,r=e.errmsg,i=e.remotesdp,o=e.svrsig,0!==t)throw new Error("errCode:"+t+", errMsg:"+r);return[2,{remoteSdp:i,svrSig:o}]}}))})))]:[3,2];case 1:return u=h.sent(),[3,4];case 2:return[4,Ne({streamurl:this.playUrl,sessionid:a,localsdp:e})];case 3:u=h.sent(),h.label=4;case 4:return this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end"),l=u.remoteSdp,p=u.svrSig,null===(s=(o=this.listener).onPlayEvent)||void 0===s||s.call(o,ge.PLAY_EVT_REQUEST_PULL_SUCCESS,{remoteSdp:l}),this.webrtcConnection.connect(l),this.svrSig=p,[3,6];case 5:return f=h.sent(),He.log(f.message),this.webrtcConnection.disconnect(),this.onPlayError(ge.PLAY_ERR_REQUEST_PULL_FAIL,f.message),[3,6];case 6:return[2]}var m}))}))},e.prototype.onConnect=function(e){var t,r;He.log("TXLivePlayer onConnect",e),e.code===he.SUCCESS&&(this.startStat(),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,ge.PLAY_EVT_SERVER_CONNECTED))},e.prototype.onDisconnect=function(e){return r(this,void 0,void 0,(function(){var t,r=this;return n(this,(function(n){switch(n.label){case 0:if(this.lastStatsReport=null,this.streamEmptyCount=0,this.streamDecodeFailCount=0,this.playingStatus=ye.INIT,this.intervalId&&(window.clearInterval(this.intervalId),this.intervalId=null),He.log("TXLivePlayer onDisconnect",e),e.code!==me.NEED_RECONNECT)return[3,6];if(!this.playUrl||!this.svrSig)return[3,5];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,Fe({streamurl:this.playUrl,svrsig:this.svrSig})];case 2:return n.sent(),[3,4];case 3:return t=n.sent(),He.log(t.message||""),[3,4];case 4:this.svrSig=null,n.label=5;case 5:this.stream&&(this.stream.getTracks().forEach((function(e){e.stop()})),this.stream=null),this.isVideoExisted&&this.playerView&&(this.playerView.pause(),this.isVideoExisted=!1),this.timeoutId&&(window.clearTimeout(this.timeoutId),this.timeoutId=null),this.timeoutId=window.setTimeout((function(){var e,t;r.connectRetry.curNum+=1;var n=r.connectRetry,i=n.curNum,o=n.maxNum;i<=o?(He.log("current retry num: "+i+", max retry num: "+o),null===(t=(e=r.listener).onPlayEvent)||void 0===t||t.call(e,ge.PLAY_EVT_SERVER_RECONNECT),r.startPlay(r.playUrl)):(r.onPlayError(ge.PLAY_ERR_SERVER_DISCONNECT),r.connectRetry.curNum=0),r.timeoutId=null}),1e3*this.connectRetry.delay),n.label=6;case 6:return[2]}}))}))},e.prototype.onError=function(e){this.onPlayError(ge.PLAY_ERR_WEBRTC_FAIL,e)},e.prototype.startStat=function(){return r(this,void 0,void 0,(function(){var e,t,r=this;return n(this,(function(n){switch(n.label){case 0:return this.report.markTime("server_connected"),this.report.measureTime("u32_connect_server_time","pull_start","server_connected"),this.intervalId&&(window.clearInterval(this.intervalId),this.intervalId=null),[4,this.webrtcConnection.getStats()];case 1:return(e=n.sent())&&(this.report.setTempData("start_stats",e),this.report.setTempData("last_stats",e),this.report.setTempData("last_time",(new Date).getTime()),this.onStats(e)),t=0,this.intervalId=window.setInterval((function(){var e;null===(e=r.webrtcConnection)||void 0===e||e.getStats().then((function(e){e&&r.onStats(e),(t+=1)%5==0&&r.startReport("interval")}))}),1e3),[2]}}))}))},e.prototype.onStats=function(e){var t,r,n,s,a,c,d,u,l,p,f=function(e,t){if(void 0===t&&(t=null),t){var r=null,n=null,i=null,o=null;e.forEach((function(e){"track"===e.type&&("video"===e.kind||e.frameWidth?r=e.id:("audio"===e.kind||e.audioLevel)&&(i=e.id)),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(o=e.id))}));var s=e.get(r),a=e.get(n),c=t.get(r),d=t.get(n),u=0;void 0!==(null==a?void 0:a.timestamp)&&void 0!==(null==d?void 0:d.timestamp)&&(u=(a.timestamp-d.timestamp)/1e3);var l=void 0;if((null==a?void 0:a.codecId)&&(E=e.get(a.codecId))){var p=E.mimeType,f=E.payloadType,h=E.sdpFmtpLine;l=(null==p?void 0:p.replace("video/",""))||"",f&&h&&(l=l+" ("+f+", "+h+")")}var m=void 0;void 0!==(null==a?void 0:a.framesPerSecond)?m=a.framesPerSecond:void 0!==(null==a?void 0:a.framerateMean)?m=a.framerateMean:void 0!==(null==s?void 0:s.framesReceived)&&void 0!==(null==c?void 0:c.framesReceived)&&u&&(m=(s.framesReceived-c.framesReceived)/u);var v=void 0;void 0!==(null==a?void 0:a.framesDecoded)&&void 0!==(null==d?void 0:d.framesDecoded)&&u&&(v=(a.framesDecoded-d.framesDecoded)/u);var y=void 0;void 0!==(null==a?void 0:a.bytesReceived)&&void 0!==(null==d?void 0:d.bytesReceived)&&u&&(y=8*(a.bytesReceived-d.bytesReceived)/u);var g=void 0;void 0!==(null==s?void 0:s.jitterBufferDelay)&&void 0!==(null==s?void 0:s.jitterBufferEmittedCount)&&void 0!==(null==c?void 0:c.jitterBufferDelay)&&void 0!==(null==c?void 0:c.jitterBufferEmittedCount)&&(g=s.jitterBufferEmittedCount-c.jitterBufferEmittedCount?(s.jitterBufferDelay-c.jitterBufferDelay)/(s.jitterBufferEmittedCount-c.jitterBufferEmittedCount)*1e3:s.jitterBufferDelay/s.jitterBufferEmittedCount*1e3);var b=void 0;void 0!==(null==a?void 0:a.totalDecodeTime)&&void 0!==(null==s?void 0:s.framesDecoded)&&void 0!==(null==d?void 0:d.totalDecodeTime)&&void 0!==(null==c?void 0:c.framesDecoded)&&(b=s.framesDecoded-c.framesDecoded?(a.totalDecodeTime-d.totalDecodeTime)/(s.framesDecoded-c.framesDecoded)*1e3:a.totalDecodeTime/s.framesDecoded*1e3);var C=e.get(i),_=e.get(o),T=t.get(i),S=t.get(o);void 0!==(null==_?void 0:_.timestamp)&&void 0!==(null==S?void 0:S.timestamp)&&(u=(_.timestamp-S.timestamp)/1e3);var E,R=void 0;(null==_?void 0:_.codecId)&&(E=e.get(_.codecId))&&(p=E.mimeType,f=E.payloadType,h=E.sdpFmtpLine,R=(null==p?void 0:p.replace("audio/",""))||"",f&&h&&(R=R+" ("+f+", "+h+")"));var P=void 0;void 0!==(null==C?void 0:C.audioLevel)?P=null==C?void 0:C.audioLevel:void 0!==(null==_?void 0:_.audioLevel)&&(P=null==_?void 0:_.audioLevel);var w=void 0;void 0!==(null==_?void 0:_.bytesReceived)&&void 0!==(null==S?void 0:S.bytesReceived)&&u&&(w=8*(_.bytesReceived-S.bytesReceived)/u);var M=void 0;return void 0!==(null==C?void 0:C.jitterBufferDelay)&&void 0!==(null==C?void 0:C.jitterBufferEmittedCount)&&void 0!==(null==T?void 0:T.jitterBufferDelay)&&void 0!==(null==T?void 0:T.jitterBufferEmittedCount)&&(M=C.jitterBufferEmittedCount-T.jitterBufferEmittedCount?(C.jitterBufferDelay-T.jitterBufferDelay)/(C.jitterBufferEmittedCount-T.jitterBufferEmittedCount)*1e3:C.jitterBufferDelay/C.jitterBufferEmittedCount*1e3),{timestamp:(null==a?void 0:a.timestamp)||(null==_?void 0:_.timestamp),video:{codec:l,bitrate:y&&Number(y.toFixed(2)),framesPerSecond:m&&Math.round(m),framesDecodedPerSecond:v&&Math.round(v),frameWidth:null==s?void 0:s.frameWidth,frameHeight:null==s?void 0:s.frameHeight,framesDecoded:null==a?void 0:a.framesDecoded,framesDropped:null==s?void 0:s.framesDropped,framesReceived:null==s?void 0:s.framesReceived,packetsLost:null==a?void 0:a.packetsLost,packetsReceived:null==a?void 0:a.packetsReceived,nackCount:null==a?void 0:a.nackCount,firCount:null==a?void 0:a.firCount,pliCount:null==a?void 0:a.pliCount,jitterBufferDelay:g&&Number(g.toFixed(2)),frameDecodeAvgTime:b&&Number(b.toFixed(2))},audio:{codec:R,audioLevel:P,bitrate:w&&Number(w.toFixed(2)),packetsLost:null==_?void 0:_.packetsLost,packetsReceived:null==_?void 0:_.packetsReceived,jitterBufferDelay:M&&Number(M.toFixed(2))}}}}(e,this.lastStatsReport);if(this.lastStatsReport=e,f&&(f.video.frameWidth=f.video.frameWidth||(null===(t=this.playerView)||void 0===t?void 0:t.videoWidth)||void 0,f.video.frameHeight=f.video.frameHeight||(null===(r=this.playerView)||void 0===r?void 0:r.videoHeight)||void 0,null===(s=(n=this.listener).onPlayStats)||void 0===s||s.call(n,f)),(null==f?void 0:f.video)&&this.offerToReceive.video){var h=f.video,m=h.bitrate,v=h.framesPerSecond,y=h.framesDecoded,g=h.framesDecodedPerSecond;if(m&&v&&(this.connectRetry.curNum=0),!this.isVideoExisted)return;if(m&&v?this.streamEmptyCount=0:(this.streamEmptyCount+=1,this.streamEmptyCount>=5&&(null===(c=(a=this.listener).onPlayEvent)||void 0===c||c.call(a,ge.PLAY_EVT_STREAM_EMPTY),this.streamEmptyCount=0)),m&&v&&!y?(this.streamDecodeFailCount+=1,3===this.streamDecodeFailCount&&this.onPlayError(ge.PLAY_ERR_DECODE_FAIL)):this.streamDecodeFailCount=0,g>=0||v>=0)if(g<=5||v<=5){var b=this.report.getTempData("freeze_interval_count")||0;this.report.setTempData("freeze_interval_count",b+1),this.playingStatus!==ye.WAITING&&(this.playingStatus=ye.WAITING,this.report.setTempData("freeze_start_time",(new Date).getTime()),null===(u=(d=this.listener).onPlayEvent)||void 0===u||u.call(d,ge.PLAY_EVT_PLAY_WAITING_BEGIN))}else{if(this.playingStatus===ye.WAITING){var C=this.report.getTempData("freeze_start_time"),_=(new Date).getTime(),T=_-C,S=this.report.getTempData("video_freeze")||{},E=S.count,R=void 0===E?0:E,P=S.totalTime,w=void 0===P?0:P,M=S.maxTime,k=void 0===M?0:M,D=S.endTimes,A=void 0===D?[]:D;R+=1,w+=T,k=Math.max(T,k),A=o(o([],i(A)),[_]),this.report.setTempData("video_freeze",{count:R,totalTime:w,maxTime:k,endTimes:A}),null===(p=(l=this.listener).onPlayEvent)||void 0===p||p.call(l,ge.PLAY_EVT_PLAY_WAITING_STOP)}this.playingStatus=ye.PLAYING}}},e.prototype.startReport=function(e){var t,i;return r(this,void 0,void 0,(function(){var r,o,s,a,c,d,u,l,p,f,h,m,v,y,g,b,C,_,T,S,E,R,P,w,M,k,D,A,O,L,x,I,F,N;return n(this,(function(n){switch(n.label){case 0:return r=(new Date).getTime(),o=this.report.getTempData("pull_start_time"),"stop"===e?(s=this.report.getTempData("u32_first_i_frame")||0,this.report.setData("u64_timestamp",r),this.report.setData("u64_begin_timestamp",o),this.report.setData("u32_result",Math.round((r-o)/1e3)),this.report.setData("u32_first_i_frame",s),this.report.setData("u32_first_frame_black",s>1e4?1:0),a=this.report.getTempData("video_freeze")||{},c=a.count,v=void 0===c?0:c,d=a.totalTime,u=void 0===d?0:d,l=a.maxTime,p=void 0===l?0:l,this.report.setData("u64_block_count",v),this.report.setData("u32_video_block_time",u),this.report.setData("u64_block_duration_max",p),this.report.setData("u32_avg_block_time",u&&v?Math.round(u/v):0)):"interval"===e&&(this.report.setData("u64_timestamp",r),this.report.setData("u64_playtime",r-o),f=(this.report.getTempData("video_freeze")||{}).endTimes,h=void 0===f?[]:f,m=this.report.getTempData("last_time")||0,v=h.filter((function(e){return e>=m&&e<r})).length,this.report.setData("u32_video_block_count",v),this.report.setTempData("last_time",r),y=this.report.getTempData("freeze_interval_count")||0,this.report.setData("u32_block_usage",y/5*1e3),this.report.setTempData("freeze_interval_count",0)),[4,this.webrtcConnection.getStats()];case 1:return(g=n.sent())&&(b=function(e){var t=null;e.forEach((function(r){var n;"candidate-pair"===r.type&&r.selected?t=r.remoteCandidateId:"transport"===r.type&&r.selectedCandidatePairId&&(t=null===(n=e.get(r.selectedCandidatePairId))||void 0===n?void 0:n.remoteCandidateId)}));var r=e.get(t)||{},n=r.address,i=void 0===n?"":n,o=r.port;return{address:i,port:void 0===o?"":o}}(g),C=b.address,_=b.port,this.report.setData("u32_server_ip",C+(_?":":"")+_),"stop"===e?(T=this.report.getTempData("start_stats"),S=function(e,t){var r=null,n=null,i=null;e.forEach((function(e){"track"!==e.type||"video"!==e.kind&&!e.frameWidth||(r=e.id),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(i=e.id))}));var o=e.get(r),s=e.get(n),a=e.get(i),c=null==t?void 0:t.get(n),d=0;void 0!==(null==s?void 0:s.timestamp)&&void 0!==(null==c?void 0:c.timestamp)&&(d=(s.timestamp-c.timestamp)/1e3);var u=0;void 0!==(null==s?void 0:s.packetsLost)&&void 0!==(null==s?void 0:s.packetsReceived)&&(u=s.packetsLost/(s.packetsLost+s.packetsReceived)*1e3);var l=0;void 0!==(null==a?void 0:a.packetsLost)&&void 0!==(null==a?void 0:a.packetsReceived)&&(l=a.packetsLost/(a.packetsLost+a.packetsReceived)*1e3);var p=0;return void 0!==(null==s?void 0:s.framesDecoded)&&void 0!==(null==c?void 0:c.framesDecoded)&&d&&(p=Math.round((s.framesDecoded-c.framesDecoded)/d)),{width:(null==o?void 0:o.frameWidth)||0,height:(null==o?void 0:o.frameHeight)||0,videoFractionLost:Number(u.toFixed(2)),audioFractionLost:Number(l.toFixed(2)),framerateMean:p}}(g,T),E=S.width,R=S.height,F=S.videoFractionLost,A=S.audioFractionLost,P=S.framerateMean,this.report.setData("u32_video_width",E||(null===(t=this.playerView)||void 0===t?void 0:t.videoWidth)),this.report.setData("u32_video_height",R||(null===(i=this.playerView)||void 0===i?void 0:i.videoHeight)),this.report.setData("u32_video_drop_usage",F),this.report.setData("u32_audio_drop_usage",A),this.report.setData("u32_video_avg_fps",P)):"interval"===e&&(w=this.report.getTempData("last_stats"),M=function(e,t){var r=null,n=null,i=null,o=null;e.forEach((function(e){"track"===e.type&&("video"===e.kind||e.frameWidth)&&(r=e.id),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(i=e.id)),"candidate-pair"===e.type&&e.selected?o=e.id:"transport"===e.type&&e.selectedCandidatePairId&&(o=e.selectedCandidatePairId)}));var s=e.get(r),a=e.get(n),c=null==t?void 0:t.get(r),d=null==t?void 0:t.get(n),u=0;void 0!==(null==a?void 0:a.timestamp)&&void 0!==(null==d?void 0:d.timestamp)&&(u=(a.timestamp-d.timestamp)/1e3);var l=0;void 0!==(null==a?void 0:a.packetsLost)&&void 0!==(null==d?void 0:d.packetsLost)&&(l=a.packetsLost-d.packetsLost);var p=0;void 0!==(null==s?void 0:s.framesReceived)&&void 0!==(null==c?void 0:c.framesReceived)&&u?p=(s.framesReceived-c.framesReceived)/u:void 0!==(null==a?void 0:a.framerateMean)&&void 0!==(null==d?void 0:d.framerateMean)&&(p=(a.framerateMean+d.framerateMean)/2);var f=0;void 0!==(null==a?void 0:a.framesDecoded)&&void 0!==(null==d?void 0:d.framesDecoded)&&u&&(f=(a.framesDecoded-d.framesDecoded)/u);var h=0;void 0!==(null==a?void 0:a.bytesReceived)&&void 0!==(null==d?void 0:d.bytesReceived)&&u&&(h=8*(a.bytesReceived-d.bytesReceived)/u/1024);var m=0;void 0!==(null==a?void 0:a.packetsLost)&&void 0!==(null==a?void 0:a.packetsReceived)&&void 0!==(null==d?void 0:d.packetsLost)&&void 0!==(null==d?void 0:d.packetsReceived)&&(m=(C=a.packetsLost-d.packetsLost)/(C+(a.packetsReceived-d.packetsReceived))*1e3);var v=e.get(i),y=null==t?void 0:t.get(i);void 0!==(null==v?void 0:v.timestamp)&&void 0!==(null==y?void 0:y.timestamp)&&(u=(v.timestamp-y.timestamp)/1e3);var g=0;void 0!==(null==v?void 0:v.packetsLost)&&void 0!==(null==y?void 0:y.packetsLost)&&(g=v.packetsLost-y.packetsLost);var b=0;void 0!==(null==v?void 0:v.bytesReceived)&&void 0!==(null==y?void 0:y.bytesReceived)&&u&&(b=8*(v.bytesReceived-y.bytesReceived)/u/1024);var C,_=0;void 0!==(null==v?void 0:v.packetsLost)&&void 0!==(null==v?void 0:v.packetsReceived)&&void 0!==(null==y?void 0:y.packetsLost)&&void 0!==(null==y?void 0:y.packetsReceived)&&(_=(C=v.packetsLost-y.packetsLost)/(C+(v.packetsReceived-y.packetsReceived))*1e3);var T=e.get(o),S=null==t?void 0:t.get(o),E=0;return void 0!==(null==T?void 0:T.totalRoundTripTime)&&void 0!==(null==T?void 0:T.responsesReceived)&&void 0!==(null==S?void 0:S.totalRoundTripTime)&&void 0!==(null==S?void 0:S.responsesReceived)&&(E=(T.totalRoundTripTime-S.totalRoundTripTime)/(T.responsesReceived-S.responsesReceived)*1e3),{videoPacketsLost:Math.max(l,0),videoReceiveFps:Math.round(p),videoDecodeFps:Math.round(f),videoBitrate:Number(h.toFixed(2)),videoFractionLost:Math.max(Number(m.toFixed(2)),0),audioPacketsLost:Math.max(g,0),audioBitrate:Number(b.toFixed(2)),audioFractionLost:Math.max(Number(_.toFixed(2)),0),roundTripTime:Math.round(E)}}(g,w),k=M.audioPacketsLost,D=M.audioBitrate,A=M.audioFractionLost,O=M.videoReceiveFps,L=M.videoDecodeFps,x=M.videoBitrate,I=M.videoPacketsLost,F=M.videoFractionLost,N=M.roundTripTime,this.report.setData("u32_audio_drop",k),this.report.setData("u32_audio_drop_usage",A),this.report.setData("u32_avg_audio_bitrate",D),this.report.setData("u32_video_drop",I),this.report.setData("u32_video_drop_usage",F),this.report.setData("u32_video_recv_fps",O),this.report.setData("u32_fps",L),this.report.setData("u32_avg_video_bitrate",x),this.report.setData("u32_avg_net_speed",Number((x+D).toFixed(2))),this.report.setData("u64_rtt",N),this.report.setTempData("last_stats",g))),this.report.startReport(e),[2]}}))}))},e.prototype.onPlayError=function(e,t){var i,o;return r(this,void 0,void 0,(function(){var r;return n(this,(function(n){return r=t?{message:t}:void 0,null===(o=(i=this.listener).onPlayEvent)||void 0===o||o.call(i,e,r),this.report.setData("u64_err_code",e),this.report.setData("str_err_info",t||""),e===ge.PLAY_ERR_SERVER_DISCONNECT?this.startReport("stop"):this.startReport("start"),[2]}))}))},e}();return Ke}));
