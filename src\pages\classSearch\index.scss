.classSearchWrapper{
  .searchBar{
    padding: 30px 0;
    .result{
      margin-bottom: 20px;
      font-size: 14px;
      em{
        color: var(--color-error);
        font-style: normal;
      }
      .searchKey{
        border: 1px solid var(--color-error);
        border-radius: 14px;
        font-size: 14px;
        padding: 4px 14px;
        margin-right: 20px;
        .close{
          color: var(--color-error);
          font-style: normal;
          position: relative;
          right: -4px;
          top: 1px;
          font-size: 18px;
          cursor: pointer;
        }
      }
    }
    .title{
      font-size: 20px;
      font-weight: 600;
    }
  }
  .searchContain{
    padding: 30px 0;
    .pageAction{
      line-height: 36px;
      cursor: pointer;
      img{
        width: 36px;
        height: 36px;
      }
      span{
        display: inline-block;
        width: 30px;
        text-align: center;
        color: var(--color-font3);
        font-size: 12px;
        em{
          color: var(--color-main);
          font-style: normal;
        }
      }
      .iconTurn{
        transform: rotate(180deg);
      }
    }
  }
  .content{
    display: flex;
    justify-content: flex-start;
    .items{
      margin-right: 20px;
      width: calc(25% - 15px);
    }
    .items:nth-child(4n){
      margin-right: 0;
    }
  }
  .noData{
    line-height: 100px;
    text-align: center;
  }
}