!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("hls.js"),require("dashjs"),require("flv.js"),require("TXLivePlayer")):"function"==typeof define&&define.amd?define(["hls.js","dashjs","flv.js","TXLivePlayer"],e):t.TCPlayer=e(t.Hls,t.dashjs,t.flvjs,t.TXLivePlayer)}(this,function(Hls$1,dashjs,flvjs,TXLivePlayer){function unwrapExports(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t["default"]:t}function createCommonjsModule(t,e){return e={exports:{}},t(e,e.exports),e.exports}function each(t,e){keys(t).forEach(function(i){return e(t[i],i)})}function reduce(t,e){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;return keys(t).reduce(function(i,n){return e(i,t[n],n)},i)}function assign(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return Object.assign?Object.assign.apply(Object,[t].concat(i)):(i.forEach(function(e){e&&each(e,function(e,i){t[i]=e})}),t)}function isObject$1(t){return!!t&&"object"===(void 0===t?"undefined":_typeof(t))}function isPlain(t){return isObject$1(t)&&"[object Object]"===toString.call(t)&&t.constructor===Object}function isFunction(t){return"function"==typeof t&&"number"!=typeof t.nodeType}function clean(t){return t.replace(/\n\r?\s*/g,"")}function computedStyle(t,e){if(!t||!e)return"";if("function"==typeof window_1.getComputedStyle){var i=window_1.getComputedStyle(t);return i?i[e]:""}return t.currentStyle[e]||""}function isNonBlankString(t){return"string"==typeof t&&/\S/.test(t)}function throwIfWhitespace(t){if(/\s/.test(t))throw new Error("class has illegal whitespace characters")}function classRegExp(t){return new RegExp("(^|\\s)"+t+"($|\\s)")}function isReal(){return document_1===window_1.document&&"undefined"!=typeof document_1.createElement}function isEl(t){return isObject$1(t)&&1===t.nodeType}function isInFrame(){try{return window_1.parent!==window_1.self}catch(t){return!0}}function createQuerier(t){return function(e,i){if(!isNonBlankString(e))return document_1[t](null);isNonBlankString(i)&&(i=document_1.querySelector(i));var n=isEl(i)?i:document_1;return n[t]&&n[t](e)}}function createEl(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"div",e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{},n=arguments[3],r=document_1.createElement(t);return Object.getOwnPropertyNames(e).forEach(function(t){var i=e[t];-1!==t.indexOf("aria-")||"role"===t||"type"===t?(log$2.warn(tsml(_templateObject,t,i)),r.setAttribute(t,i)):"textContent"===t?textContent(r,i):r[t]=i}),Object.getOwnPropertyNames(i).forEach(function(t){r.setAttribute(t,i[t])}),n&&appendContent(r,n),r}function textContent(t,e){return"undefined"==typeof t.textContent?t.innerText=e:t.textContent=e,t}function prependTo(t,e){e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t)}function hasClass(t,e){return throwIfWhitespace(e),t.classList?t.classList.contains(e):classRegExp(e).test(t.className)}function addClass(t,e){return t.classList?t.classList.add(e):hasClass(t,e)||(t.className=(t.className+" "+e).trim()),t}function removeClass(t,e){return t.classList?t.classList.remove(e):(throwIfWhitespace(e),t.className=t.className.split(/\s+/).filter(function(t){return t!==e}).join(" ")),t}function toggleClass(t,e,i){var n=hasClass(t,e);if("function"==typeof i&&(i=i(t,e)),"boolean"!=typeof i&&(i=!n),i!==n)return i?addClass(t,e):removeClass(t,e),t}function setAttributes(t,e){Object.getOwnPropertyNames(e).forEach(function(i){var n=e[i];null===n||void 0===n||!1===n?t.removeAttribute(i):t.setAttribute(i,!0===n?"":n)})}function getAttributes(t){var e={};if(t&&t.attributes&&t.attributes.length>0)for(var i=t.attributes,n=i.length-1;n>=0;n--){var r=i[n].name,o=i[n].value;"boolean"!=typeof t[r]&&-1===",autoplay,controls,playsinline,loop,muted,default,defaultMuted,".indexOf(","+r+",")||(o=null!==o),e[r]=o}return e}function getAttribute(t,e){return t.getAttribute(e)}function setAttribute(t,e,i){t.setAttribute(e,i)}function removeAttribute(t,e){t.removeAttribute(e)}function blockTextSelection(){document_1.body.focus(),document_1.onselectstart=function(){return!1}}function unblockTextSelection(){document_1.onselectstart=function(){return!0}}function getBoundingClientRect(t){if(t&&t.getBoundingClientRect&&t.parentNode){var e=t.getBoundingClientRect(),i={};return["bottom","height","left","right","top","width"].forEach(function(t){e[t]!==undefined&&(i[t]=e[t])}),i.height||(i.height=parseFloat(computedStyle(t,"height"))),i.width||(i.width=parseFloat(computedStyle(t,"width"))),i}}function findPosition(t){var e=void 0;if(t.getBoundingClientRect&&t.parentNode&&(e=t.getBoundingClientRect()),!e)return{left:0,top:0};var i=document_1.documentElement,n=document_1.body,r=i.clientLeft||n.clientLeft||0,o=window_1.pageXOffset||n.scrollLeft,s=e.left+o-r,a=i.clientTop||n.clientTop||0,l=window_1.pageYOffset||n.scrollTop,u=e.top+l-a;return{left:Math.round(s),top:Math.round(u)}}function getPointerPosition(t,e){var i={},n=findPosition(t),r=t.offsetWidth,o=t.offsetHeight,s=n.top,a=n.left,l=e.pageY,u=e.pageX;return e.changedTouches&&(u=e.changedTouches[0].pageX,l=e.changedTouches[0].pageY),i.y=Math.max(0,Math.min(1,(s-l+o)/o)),i.x=Math.max(0,Math.min(1,(u-a)/r)),i}function isTextNode(t){return isObject$1(t)&&3===t.nodeType}function emptyEl(t){for(;t.firstChild;)t.removeChild(t.firstChild);return t}function normalizeContent(t){return"function"==typeof t&&(t=t()),(Array.isArray(t)?t:[t]).map(function(t){return"function"==typeof t&&(t=t()),isEl(t)||isTextNode(t)?t:"string"==typeof t&&/\S/.test(t)?document_1.createTextNode(t):void 0}).filter(function(t){return t})}function appendContent(t,e){return normalizeContent(e).forEach(function(e){return t.appendChild(e)}),t}function insertContent(t,e){return appendContent(emptyEl(t),e)}function isSingleLeftClick(t){return t.button===undefined&&t.buttons===undefined||(0===t.button&&t.buttons===undefined||(9===IE_VERSION||0===t.button&&1===t.buttons))}function newGUID(){return _guid++}function getData(t){var e=t[elIdAttr];return e||(e=t[elIdAttr]=newGUID()),elData[e]||(elData[e]={}),elData[e]}function hasData(t){var e=t[elIdAttr];return!!e&&!!Object.getOwnPropertyNames(elData[e]).length}function removeData(t){var e=t[elIdAttr];if(e){delete elData[e];try{delete t[elIdAttr]}catch(i){t.removeAttribute?t.removeAttribute(elIdAttr):t[elIdAttr]=null}}}function _cleanUpEvents(t,e){var i=getData(t);0===i.handlers[e].length&&(delete i.handlers[e],t.removeEventListener?t.removeEventListener(e,i.dispatcher,!1):t.detachEvent&&t.detachEvent("on"+e,i.dispatcher)),Object.getOwnPropertyNames(i.handlers).length<=0&&(delete i.handlers,delete i.dispatcher,delete i.disabled),0===Object.getOwnPropertyNames(i).length&&removeData(t)}function _handleMultipleEvents(t,e,i,n){i.forEach(function(i){t(e,i,n)})}function fixEvent(t){function e(){return!0}function i(){return!1}if(!t||!t.isPropagationStopped){var n=t||window_1.event;t={};for(var r in n)"layerX"!==r&&"layerY"!==r&&"keyLocation"!==r&&"webkitMovementX"!==r&&"webkitMovementY"!==r&&("returnValue"===r&&n.preventDefault||(t[r]=n[r]));if(t.target||(t.target=t.srcElement||document_1),t.relatedTarget||(t.relatedTarget=t.fromElement===t.target?t.toElement:t.fromElement),t.preventDefault=function(){n.preventDefault&&n.preventDefault(),t.returnValue=!1,n.returnValue=!1,t.defaultPrevented=!0},t.defaultPrevented=!1,t.stopPropagation=function(){n.stopPropagation&&n.stopPropagation(),t.cancelBubble=!0,n.cancelBubble=!0,t.isPropagationStopped=e},t.isPropagationStopped=i,t.stopImmediatePropagation=function(){n.stopImmediatePropagation&&n.stopImmediatePropagation(),t.isImmediatePropagationStopped=e,t.stopPropagation()},t.isImmediatePropagationStopped=i,null!==t.clientX&&t.clientX!==undefined){var o=document_1.documentElement,s=document_1.body;t.pageX=t.clientX+(o&&o.scrollLeft||s&&s.scrollLeft||0)-(o&&o.clientLeft||s&&s.clientLeft||0),t.pageY=t.clientY+(o&&o.scrollTop||s&&s.scrollTop||0)-(o&&o.clientTop||s&&s.clientTop||0)}t.which=t.charCode||t.keyCode,null!==t.button&&t.button!==undefined&&(t.button=1&t.button?0:4&t.button?1:2&t.button?2:0)}return t}function on(t,e,i){if(Array.isArray(e))return _handleMultipleEvents(on,t,e,i);var n=getData(t);if(n.handlers||(n.handlers={}),n.handlers[e]||(n.handlers[e]=[]),i.guid||(i.guid=newGUID()),n.handlers[e].push(i),n.dispatcher||(n.disabled=!1,n.dispatcher=function(e,i){if(!n.disabled){e=fixEvent(e);var r=n.handlers[e.type];if(r)for(var o=r.slice(0),s=0,a=o.length;s<a&&!e.isImmediatePropagationStopped();s++)try{o[s].call(t,e,i)}catch(l){log$2.error(l)}}}),1===n.handlers[e].length)if(t.addEventListener){var r=!1;_supportsPassive&&passiveEvents.indexOf(e)>-1&&(r={passive:!0}),t.addEventListener(e,n.dispatcher,r)}else t.attachEvent&&t.attachEvent("on"+e,n.dispatcher)}function off(t,e,i){if(hasData(t)){var n=getData(t);if(n.handlers){if(Array.isArray(e))return _handleMultipleEvents(off,t,e,i);var r=function(t,e){n.handlers[e]=[],_cleanUpEvents(t,e)};if(e!==undefined){var o=n.handlers[e];if(o){if(!i)return void r(t,e);if(i.guid)for(var s=0;s<o.length;s++)o[s].guid===i.guid&&o.splice(s--,1);_cleanUpEvents(t,e)}}else for(var a in n.handlers)Object.prototype.hasOwnProperty.call(n.handlers||{},a)&&r(t,a)}}}function trigger(t,e,i){var n=hasData(t)?getData(t):{},r=t.parentNode||t.ownerDocument;if("string"==typeof e&&(e={type:e,target:t}),e=fixEvent(e),n.dispatcher&&n.dispatcher.call(t,e,i),r&&!e.isPropagationStopped()&&!0===e.bubbles)trigger.call(null,r,e,i);else if(!r&&!e.defaultPrevented){var o=getData(e.target);e.target[e.type]&&(o.disabled=!0,"function"==typeof e.target[e.type]&&e.target[e.type](),o.disabled=!1)}return!e.defaultPrevented}function one(t,e,i){if(Array.isArray(e))return _handleMultipleEvents(one,t,e,i);var n=function r(){off(t,e,r),i.apply(this,arguments)};n.guid=i.guid=i.guid||newGUID(),on(t,e,n)}function autoSetupTimeout(t,e){e&&(videojs$2=e),window_1.setTimeout(autoSetup,t)}function evented(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},i=e.eventBusKey;if(i){if(!t[i].nodeName)throw new Error('The eventBusKey "'+i+'" does not refer to an element.');t.eventBusEl_=t[i]}else t.eventBusEl_=createEl("span",{className:"vjs-event-bus"});return assign(t,EventedMixin),t.on("dispose",function(){t.off(),window_1.setTimeout(function(){t.eventBusEl_=null},0)}),t}function stateful(t,e){return assign(t,StatefulMixin),t.state=assign({},t.state,e),"function"==typeof t.handleStateChanged&&isEvented(t)&&t.on("statechanged",t.handleStateChanged),t}function toTitleCase(t){return"string"!=typeof t?t:t.charAt(0).toUpperCase()+t.slice(1)}function titleCaseEquals(t,e){return toTitleCase(t)===toTitleCase(e)}function mergeOptions(){for(var t={},e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];return i.forEach(function(e){e&&each(e,function(e,i){if(!isPlain(e))return void(t[i]=e);isPlain(t[i])||(t[i]={}),t[i]=mergeOptions(t[i],e)})}),t}function extend(){var t,e,i,n,r,o,s=arguments[0]||{},a=1,l=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[a]||{},a++),"object"===(void 0===s?"undefined":_typeof(s))||isFunction(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)i=s[e],n=t[e],s!==n&&(u&&n&&(isPlain(n)||(r=Array.isArray(n)))?(r?(r=!1,o=i&&Array.isArray(i)?i:[]):o=i&&isPlain(i)?i:{},s[e]=extend(u,o,n)):n!==undefined&&(s[e]=n));return s}function getParams(t,e){var i=e.split("?")[1];if(i){var n=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),r=i.match(n);return null!=r?decodeURIComponent(r[2]):null}}function guid(){function t(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}function unifyProtocol(t){t=t.replace(/^(http|https):/,"");var e=window.location.protocol;return"http:"!=e&&"https:"!=e&&(e="https:"),t=e+t}function replaceHost(t,e){var i=/^http(s)?:\/\/(.*?)\//,n=t.match(i);return t.replace(n[2],e)}function getViewportSize(){var t=document,e=t.documentElement,i=t.body;return{width:e&&e.clientWidth||i&&i.offsetWidth||window.innerWidth||0,height:e&&e.clientHeight||i&&i.offsetHeight||window.innerHeight||0}}function getTimeStamp(){return(new Date).getTime()}function getTimeStampBySecond(){return Date.parse(new Date)/1e3}function serializeParams(t){var e=[],i=function(t,i){i="function"==typeof i?i():i,i=null===i?"":i===undefined?"":i,e[e.length]=encodeURIComponent(t)+"="+encodeURIComponent(i)};return function n(t,r){var o=void 0,s=void 0,a=void 0;if(t)if(Array.isArray(r))for(o=0,s=r.length;o<s;o++)n(t+"["+("object"===_typeof(r[o])&&r[o]?o:"")+"]",r[o]);else if("[object Object]"===String(r))for(a in r)n(t+"["+a+"]",r[a]);else i(t,r);else if(Array.isArray(r))for(o=0,s=r.length;o<s;o++)i(r[o].name,r[o].value);else for(a in r)n(a,r[a]);return e}("",t).join("&")}function encodeHTML(t){return t.length?String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\"/g,"&quot;").replace(/\'/g,"&#39;").replace(/\//g,"&#x2F;"):""}function rangeCheck(t,e,i){if("number"!=typeof e||e<0||e>i)throw new Error("Failed to execute '"+t+"' on 'TimeRanges': The index provided ("+e+") is non-numeric or out of bounds (0-"+i+").")}function getRange(t,e,i,n){return rangeCheck(t,n,i.length-1),i[n][e]}function createTimeRangesObj(t){return t===undefined||0===t.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:t.length,start:getRange.bind(null,"start",0,t),end:getRange.bind(null,"end",1,t)}}function createTimeRanges(t,e){return Array.isArray(t)?createTimeRangesObj(t):t===undefined||e===undefined?createTimeRangesObj():createTimeRangesObj([[t,e]])}function bufferedPercent(t,e){var i=0,n=void 0,r=void 0;if(!e)return 0;t&&t.length||(t=createTimeRanges(0,0));for(var o=0;o<t.length;o++)n=t.start(o),r=t.end(o),r>e&&(r=e),i+=r-n;return i/e}function MediaError(t){if(t instanceof MediaError)return t;"number"==typeof t?this.code=t:"string"==typeof t?this.message=t:isObject$1(t)&&("number"==typeof t.code&&(this.code=t.code),assign(this,t)),this.message||(this.message=MediaError.defaultMessages[this.code]||"")}function SafeParseTuple(t,e){var i,n=null;try{i=JSON.parse(t,e)}catch(r){n=r}return[n,i]}function isPromise(t){return t!==undefined&&"function"==typeof t.then}function silencePromise(t){isPromise(t)&&t.then(null,function(t){})}function isFunction$1(t){if(!t)return!1;var e=toString$1.call(t);return"[object Function]"===e||"function"==typeof t&&"[object RegExp]"!==e||"undefined"!=typeof window&&(t===window.setTimeout||t===window.alert||t===window.confirm||t===window.prompt)}function extend$1(){for(var t={},e=0;e<arguments.length;e++){var i=arguments[e];for(var n in i)hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}function forEachArray(t,e){for(var i=0;i<t.length;i++)e(t[i])}function isEmpty(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}function initParams(t,e,i){var n=t;return isFunction_1(e)?(i=e,"string"==typeof t&&(n={uri:t})):n=immutable(e,{uri:t}),n.callback=i,n}function createXHR(t,e,i){return e=initParams(t,e,i),_createXHR(e)}function _createXHR(t){function e(){4===a.readyState&&setTimeout(r,0)}function i(){var t=undefined;if(t=a.response?a.response:a.responseText||getXml(a),y)try{t=JSON.parse(t)}catch(e){}return t}function n(t){return clearTimeout(c),t instanceof Error||(t=new Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,s(t,m)}function r(){if(!u){var e;clearTimeout(c),e=t.useXDR&&a.status===undefined?200:1223===a.status?204:a.status;var n=m,r=null;return 0!==e?(n={body:i(),statusCode:e,method:p,headers:{},url:h,rawRequest:a},a.getAllResponseHeaders&&(n.headers=parseHeaders(a.getAllResponseHeaders()))):r=new Error("Internal XMLHttpRequest Error"),s(r,n,n.body)}}if("undefined"==typeof t.callback)throw new Error("callback argument missing");var o=!1,s=function(e,i,n){o||(o=!0,t.callback(e,i,n))},a=t.xhr||null;a||(a=t.cors||t.useXDR?new createXHR.XDomainRequest:new createXHR.XMLHttpRequest);var l,u,c,h=a.url=t.uri||t.url,p=a.method=t.method||"GET",d=t.body||t.data,f=a.headers=t.headers||{},g=!!t.sync,y=!1,m={body:undefined,headers:{},statusCode:0,method:p,url:h,rawRequest:a};if("json"in t&&!1!==t.json&&(y=!0,f.accept||f.Accept||(f.Accept="application/json"),"GET"!==p&&"HEAD"!==p&&(f["content-type"]||f["Content-Type"]||(f["Content-Type"]="application/json"),d=JSON.stringify(!0===t.json?d:t.json))),a.onreadystatechange=e,a.onload=r,a.onerror=n,a.onprogress=function(){},a.onabort=function(){u=!0},a.ontimeout=n,a.open(p,h,!g,t.username,t.password),g||(a.withCredentials=!!t.withCredentials),!g&&t.timeout>0&&(c=setTimeout(function(){if(!u){u=!0,a.abort("timeout");var t=new Error("XMLHttpRequest timeout");t.code="ETIMEDOUT",n(t)}},t.timeout)),a.setRequestHeader)for(l in f)f.hasOwnProperty(l)&&a.setRequestHeader(l,f[l]);else if(t.headers&&!isEmpty(t.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in t&&(a.responseType=t.responseType),"beforeSend"in t&&"function"==typeof t.beforeSend&&t.beforeSend(a),a.send(d||null),a}function getXml(t){if("document"===t.responseType)return t.responseXML;var e=t.responseXML&&"parsererror"===t.responseXML.documentElement.nodeName;return""!==t.responseType||e?null:t.responseXML}function noop(){}function ParsingError(t,e){this.name="ParsingError",this.code=t.code,this.message=e||t.message}function parseTimeStamp(t){function e(t,e,i,n){return 3600*(0|t)+60*(0|e)+(0|i)+(0|n)/1e3}var i=t.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return i?i[3]?e(i[1],i[2],i[3].replace(":",""),i[4]):i[1]>59?e(i[1],i[2],0,i[4]):e(0,i[1],i[2],i[4]):null}function Settings(){this.values=_objCreate(null)}function parseOptions(t,e,i,n){var r=n?t.split(n):[t];for(var o in r)if("string"==typeof r[o]){var s=r[o].split(i);if(2===s.length){var a=s[0],l=s[1];e(a,l)}}}function parseCue(t,e,i){function n(){var e=parseTimeStamp(t);if(null===e)throw new ParsingError(ParsingError.Errors.BadTimeStamp,"Malformed timestamp: "+o);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function r(){t=t.replace(/^\s+/,"")}var o=t;if(r(),e.startTime=n(),r(),"--\x3e"!==t.substr(0,3))throw new ParsingError(ParsingError.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+o);t=t.substr(3),r(),e.endTime=n(),r(),function(t,e){var n=new Settings;parseOptions(t,function(t,e){switch(t){case"region":for(var r=i.length-1;r>=0;r--)if(i[r].id===e){n.set(t,i[r].region);break}break;case"vertical":n.alt(t,e,["rl","lr"]);break;case"line":var o=e.split(","),s=o[0];n.integer(t,s),n.percent(t,s)&&n.set("snapToLines",!1),n.alt(t,s,["auto"]),2===o.length&&n.alt("lineAlign",o[1],["start","middle","end"]);break;case"position":o=e.split(","),n.percent(t,o[0]),2===o.length&&n.alt("positionAlign",o[1],["start","middle","end"]);break;case"size":n.percent(t,e);break;case"align":n.alt(t,e,["start","middle","end","left","right"])}},/:/,/\s/),e.region=n.get("region",null),e.vertical=n.get("vertical",""),e.line=n.get("line","auto"),e.lineAlign=n.get("lineAlign","start"),e.snapToLines=n.get("snapToLines",!0),e.size=n.get("size",100),e.align=n.get("align","middle"),e.position=n.get("position",{start:0,left:0,middle:50,end:100,right:100},e.align),e.positionAlign=n.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},e.align)}(t,e)}function parseContent(t,e){function i(t){return ESCAPE[t]}for(var n,r=t.document.createElement("div"),o=r,s=[];null!==(n=function(){if(!e)return null;var t=e.match(/^([^<]*)(<[^>]+>?)?/);return function(t){return e=e.substr(t.length),t}(t[1]?t[1]:t[2])}());)if("<"!==n[0])o.appendChild(t.document.createTextNode(function(t){for(;u=t.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)t=t.replace(u[0],i);return t}(n)));else{if("/"===n[1]){s.length&&s[s.length-1]===n.substr(2).replace(">","")&&(s.pop(),o=o.parentNode);continue}var a,l=parseTimeStamp(n.substr(1,n.length-2));if(l){a=t.document.createProcessingInstruction("timestamp",l),o.appendChild(a);continue}var u=n.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!u)continue;if(!(a=function(e,i){var n=TAG_NAME[e];if(!n)return null;var r=t.document.createElement(n);r.localName=n;var o=TAG_ANNOTATION[e];return o&&i&&(r[o]=i.trim()),r}(u[1],u[3])))continue;if(!function(t,e){return!NEEDS_PARENT[e.localName]||NEEDS_PARENT[e.localName]===t.localName}(o,a))continue;u[2]&&(a.className=u[2].substr(1).replace("."," ")),s.push(u[1]),o.appendChild(a),o=a}return r}function isStrongRTLChar(t){for(var e=0;e<strongRTLRanges.length;e++){var i=strongRTLRanges[e];if(t>=i[0]&&t<=i[1])return!0}return!1}function determineBidi(t){function e(t,e){for(var i=e.childNodes.length-1;i>=0;i--)t.push(e.childNodes[i])}function i(t){if(!t||!t.length)return null;var n=t.pop(),r=n.textContent||n.innerText;if(r){var o=r.match(/^.*(\n|\r)/);return o?(t.length=0,o[0]):r}return"ruby"===n.tagName?i(t):n.childNodes?(e(t,n),i(t)):void 0}var n,r=[],o="";if(!t||!t.childNodes)return"ltr";for(e(r,t);o=i(r);)for(var s=0;s<o.length;s++)if(n=o.charCodeAt(s),isStrongRTLChar(n))return"rtl";return"ltr"}function computeLinePos(t){if("number"==typeof t.line&&(t.snapToLines||t.line>=0&&t.line<=100))return t.line;if(!t.track||!t.track.textTrackList||!t.track.textTrackList.mediaElement)return-1;for(var e=t.track,i=e.textTrackList,n=0,r=0;r<i.length&&i[r]!==e;r++)"showing"===i[r].mode&&n++;return-1*++n}function StyleBox(){}function CueStyleBox(t,e,i){var n=/MSIE\s8\.0/.test(navigator.userAgent),r="rgba(255, 255, 255, 1)",o="rgba(0, 0, 0, 0.8)";n&&(r="rgb(255, 255, 255)",o="rgb(0, 0, 0)"),StyleBox.call(this),this.cue=e,this.cueDiv=parseContent(t,e.text);var s={color:r,backgroundColor:o,position:"relative",left:0,right:0,top:0,bottom:0,display:"inline"};n||(s.writingMode=""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",s.unicodeBidi="plaintext"),this.applyStyles(s,this.cueDiv),this.div=t.document.createElement("div"),s={textAlign:"middle"===e.align?"center":e.align,font:i.font,whiteSpace:"pre-line",position:"absolute"},n||(s.direction=determineBidi(this.cueDiv),s.writingMode=""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl".stylesunicodeBidi="plaintext"),this.applyStyles(s),this.div.appendChild(this.cueDiv);var a=0;switch(e.positionAlign){case"start":a=e.position;break;case"middle":a=e.position-e.size/2;break;case"end":a=e.position-e.size}""===e.vertical?this.applyStyles({left:this.formatStyle(a,"%"),width:this.formatStyle(e.size,"%")}):this.applyStyles({top:this.formatStyle(a,"%"),height:this.formatStyle(e.size,"%")}),this.move=function(t){this.applyStyles({top:this.formatStyle(t.top,"px"),bottom:this.formatStyle(t.bottom,"px"),left:this.formatStyle(t.left,"px"),right:this.formatStyle(t.right,"px"),height:this.formatStyle(t.height,"px"),width:this.formatStyle(t.width,"px")})}}function BoxPosition(t){var e,i,n,r,o=/MSIE\s8\.0/.test(navigator.userAgent);if(t.div){i=t.div.offsetHeight,n=t.div.offsetWidth,r=t.div.offsetTop;var s=(s=t.div.childNodes)&&(s=s[0])&&s.getClientRects&&s.getClientRects();t=t.div.getBoundingClientRect(),e=s?Math.max(s[0]&&s[0].height||0,t.height/s.length):0}this.left=t.left,this.right=t.right,this.top=t.top||r,this.height=t.height||i,this.bottom=t.bottom||r+(t.height||i),this.width=t.width||n,this.lineHeight=e!==undefined?e:t.lineHeight,o&&!this.lineHeight&&(this.lineHeight=13)}function moveBoxToLinePosition(t,e,i,n){var r=new BoxPosition(e),o=e.cue,s=computeLinePos(o),a=[];if(o.snapToLines){var l;switch(o.vertical){case"":a=["+y","-y"],l="height";break;case"rl":a=["+x","-x"],l="width";break;case"lr":a=["-x","+x"],l="width"}var u=r.lineHeight,c=u*Math.round(s),h=i[l]+u,p=a[0];Math.abs(c)>h&&(c=c<0?-1:1,c*=Math.ceil(h/u)*u),s<0&&(c+=""===o.vertical?i.height:i.width,a=a.reverse()),r.move(p,c)}else{var d=r.lineHeight/i.height*100;switch(o.lineAlign){case"middle":s-=d/2;break;case"end":s-=d}switch(o.vertical){case"":e.applyStyles({top:e.formatStyle(s,"%")});break;case"rl":e.applyStyles({left:e.formatStyle(s,"%")});break;case"lr":e.applyStyles({right:e.formatStyle(s,"%")})}a=["+y","-x","+x","-y"],r=new BoxPosition(e)}var f=function(t,e){for(var r,o=new BoxPosition(t),s=1,a=0;a<e.length;a++){for(;t.overlapsOppositeAxis(i,e[a])||t.within(i)&&t.overlapsAny(n);)t.move(e[a]);if(t.within(i))return t;var l=t.intersectPercentage(i);s>l&&(r=new BoxPosition(t),s=l),t=new BoxPosition(o)}return r||o}(r,a);e.move(f.toCSSCompatValues(i))}function WebVTT$1(){}function findDirectionSetting(t){return"string"==typeof t&&(!!directionSetting[t.toLowerCase()]&&t.toLowerCase())}function findAlignSetting(t){return"string"==typeof t&&(!!alignSetting[t.toLowerCase()]&&t.toLowerCase())}function extend$2(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)t[n]=i[n]}return t}function VTTCue(t,e,i){var n=this,r=/MSIE\s8\.0/.test(navigator.userAgent),o={};r?n=document.createElement("custom"):o.enumerable=!0,n.hasBeenReset=!1;var s="",a=!1,l=t,u=e,c=i,h=null,p="",d=!0,f="auto",g="start",y=50,m="middle",v=50,_="middle";if(Object.defineProperty(n,"id",extend$2({},o,{get:function(){return s},set:function(t){s=""+t}})),Object.defineProperty(n,"pauseOnExit",extend$2({},o,{get:function(){return a},set:function(t){a=!!t}})),Object.defineProperty(n,"startTime",extend$2({},o,{get:function(){return l},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");l=t,this.hasBeenReset=!0}})),Object.defineProperty(n,"endTime",extend$2({},o,{get:function(){return u},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");u=t,this.hasBeenReset=!0}})),Object.defineProperty(n,"text",extend$2({},o,{get:function(){return c},set:function(t){c=""+t,this.hasBeenReset=!0}})),Object.defineProperty(n,"region",extend$2({},o,{get:function(){return h},set:function(t){h=t,this.hasBeenReset=!0}})),Object.defineProperty(n,"vertical",extend$2({},o,{get:function(){return p},set:function(t){var e=findDirectionSetting(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");p=e,this.hasBeenReset=!0}})),Object.defineProperty(n,"snapToLines",extend$2({},o,{get:function(){return d},set:function(t){d=!!t,this.hasBeenReset=!0}})),Object.defineProperty(n,"line",extend$2({},o,{get:function(){return f},set:function(t){if("number"!=typeof t&&t!==autoKeyword)throw new SyntaxError("An invalid number or illegal string was specified.");f=t,this.hasBeenReset=!0}})),Object.defineProperty(n,"lineAlign",extend$2({},o,{get:function(){return g},set:function(t){var e=findAlignSetting(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");g=e,this.hasBeenReset=!0}})),Object.defineProperty(n,"position",extend$2({},o,{get:function(){return y},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");y=t,this.hasBeenReset=!0}})),Object.defineProperty(n,"positionAlign",extend$2({},o,{get:function(){return m},set:function(t){var e=findAlignSetting(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");m=e,this.hasBeenReset=!0}})),Object.defineProperty(n,"size",extend$2({},o,{get:function(){return v},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");v=t,this.hasBeenReset=!0}})),Object.defineProperty(n,"align",extend$2({},o,{get:function(){return _},set:function(t){var e=findAlignSetting(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");_=e,this.hasBeenReset=!0}})),n.displayState=undefined,r)return n}function findScrollSetting(t){return"string"==typeof t&&(!!scrollSetting[t.toLowerCase()]&&t.toLowerCase())}function isValidPercentValue(t){return"number"==typeof t&&t>=0&&t<=100}function VTTRegion(){var t=100,e=3,i=0,n=100,r=0,o=100,s="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!isValidPercentValue(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return e},set:function(t){if("number"!=typeof t)throw new TypeError("Lines must be set to a number.");e=t}},regionAnchorY:{enumerable:!0,get:function(){return n},set:function(t){if(!isValidPercentValue(t))throw new Error("RegionAnchorX must be between 0 and 100.");n=t}},regionAnchorX:{enumerable:!0,get:function(){return i},set:function(t){if(!isValidPercentValue(t))throw new Error("RegionAnchorY must be between 0 and 100.");i=t}},viewportAnchorY:{enumerable:!0,get:function(){return o},set:function(t){if(!isValidPercentValue(t))throw new Error("ViewportAnchorY must be between 0 and 100.");o=t}},viewportAnchorX:{enumerable:!0,get:function(){return r},set:function(t){if(!isValidPercentValue(t))throw new Error("ViewportAnchorX must be between 0 and 100.");r=t}},scroll:{enumerable:!0,get:function(){return s},set:function(t){var e=findScrollSetting(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");s=e}}})}function createTrackHelper(t,e,i,n){var r=arguments.length>4&&arguments[4]!==undefined?arguments[4]:{},o=t.textTracks();r.kind=e,i&&(r.label=i),n&&(r.language=n),r.tech=t;var s=new ALL.text.TrackClass(r);return o.addTrack(s),s}function use(t,e){middlewares[t]=middlewares[t]||[],middlewares[t].push(e)}function setSource(t,e,i){t.setTimeout(function(){return setSourceHelper(e,middlewares[e.type],i,t)},1)}function setTech(t,e){t.forEach(function(t){return t.setTech&&t.setTech(e)})}function get$1(t,e,i){return t.reduceRight(middlewareIterator(i),e[i]())}function set$1(t,e,i,n){return e[i](t.reduce(middlewareIterator(i),n))}function middlewareIterator(t){return function(e,i){return i[t]?i[t](e):e}}function setSourceHelper(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[],i=arguments[2],n=arguments[3],r=arguments.length>4&&arguments[4]!==undefined?arguments[4]:[],o=arguments.length>5&&arguments[5]!==undefined&&arguments[5],s=e[0],a=e.slice(1);if("string"==typeof s)setSourceHelper(t,middlewares[s],i,n,r,o);else if(s){var l=s(n);l.setSource(assign({},t),function(e,s){if(e)return setSourceHelper(t,a,i,n,r,o);r.push(l),setSourceHelper(s,t.type===s.type?a:middlewares[s.type],i,n,r,o)})}else a.length?setSourceHelper(t,a,i,n,r,o):o?i(t,r):setSourceHelper(t,middlewares["*"],i,n,r,!0)}function constructColor(t,e){return"rgba("+parseInt(t[1]+t[1],16)+","+parseInt(t[2]+t[2],16)+","+parseInt(t[3]+t[3],16)+","+e+")"}function tryUpdateStyle(t,e,i){try{t.style[e]=i}catch(n){return}}function formatTime(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:t;t=t<0?0:t;var i=Math.floor(t%60),n=Math.floor(t/60%60),r=Math.floor(t/3600),o=Math.floor(e/60%60),s=Math.floor(e/3600);return(isNaN(t)||t===Infinity)&&(r=n=i="-"),r=r>0||s>0?r+":":"",n=((r||o>=10)&&n<10?"0"+n:n)+":",i=i<10?"0"+i:i,r+n+i}function parseOptionValue(t,e){if(e&&(t=e(t)),t&&"none"!==t)return t}function getSelectedOptionValue(t,e){return parseOptionValue(t.options[t.options.selectedIndex].value,e)}function setSelectedOption(t,e,i){if(e)for(var n=0;n<t.options.length;n++)if(parseOptionValue(t.options[n].value,i)===e){t.selectedIndex=n;break}}function videojs(t,e,i){var n=void 0;if("string"==typeof t){var r=videojs.getPlayers();if(0===t.indexOf("#")&&(t=t.slice(1)),r[t])return e&&log$2.warn('Player "'+t+'" is already initialised. Options will not be applied.'),i&&r[t].ready(i),r[t];n=$("#"+t)}else n=t;if(!n||!n.nodeName)throw new TypeError("The element or ID supplied is not valid. (videojs)");if(n.player||Player.players[n.playerId])return n.player||Player.players[n.playerId];isEl(n)&&!document_1.body.contains(n)&&log$2.warn("The element supplied is not included in the DOM"),e=e||{},videojs.hooks("beforesetup").forEach(function(t){var i=t(n,mergeOptions(e));if(!isObject$1(i)||Array.isArray(i))return void log$2.error("please return an object in beforesetup hooks");e=mergeOptions(e,i)})
;var o=Component.getComponent("Player"),s=new o(n,e,i);return videojs.hooks("setup").forEach(function(t){return t(s)}),s}function toObject(t){if(null===t||t===undefined)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}function shouldUseNative(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},i=0;i<10;i++)e["_"+String.fromCharCode(i)]=i;if("0123456789"!==Object.getOwnPropertyNames(e).map(function(t){return e[t]}).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach(function(t){n[t]=t}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(r){return!1}}function decodeComponents(t,e){try{return decodeURIComponent(t.join(""))}catch(r){}if(1===t.length)return t;e=e||1;var i=t.slice(0,e),n=t.slice(e);return Array.prototype.concat.call([],decodeComponents(i),decodeComponents(n))}function decode(t){try{return decodeURIComponent(t)}catch(n){for(var e=t.match(singleMatcher),i=1;i<e.length;i++)t=decodeComponents(e,i).join(""),e=t.match(singleMatcher);return t}}function customDecodeURIComponent(t){for(var e={"%FE%FF":"��","%FF%FE":"��"},i=multiMatcher.exec(t);i;){try{e[i[0]]=decodeURIComponent(i[0])}catch(a){var n=decode(i[0]);n!==i[0]&&(e[i[0]]=n)}i=multiMatcher.exec(t)}e["%C2"]="�";for(var r=Object.keys(e),o=0;o<r.length;o++){var s=r[o];t=t.replace(new RegExp(s,"g"),e[s])}return t}function encoderForArrayFormat(t){switch(t.arrayFormat){case"index":return function(e,i,n){return null===i?[encode(e,t),"[",n,"]"].join(""):[encode(e,t),"[",encode(n,t),"]=",encode(i,t)].join("")};case"bracket":return function(e,i){return null===i?encode(e,t):[encode(e,t),"[]=",encode(i,t)].join("")};default:return function(e,i){return null===i?encode(e,t):[encode(e,t),"=",encode(i,t)].join("")}}}function parserForArrayFormat(t){var e;switch(t.arrayFormat){case"index":return function(t,i,n){if(e=/\[(\d*)\]$/.exec(t),t=t.replace(/\[\d*\]$/,""),!e)return void(n[t]=i);n[t]===undefined&&(n[t]={}),n[t][e[1]]=i};case"bracket":return function(t,i,n){return e=/(\[\])$/.exec(t),t=t.replace(/\[\]$/,""),e?n[t]===undefined?void(n[t]=[i]):void(n[t]=[].concat(n[t],i)):void(n[t]=i)};default:return function(t,e,i){if(i[t]===undefined)return void(i[t]=e);i[t]=[].concat(i[t],e)}}}function encode(t,e){return e.encode?e.strict?strictUriEncode(t):encodeURIComponent(t):t}function keysSorter(t){return Array.isArray(t)?t.sort():"object"==typeof t?keysSorter(Object.keys(t)).sort(function(t,e){return Number(t)-Number(e)}).map(function(e){return t[e]}):t}function extract(t){var e=t.indexOf("?");return-1===e?"":t.slice(e+1)}function parse(t,e){e=objectAssign({arrayFormat:"none"},e);var i=parserForArrayFormat(e),n=Object.create(null);return"string"!=typeof t?n:(t=t.trim().replace(/^[?#&]/,""))?(t.split("&").forEach(function(t){var e=t.replace(/\+/g," ").split("="),r=e.shift(),o=e.length>0?e.join("="):undefined;o=o===undefined?null:decodeUriComponent(o),i(decodeUriComponent(r),o,n)}),Object.keys(n).sort().reduce(function(t,e){var i=n[e];return Boolean(i)&&"object"==typeof i&&!Array.isArray(i)?t[e]=keysSorter(i):t[e]=i,t},Object.create(null))):n}function jsonp(t,e,i){function n(){u.parentNode&&u.parentNode.removeChild(u),window[a]="",c&&clearTimeout(c)}function r(){window[a]&&n()}"function"==typeof e&&(i=e,e={}),e||(e={});var o,s=e.prefix||"__jp",a=e.name||s+count++,l=e.funcKey||"callback";o="object"==_typeof(e.param)?queryString.stringify(e.param):e.param;var u,c,h=null!=e.timeout?e.timeout:6e3,p=encodeURIComponent,d=document.getElementsByTagName("script")[0]||document.head;return h&&(c=setTimeout(function(){n(),i&&i(new Error("Timeout"))},h)),window[a]=function(t){n(),i&&i(null,t)},t+=(~t.indexOf("?")?"&":"?")+o+"&"+l+"="+p(a),t=t.replace("?&","?"),u=document.createElement("script"),u.setAttribute("src",t),u.setAttribute("async",!0),u.onload=function(t){},u.onreadystatechange=function(){"loaded"===this.readyState&&window[a]&&(n(),i&&i(new Error("ServerError")))},u.onerror=function(t){n(),i&&i(new Error("ServerError"))},d.parentNode.insertBefore(u,d),r}function genOverlay(){var t="",e=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];for(i=0;i<32;i++){t+=e[getRandomInt(0,15)]}return t}function getRandomInt(t,e){return Math.floor(Math.random()*(e-t+1)+t)}function mountHlsProvider(t){if(videojs.browser.IS_IOS&&videojs.browser.IS_MQQ)return!1;if(Hls$1&&Hls$1.isSupported()&&!videojs.browser.IS_SAFARI_NOT_SIMULATOR||t)try{var e=videojs.getTech&&videojs.getTech("Html5");e&&e.registerSourceHandler(HlsSourceHandler,0)}catch(i){}}function handlePlaybackMetadataLoaded(t,e){function i(t){return"dash-audio-"+t}function n(t,e){return t.find(function(t){return i(t.index)===e.id})}var r=t.dash.mediaPlayer,o=r.getTracksFor("audio"),s=t.audioTracks();s.length&&e.clearTracks(["audio"]);var a=r.getCurrentTrackFor("audio");o.forEach(function(t){var e=t.lang;t.roles&&t.roles.length&&(e+=" ("+t.roles.join(", ")+")"),s.addTrack(new videojs.AudioTrack({enabled:t===a,id:i(t.index),kind:t.kind||"main",label:e,language:t.lang}))});var l=function(){for(var t=0;t<s.length;t++){var e=s[t];if(e.enabled){var i=n(o,e);r.setCurrentTrack(i)}else;}};s.addEventListener("change",l),t.dash.mediaPlayer.on(dashjs.MediaPlayer.events.STREAM_TEARDOWN_COMPLETE,function(){s.removeEventListener("change",l)})}function setupAudioTracks(t,e){t.dash.mediaPlayer.on(dashjs.MediaPlayer.events.PLAYBACK_METADATA_LOADED,handlePlaybackMetadataLoaded.bind(null,t,e))}function find(t,e){for(var i=0;i<t.length;i++)if(e(t[i]))return t[i]}function attachDashTextTracksToVideojs(t,e,i){function n(){for(var e=t.dash.mediaPlayer,n=t.textTracks(),o=-1,s=0;s<n.length;s+=1)!function(t){var e=n[t];if("showing"===e.mode){var s=find(r,function(t){return t.textTrack===e}),a=s?s.dashTrack:null;a&&(o=i.indexOf(a))}}(s);o!==e.getCurrentTextTrackIndex()&&e.setTextTrack(o)}var r=[],o=i.map(function(t){return{dashTrack:t,trackConfig:{label:t.lang,language:t.lang,srclang:t.lang,kind:t.kind}}}).map(function(e){var i=e.trackConfig,n=e.dashTrack,o=t.addRemoteTextTrack(i,!1);return r.push({textTrack:o.track,dashTrack:n}),o});return t.textTracks().on("change",n),t.dash.mediaPlayer.on(dashjs.MediaPlayer.events.STREAM_TEARDOWN_COMPLETE,function(){t.textTracks().off("change",n)}),n(),o}function setupTextTracks(t,e,i){function n(){o.forEach(t.removeRemoteTextTrack.bind(t)),o=[]}function r(a){var l=(a.index,a.tracks);s.off(dashjs.MediaPlayer.events.TEXT_TRACKS_ADDED,r),n(),l.length&&(o=attachDashTextTracksToVideojs(t,e,l,i))}window_1.VTTCue&&!/\[native code\]/.test(window_1.VTTCue.toString())&&(window_1.VTTCue=!1);var o=[];if(e.featuresNativeTextTracks)return void videojs.log.error("You must pass {html: {nativeCaptions: false}} in the videojs constructor to use text tracks in videojs-contrib-dash");var s=t.dash.mediaPlayer;s.on(dashjs.MediaPlayer.events.TEXT_TRACKS_ADDED,r),s.on(dashjs.MediaPlayer.events.CAN_PLAY,function(){s.off(dashjs.MediaPlayer.events.TEXT_TRACKS_ADDED,r)})}function mountDashProvider(t){(window_1.MediaSource&&dashjs||t)&&videojs.getTech("Html5").registerSourceHandler(videojs.DashSourceHandler(),0)}function getTemplate(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0,e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"fill",i=t,n="draw-"+e;return['<svg height="100%" width="100%" viewBox="0 0 484 348">\n      <path class="'+n+'" transform="translate(4 4)" d="M383,124C350-42,122.3-40.5,90.6,123.5c-110.4,18.5-131,185,.6,216.5H381.6C519.7,310.7,494.1,137.8,383,124Z"/>\n      <path transform="translate(4 4)" d="M200.4,110.2c-9.4-5.8-19.6,2.7-19.4,11.7V256.2c0,8,10.4,16.7,19.4,10.8,2.2-1.4,111.8-65.9,114-67.3,7.4-4,10.1-15.8,0-22.2Z" style="fill:#fff ;"/>\n    </svg>','<svg height="100%" width="100%" viewBox="0 0 476 340">\n      <path class="'+n+'" d="M383,124C350-42,122.3-40.5,90.6,123.5c-110.4,18.5-131,185,.6,216.5H381.6C519.7,310.7,494.1,137.8,383,124Z"/>\n      <path d="M200.4,110.2c-9.4-5.8-19.6,2.7-19.4,11.7V256.2c0,8,10.4,16.7,19.4,10.8,2.2-1.4,111.8-65.9,114-67.3,7.4-4,10.1-15.8,0-22.2Z" style="fill:#fff ;"/>\n    </svg>','<svg height="100%" width="100%" viewBox="0 14 96 68">\n      <path class="'+n+'" d="M96,44.3v7.3c-0.1,7.7-1,15.5-1,15.5s-0.9,6.6-3.8,9.5c-3.6,3.8-7.7,3.8-9.6,4c-13.4,1-33.5,0.9-33.5,0.9 c-0.8,0-25-0.2-32.5-0.9c-2.1-0.4-6.9-0.3-10.6-4.1c-2.9-2.9-3.8-9.5-3.8-9.5s-1-7.7-1.1-15.5v-7.3c0.2-7.8,1.1-15.5,1.1-15.5 s0.9-6.6,3.8-9.5c3.6-3.8,7.7-3.8,9.6-4.1c13.4-1,33.5-0.9,33.5-0.9s20.1-0.1,33.5,0.9c1.9,0.2,5.9,0.2,9.6,4.1 c2.9,2.9,3.8,9.5,3.8,9.5S95.9,36.6,96,44.3z M38.3,61.4L64,47.9L38.3,34.4V61.4z"/>\n      <polygon points="64,47.9 38.3,61.4 38.3,34.4" fill="#fff"/>\n    </svg>'][i]}function isObject$2(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function trimmedEndIndex(t){for(var e=t.length;e--&&reWhitespace.test(t.charAt(e)););return e}function baseTrim(t){return t?t.slice(0,_trimmedEndIndex(t)+1).replace(reTrimStart,""):t}function getRawTag(t){var e=hasOwnProperty$2.call(t,symToStringTag$1),i=t[symToStringTag$1];try{t[symToStringTag$1]=undefined;var n=!0}catch(o){}var r=nativeObjectToString.call(t);return n&&(e?t[symToStringTag$1]=i:delete t[symToStringTag$1]),r}function objectToString(t){return nativeObjectToString$1.call(t)}function baseGetTag(t){return null==t?t===undefined?undefinedTag:nullTag:symToStringTag&&symToStringTag in Object(t)?_getRawTag(t):_objectToString(t)}function isObjectLike(t){return null!=t&&"object"==typeof t}function isSymbol(t){return"symbol"==typeof t||isObjectLike_1(t)&&_baseGetTag(t)==symbolTag}function toNumber(t){if("number"==typeof t)return t;if(isSymbol_1(t))return NAN;if(isObject_1(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=isObject_1(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=_baseTrim(t);var i=reIsBinary.test(t);return i||reIsOctal.test(t)?freeParseInt(t.slice(2),i?2:8):reIsBadHex.test(t)?NAN:+t}function debounce(t,e,i){function n(e){var i=p,n=d;return p=d=undefined,v=e,g=t.apply(n,i)}function r(t){return v=t,y=setTimeout(a,e),_?n(t):g}function o(t){var i=t-m,n=t-v,r=e-i;return b?nativeMin(r,f-n):r}function s(t){var i=t-m,n=t-v;return m===undefined||i>=e||i<0||b&&n>=f}function a(){var t=now_1();if(s(t))return l(t);y=setTimeout(a,o(t))}function l(t){return y=undefined,T&&p?n(t):(p=d=undefined,g)}function u(){y!==undefined&&clearTimeout(y),v=0,p=m=d=y=undefined}function c(){return y===undefined?g:l(now_1())}function h(){var t=now_1(),i=s(t);if(p=arguments,d=this,m=t,i){if(y===undefined)return r(m);if(b)return clearTimeout(y),y=setTimeout(a,e),n(m)}return y===undefined&&(y=setTimeout(a,e)),g}var p,d,f,g,y,m,v=0,_=!1,b=!1,T=!0;if("function"!=typeof t)throw new TypeError(FUNC_ERROR_TEXT$1);return e=toNumber_1(e)||0,isObject_1(i)&&(_=!!i.leading,b="maxWait"in i,f=b?nativeMax(toNumber_1(i.maxWait)||0,e):f,T="trailing"in i?!!i.trailing:T),h.cancel=u,h.flush=c,h}function throttle$1(t,e,i){var n=!0,r=!0;if("function"!=typeof t)throw new TypeError(FUNC_ERROR_TEXT);return isObject_1(i)&&(n="leading"in i?!!i.leading:n,r="trailing"in i?!!i.trailing:r),debounce_1(t,e,{leading:n,maxWait:e,trailing:r})}function mountFlvProvider(t){if(videojs.browser.IS_IOS&&videojs.browser.IS_MQQ)return!1;if(flvjs&&flvjs.isSupported()&&!videojs.browser.IS_TBS&&!videojs.browser.IS_MQQB&&!videojs.browser.IS_SAFARI&&!videojs.browser.IE_VERSION||t)try{var e=videojs.getTech&&videojs.getTech("Html5");e&&e.registerSourceHandler(FlvSourceHandler,0)}catch(i){}}function mountWebRTCProvider(t){if(!videojs.browser.isWebRTCSupported())return!1;try{var e=videojs.getTech&&videojs.getTech("Html5");e&&e.registerSourceHandler(sourceHandler,0)}catch(i){}}function FlashRtmpDecorator(t){return t.streamingFormats={"rtmp/mp4":"MP4","rtmp/flv":"FLV"},t.streamFromParts=function(t,e){return t+"&"+e},t.streamToParts=function(t){var e={connection:"",stream:""};if(!t)return e;var i=t.search(/&(?!\w+=)/),n=void 0;return-1!==i?n=i+1:0===(i=n=t.lastIndexOf("/")+1)&&(i=n=t.length),e.connection=t.substring(0,i),e.stream=t.substring(n,t.length),e},t.isStreamingType=function(e){return e in t.streamingFormats},t.RTMP_RE=/^rtmp[set]?:\/\//i,t.isStreamingSrc=function(e){return t.RTMP_RE.test(e)},t.rtmpSourceHandler={},t.rtmpSourceHandler.canPlayType=function(e){return t.isStreamingType(e)?"maybe":""},t.rtmpSourceHandler.canHandleSource=function(e,i){var n=t.rtmpSourceHandler.canPlayType(e.type);return n||(t.isStreamingSrc(e.src)?"maybe":"")},t.rtmpSourceHandler.handleSource=function(e,i,n){var r=t.streamToParts(e.src);i.setRtmpConnection(r.connection),i.setRtmpStream(r.stream)},t.registerSourceHandler(t.rtmpSourceHandler),t}function _createSetter(t){var e=t.charAt(0).toUpperCase()+t.slice(1);_api["set"+e]=function(e){return this.el_.vjs_setProperty(t,e)}}function _createGetter(t){_api[t]=function(){return this.el_.vjs_getProperty(t)}}function TCPlayer(t,e,i){var n=void 0,r=(new Date).getTime(),o=videojs.dom;if("string"==typeof t)n=document.querySelector("#"+t);else{if(!t||!t.nodeName)throw new TypeError("The ID or element supplied is not valid.");n=t}if("video"!=n.nodeName.toLowerCase()&&"audio"!=n.nodeName.toLowerCase())throw new TypeError("The element type must be <video>.");o.hasClass(n,"tcplayer")||o.addClass(n,"tcplayer"),o.hasClass(n,"video-js")||o.addClass(n,"video-js"),videojs.browser.IE_VERSION&&(n.style.width||n.offsetWidth&&300!==n.offsetWidth)&&(window_1.VIDEOJS_NO_DYNAMIC_STYLE=!0);var s={controls:!0,language:"zh-CN",playbackRates:[.5,1,1.25,1.5,2],controlBar:{volumePanel:{inline:"video"!=n.nodeName.toLowerCase()},fullscreenToggle:"video"==n.nodeName.toLowerCase(),QualitySwitcherMenuButton:!0},plugins:{Skin:"",VID:"",QualitySwitcher:{},MultiResolution:{},Errors:{},Reporter:{},ContextMenu:{},LevelSwitch:{},VttThumbnail:{},PlayerMetrics:{}}};e=videojs.mergeOptions(s,e),videojs.browser.IE_VERSION&&videojs.browser.IE_VERSION<11&&window_1.top,e.autoplay&&videojs.browser.IS_ANDROID&&videojs.browser.IS_WECHAT&&(e.autoplay=!1),log("player initializing",e),videojs.getComponent("player").prototype.options_.children.splice(1,0,"logoImage");var a=videojs(t,e,i);return a.PlayerMetrics(e).setTimingData({initStart:r}),log("player initialized",e),a}Hls$1=Hls$1&&Hls$1.hasOwnProperty("default")?Hls$1["default"]:Hls$1,dashjs=dashjs&&dashjs.hasOwnProperty("default")?dashjs["default"]:dashjs,flvjs=flvjs&&flvjs.hasOwnProperty("default")?flvjs["default"]:flvjs,TXLivePlayer=TXLivePlayer&&TXLivePlayer.hasOwnProperty("default")?TXLivePlayer["default"]:TXLivePlayer;var version="4.5.2",commonjsGlobal="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},win;win="undefined"!=typeof window?window:void 0!==commonjsGlobal?commonjsGlobal:"undefined"!=typeof self?self:{};var window_1=win,empty={},empty$1=(Object.freeze||Object)({"default":empty}),minDoc=empty$1&&empty||empty$1,topLevel=void 0!==commonjsGlobal?commonjsGlobal:"undefined"!=typeof window?window:{},doccy;"undefined"!=typeof document?doccy=document:(doccy=topLevel["__GLOBAL_DOCUMENT_CACHE@4"])||(doccy=topLevel["__GLOBAL_DOCUMENT_CACHE@4"]=minDoc);var document_1=doccy,USER_AGENT=window_1.navigator&&window_1.navigator.userAgent||"",VENDOR=window_1.navigator&&window_1.navigator.vendor||"",webkitVersionMap=/AppleWebKit\/([\d.]+)/i.exec(USER_AGENT),appleWebkitVersion=webkitVersionMap?parseFloat(webkitVersionMap.pop()):null,IS_IPAD=/iPad/i.test(USER_AGENT),IS_IPHONE=/iPhone/i.test(USER_AGENT)&&!IS_IPAD,IS_IPOD=/iPod/i.test(USER_AGENT),IS_IOS=IS_IPHONE||IS_IPAD||IS_IPOD,IOS_VERSION=function(){var t=USER_AGENT.match(/OS (\d+)_/i);return t&&t[1]?t[1]:null}(),IOS_VERSION_ARRAY=function(){var t=USER_AGENT.match(/OS (\d+)_(\d+)_?(\d+)?/i);return t&&[parseInt(t[1],10),parseInt(t[2],10),parseInt(t[3]||"0",10)]||[]}(),IS_ANDROID=/Android/i.test(USER_AGENT),ANDROID_VERSION=function(){var t=USER_AGENT.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!t)return null;var e=t[1]&&parseFloat(t[1]),i=t[2]&&parseFloat(t[2]);return e&&i?parseFloat(t[1]+"."+t[2]):e||null}(),IS_TBS=/TBS\/\d+/i.test(USER_AGENT),TBS_VERSION=function(){var t=USER_AGENT.match(/TBS\/(\d+)/i);if(t&&t[1])return t[1]}(),IS_MQQB=!IS_TBS&&/MQQBrowser\/\d+/i.test(USER_AGENT),IS_QQB=!IS_TBS&&/ QQBrowser\/\d+/i.test(USER_AGENT),IS_PC_WECHAT=/windowswechat/i.test(USER_AGENT),IS_WECHAT=/(micromessenger|webbrowser)/i.test(USER_AGENT),IS_MQQ=/ QQ\/\d+/i.test(USER_AGENT),IS_OLD_ANDROID=IS_ANDROID&&/webkit/i.test(USER_AGENT)&&ANDROID_VERSION<2.3,IS_NATIVE_ANDROID=IS_ANDROID&&ANDROID_VERSION<5&&appleWebkitVersion<537,IS_FIREFOX=/Firefox/i.test(USER_AGENT),IS_EDGE=/Edge/i.test(USER_AGENT),IS_EDG=/Edg/i.test(USER_AGENT),EDG_VERSION=IS_EDG&&function(){var t=USER_AGENT.match(/Edg\/(\d+)/);return t&&t[1]?parseFloat(t[1]):null}(),IS_CHROME=function(){return!(IS_EDGE||!/Chrome/i.test(USER_AGENT))||!(!/Safari/i.test(USER_AGENT)||!/CriOS/i.test(USER_AGENT))}()&&!IS_WECHAT&&!IS_MQQB&&!IS_QQB,CHROME_VERSION=function(){var t=USER_AGENT.match(/Chrome\/(\d+)/),e=USER_AGENT.match(/CriOS\/(\d+)/);return t&&t[1]?parseFloat(t[1]):e&&e[1]?parseFloat(e[1]):null}(),IS_IE8=/MSIE\s8\.0/.test(USER_AGENT),IE_VERSION=function(){var t=/MSIE\s(\d+)\.\d/.exec(USER_AGENT),e=t&&parseFloat(t[1]);return!e&&/Trident\/7.0/i.test(USER_AGENT)&&/rv:11.0/.test(USER_AGENT)&&(e=11),e}(),IS_SAFARI=/Safari/i.test(USER_AGENT)&&!IS_CHROME&&!IS_ANDROID&&!IS_EDGE&&!IS_MQQB&&!IS_QQB&&!IS_PC_WECHAT,IS_ANY_SAFARI=IS_SAFARI||IS_IOS,IS_SAFARI_NOT_SIMULATOR=IS_SAFARI&&"Google Inc."!==VENDOR,IS_UC=/UCBrowser\/(\d+)\./i.test(USER_AGENT),TOUCH_ENABLED=isReal()&&("ontouchstart"in window_1||window_1.DocumentTouch&&window_1.document instanceof window_1.DocumentTouch),BACKGROUND_SIZE_SUPPORTED=isReal()&&"backgroundSize"in window_1.document.createElement("video").style,IS_WIN=/Windows/i.test(USER_AGENT),IS_MAC=/MAC OS X/i.test(USER_AGENT),WIN_VER=function(){return/Windows NT ([.\w]+)/.test(USER_AGENT),RegExp.$1}(),MAC_VER=function(){return/Mac OS X (\w+)/.test(USER_AGENT),RegExp.$1}(),QQ_VER=function(){return/QQBrowser\/([.\d]+)/.test(USER_AGENT),RegExp.$1}(),EDGE_VER=function(){return/Edge\/([.\d]+)/.test(USER_AGENT),RegExp.$1}(),SAFARI_VER=function(){return/Version\/([.\d]+)/.test(USER_AGENT),RegExp.$1}(),FIREFOX_VER=function(){return/Firefox\/([.\d]+)/.test(USER_AGENT),RegExp.$1}(),CHROME_VER=function(){return/Chrome\/([.\d]+)/.test(USER_AGENT),RegExp.$1}(),IS_HUAWEI=/HUAWEI|honor|HMA/i.test(USER_AGENT),IS_XIAOMI=/HM|RedMi|Mi/i.test(USER_AGENT)&&!IS_HUAWEI,IS_OPPO=/OPPO/i.test(USER_AGENT),IS_VIVO=/VIVO/i.test(USER_AGENT),IS_SX=/GT-|SM-|SCH-/i.test(USER_AGENT),IS_ONE=/ONE/i.test(USER_AGENT),IS_CP=/Coolpad/i.test(USER_AGENT),IS_ZX=/ZTE/i.test(USER_AGENT),isWebRTCAPISupported=function(){return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter(function(t){return t in window_1}).length>0},isWebRTCSupported=function(){return!IS_UC&&!IS_EDGE&&(!(IS_EDG&&EDG_VERSION<80)&&(!(IS_FIREFOX&&FIREFOX_VER<56)&&(!(!IS_TBS&&IS_SAFARI&&IS_IOS&&(0===IOS_VERSION_ARRAY.length||IOS_VERSION_ARRAY[0]<11||11===IOS_VERSION_ARRAY[0]&&IOS_VERSION_ARRAY[1]<1||11===IOS_VERSION_ARRAY[0]&&1===IOS_VERSION_ARRAY[1]&&IOS_VERSION_ARRAY[2]<2))&&isWebRTCAPISupported())))},browser=(Object.freeze||Object)({USER_AGENT:USER_AGENT,IS_IPAD:IS_IPAD,IS_IPHONE:IS_IPHONE,IS_IPOD:IS_IPOD,IS_IOS:IS_IOS,IOS_VERSION:IOS_VERSION,IOS_VERSION_ARRAY:IOS_VERSION_ARRAY,IS_ANDROID:IS_ANDROID,ANDROID_VERSION:ANDROID_VERSION,IS_TBS:IS_TBS,TBS_VERSION:TBS_VERSION,IS_MQQB:IS_MQQB,IS_QQB:IS_QQB,IS_PC_WECHAT:IS_PC_WECHAT,IS_WECHAT:IS_WECHAT,IS_MQQ:IS_MQQ,IS_OLD_ANDROID:IS_OLD_ANDROID,IS_NATIVE_ANDROID:IS_NATIVE_ANDROID,IS_FIREFOX:IS_FIREFOX,IS_EDGE:IS_EDGE,IS_EDG:IS_EDG,EDG_VERSION:EDG_VERSION,IS_CHROME:IS_CHROME,CHROME_VERSION:CHROME_VERSION,IS_IE8:IS_IE8,IE_VERSION:IE_VERSION,IS_SAFARI:IS_SAFARI,IS_ANY_SAFARI:IS_ANY_SAFARI,IS_SAFARI_NOT_SIMULATOR:IS_SAFARI_NOT_SIMULATOR,IS_UC:IS_UC,TOUCH_ENABLED:TOUCH_ENABLED,BACKGROUND_SIZE_SUPPORTED:BACKGROUND_SIZE_SUPPORTED,IS_WIN:IS_WIN,IS_MAC:IS_MAC,WIN_VER:WIN_VER,MAC_VER:MAC_VER,QQ_VER:QQ_VER,EDGE_VER:EDGE_VER,SAFARI_VER:SAFARI_VER,FIREFOX_VER:FIREFOX_VER,CHROME_VER:CHROME_VER,IS_HUAWEI:IS_HUAWEI,IS_XIAOMI:IS_XIAOMI,IS_OPPO:IS_OPPO,IS_VIVO:IS_VIVO,IS_SX:IS_SX,IS_ONE:IS_ONE,IS_CP:IS_CP,IS_ZX:IS_ZX,isWebRTCAPISupported:isWebRTCAPISupported,isWebRTCSupported:isWebRTCSupported}),_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},classCallCheck=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},inherits=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},possibleConstructorReturn=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},taggedTemplateLiteralLoose=function(t,e){return t.raw=e,t},toString=Object.prototype.toString,keys=function(t){return isObject$1(t)?Object.keys(t):[]},log$1=void 0,level="info",history=[],logByType=function(t,e){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:!!IE_VERSION&&IE_VERSION<11,n=log$1.levels[level],r=new RegExp("^("+n+")$");if("log"!==t&&e.unshift(t.toUpperCase()+":"),history&&history.push([].concat(e)),(log$1.enableLog||"log"!=t)&&(e.unshift("TCPlayer:"),window_1.console)){var o=window_1.console[t];o||"debug"!==t||(o=window_1.console.info||window_1.console.log),o&&n&&r.test(t)&&(i&&(e=e.map(function(t){if(isObject$1(t)||Array.isArray(t))try{return JSON.stringify(t)}catch(e){return String(t)}return String(t)}).join(" ")),o.apply?o[Array.isArray(e)?"apply":"call"](window_1.console,e):o(e))}};log$1=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];logByType("log",e)},log$1.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:level},log$1.level=function(t){if("string"==typeof t){if(!log$1.levels.hasOwnProperty(t))throw new Error('"'+t+'" in not a valid log level');level=t}return level},log$1.history=function(){return history?[].concat(history):[]},log$1.history.clear=function(){history&&(history.length=0)},log$1.history.disable=function(){null!==history&&(history.length=0,history=null)},log$1.history.enable=function(){null===history&&(history=[])},log$1.enableLog=!1,log$1.error=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return logByType("error",e)},log$1.warn=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return logByType("warn",e)},log$1.debug=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return logByType("debug",e)};var log$2=log$1,tsml=function(t){for(var e="",i=0;i<arguments.length;i++)e+=clean(t[i])+(arguments[i+1]||"");return e},_templateObject=taggedTemplateLiteralLoose(["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."],["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."]),$=createQuerier("querySelector"),$$=createQuerier("querySelectorAll"),Dom=(Object.freeze||Object)({isReal:isReal,isEl:isEl,isInFrame:isInFrame,createEl:createEl,textContent:textContent,prependTo:prependTo,hasClass:hasClass,addClass:addClass,removeClass:removeClass,toggleClass:toggleClass,setAttributes:setAttributes,getAttributes:getAttributes,getAttribute:getAttribute,setAttribute:setAttribute,removeAttribute:removeAttribute,blockTextSelection:blockTextSelection,unblockTextSelection:unblockTextSelection,getBoundingClientRect:getBoundingClientRect,findPosition:findPosition,getPointerPosition:getPointerPosition,isTextNode:isTextNode,emptyEl:emptyEl,normalizeContent:normalizeContent,appendContent:appendContent,insertContent:insertContent,isSingleLeftClick:isSingleLeftClick,$:$,$$:$$}),_guid=1,elData={},elIdAttr="vdata"+(new Date).getTime(),_supportsPassive=!1;!function(){try{var t=Object.defineProperty({},"passive",{get:function(){_supportsPassive=!0}});window_1.addEventListener("test",null,t),window_1.removeEventListener("test",null,t)}catch(e){}}();var passiveEvents=["touchstart","touchmove"],Events=(Object.freeze||Object)({fixEvent:fixEvent,on:on,off:off,trigger:trigger,one:one}),_windowLoaded=!1,videojs$2=void 0,autoSetup=function(){if(isReal()){var t=document_1.getElementsByTagName("video"),e=document_1.getElementsByTagName("audio"),i=document_1.getElementsByTagName("video-js"),n=[];if(t&&t.length>0)for(var r=0,o=t.length;r<o;r++)n.push(t[r]);if(e&&e.length>0)for(var s=0,a=e.length;s<a;s++)n.push(e[s]);if(i&&i.length>0)for(var l=0,u=i.length;l<u;l++)n.push(i[l]);if(n&&n.length>0)for(var c=0,h=n.length;c<h;c++){var p=n[c];if(!p||!p.getAttribute){autoSetupTimeout(1);break}if(p.player===undefined){var d=p.getAttribute("data-setup");null!==d&&videojs$2(p)}}else _windowLoaded||autoSetupTimeout(1)}};isReal()&&"complete"===document_1.readyState?_windowLoaded=!0:one(window_1,"load",function(){_windowLoaded=!0});var createStyleElement=function(t){var e=document_1.createElement("style");return e.className=t,e},setTextContent=function(t,e){t.styleSheet?t.styleSheet.cssText=e:t.textContent=e},bind=function(t,e,i){e.guid||(e.guid=newGUID());var n=function(){return e.apply(t,arguments)};return n.guid=i?i+"_"+e.guid:e.guid,n},throttle=function(t,e){var i=Date.now();return function(){var n=Date.now();n-i>=e&&(t.apply(undefined,arguments),i=n)}},EventTarget=function(){};EventTarget.prototype.allowedEvents_={},EventTarget.prototype.on=function(t,e){var i=this.addEventListener;this.addEventListener=function(){},on(this,t,e),this.addEventListener=i},EventTarget.prototype.addEventListener=EventTarget.prototype.on,EventTarget.prototype.off=function(t,e){off(this,t,e)},EventTarget.prototype.removeEventListener=EventTarget.prototype.off,EventTarget.prototype.one=function(t,e){var i=this.addEventListener;this.addEventListener=function(){},one(this,t,e),this.addEventListener=i},EventTarget.prototype.trigger=function(t){var e=t.type||t;"string"==typeof t&&(t={type:e}),t=fixEvent(t),this.allowedEvents_[e]&&this["on"+e]&&this["on"+e](t),trigger(this,t)},EventTarget.prototype.dispatchEvent=EventTarget.prototype.trigger;var isEvented=function(t){return t instanceof EventTarget||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},isValidEventType=function(t){return"string"==typeof t&&/\S/.test(t)||Array.isArray(t)&&!!t.length},validateTarget=function(t){if(!t.nodeName&&!isEvented(t))throw new Error("Invalid target; must be a DOM node or evented object.")},validateEventType=function(t){if(!isValidEventType(t))throw new Error("Invalid event type; must be a non-empty string or array.")},validateListener=function(t){if("function"!=typeof t)throw new Error("Invalid listener; must be a function.")},normalizeListenArgs=function(t,e){var i=e.length<3||e[0]===t||e[0]===t.eventBusEl_,n=void 0,r=void 0,o=void 0;return i?(n=t.eventBusEl_,e.length>=3&&e.shift(),r=e[0],o=e[1]):(n=e[0],r=e[1],o=e[2]),validateTarget(n),validateEventType(r),validateListener(o),o=bind(t,o),{isTargetingSelf:i,target:n,type:r,listener:o}},listen=function(t,e,i,n){validateTarget(t),t.nodeName?Events[e](t,i,n):t[e](i,n)},EventedMixin={on:function(){for(var t=this,e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];var r=normalizeListenArgs(this,i),o=r.isTargetingSelf,s=r.target,a=r.type,l=r.listener;if(listen(s,"on",a,l),!o){var u=function(){return t.off(s,a,l)};u.guid=l.guid;var c=function(){return t.off("dispose",u)};c.guid=l.guid,listen(this,"on","dispose",u),listen(s,"on","dispose",c)}},one:function(){for(var t=this,e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];var r=normalizeListenArgs(this,i),o=r.isTargetingSelf,s=r.target,a=r.type,l=r.listener;if(o)listen(s,"one",a,l);else{var u=function c(){for(var e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];t.off(s,a,c),l.apply(null,i)};u.guid=l.guid,listen(s,"one",a,u)}},off:function(t,e,i){if(!t||isValidEventType(t))isValidEventType(e)?off(this.eventBusEl_,e,i):off(this.eventBusEl_,t,e);else{var n=t,r=e;validateTarget(n),validateEventType(r),validateListener(i),i=bind(this,i),this.off("dispose",i),n.nodeName?(off(n,r,i),off(n,"dispose",i)):isEvented(n)&&(n.off(r,i),n.off("dispose",i))}},trigger:function(t,e){return trigger(this.eventBusEl_,t,e)}},StatefulMixin={state:{},setState:function(t){var e=this;"function"==typeof t&&(t=t());var i=void 0;return each(t,function(t,n){e.state[n]!==t&&(i=i||{},i[n]={from:e.state[n],to:t}),e.state[n]=t}),i&&isEvented(this)&&this.trigger({changes:i,type:"statechanged"}),i}},Component=function(){function t(e,i,n){if(classCallCheck(this,t),!e&&this.play?this.player_=e=this:this.player_=e,this.options_=extend(!0,{},this.options_),i=this.options_=mergeOptions(this.options_,i),this.id_=i.id||i.el&&i.el.id,!this.id_){var r=e&&e.id&&e.id()||"no_player";this.id_=r+"_component_"+newGUID()}this.name_=i.name||null,i.el?this.el_=i.el:!1!==i.createEl&&(this.el_=this.createEl()),!1!==i.evented&&evented(this,{eventBusKey:this.el_?"el_":null}),stateful(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},!1!==i.initChildren&&this.initChildren(),this.ready(n),!1!==i.reportTouchActivity&&this.enableTouchActivity()}return t.prototype.dispose=function(){if(this.trigger({type:"dispose",bubbles:!1}),this.children_)for(var t=this.children_.length-1;t>=0;t--)this.children_[t].dispose&&this.children_[t].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),removeData(this.el_),this.el_=null),this.player_=null},t.prototype.player=function(){return this.player_},t.prototype.options=function(t){return log$2.warn("this.options() has been deprecated and will be moved to the constructor in 6.0"),t?(this.options_=mergeOptions(this.options_,t),this.options_):this.options_},t.prototype.el=function(){return this.el_},t.prototype.createEl=function(t,e,i){return createEl(t,e,i)},t.prototype.localize=function(t,e){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:t,n=this.player_.language&&this.player_.language(),r=this.player_.languages&&this.player_.languages(),o=r&&r[n],s=n&&n.split("-")[0],a=r&&r[s],l=i;return o&&o[t]?l=o[t]:a&&a[t]&&(l=a[t]),e&&(l=l.replace(/\{(\d+)\}/g,function(t,i){var n=e[i-1],r=n;return void 0===n&&(r=t),r})),l},t.prototype.contentEl=function(){return this.contentEl_||this.el_},t.prototype.id=function(){return this.id_},t.prototype.name=function(){return this.name_},t.prototype.children=function(){return this.children_},t.prototype.getChildById=function(t){return this.childIndex_[t]},t.prototype.getChild=function(t){if(t)return t=toTitleCase(t),this.childNameIndex_[t]},t.prototype.addChild=function(e){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:this.children_.length,r=void 0,o=void 0;if("string"==typeof e){o=toTitleCase(e);var s=i.componentClass||o;i.name=o;var a=t.getComponent(s);if(!a)throw new Error("Component "+s+" does not exist");if("function"!=typeof a)return null;r=new a(this.player_||this,i)}else r=e;if(this.children_.splice(n,0,r),"function"==typeof r.id&&(this.childIndex_[r.id()]=r),o=o||r.name&&toTitleCase(r.name()),o&&(this.childNameIndex_[o]=r),"function"==typeof r.el&&r.el()){
var l=this.contentEl().children,u=l[n]||null;this.contentEl().insertBefore(r.el(),u)}return r},t.prototype.removeChild=function(t){if("string"==typeof t&&(t=this.getChild(t)),t&&this.children_){for(var e=!1,i=this.children_.length-1;i>=0;i--)if(this.children_[i]===t){e=!0,this.children_.splice(i,1);break}if(e){this.childIndex_[t.id()]=null,this.childNameIndex_[t.name()]=null;var n=t.el();n&&n.parentNode===this.contentEl()&&this.contentEl().removeChild(t.el())}}},t.prototype.initChildren=function(){var e=this,i=this.options_.children;if(i){var n=this.options_,r=function(t){var i=t.name,r=t.opts;if(n[i]!==undefined&&(r=n[i]),!1!==r){!0===r&&(r={}),r.playerOptions=e.options_.playerOptions;var o=e.addChild(i,r);o&&(e[i]=o)}},o=void 0,s=t.getComponent("Tech");o=Array.isArray(i)?i:Object.keys(i),o.concat(Object.keys(this.options_).filter(function(t){return!o.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(t){var n=void 0,r=void 0;return"string"==typeof t?(n=t,r=i[n]||e.options_[n]||{}):(n=t.name,r=t),{name:n,opts:r}}).filter(function(e){var i=t.getComponent(e.opts.componentClass||toTitleCase(e.name));return i&&!s.isTech(i)}).forEach(r)}},t.prototype.buildCSSClass=function(){return""},t.prototype.ready=function(t){var e=arguments.length>1&&arguments[1]!==undefined&&arguments[1];if(t)return this.isReady_?void(e?t.call(this):this.setTimeout(t,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(t))},t.prototype.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var t=this.readyQueue_;this.readyQueue_=[],t&&t.length>0&&t.forEach(function(t){t.call(this)},this),this.trigger("ready")},1)},t.prototype.$=function(t,e){return $(t,e||this.contentEl())},t.prototype.$$=function(t,e){return $$(t,e||this.contentEl())},t.prototype.hasClass=function(t){return hasClass(this.el_,t)},t.prototype.addClass=function(t){addClass(this.el_,t)},t.prototype.removeClass=function(t){removeClass(this.el_,t)},t.prototype.toggleClass=function(t,e){toggleClass(this.el_,t,e)},t.prototype.show=function(){this.removeClass("vjs-hidden")},t.prototype.hide=function(){this.addClass("vjs-hidden")},t.prototype.lockShowing=function(){this.addClass("vjs-lock-showing")},t.prototype.unlockShowing=function(){this.removeClass("vjs-lock-showing")},t.prototype.getAttribute=function(t){return getAttribute(this.el_,t)},t.prototype.setAttribute=function(t,e){setAttribute(this.el_,t,e)},t.prototype.removeAttribute=function(t){removeAttribute(this.el_,t)},t.prototype.width=function(t,e){return this.dimension("width",t,e)},t.prototype.height=function(t,e){return this.dimension("height",t,e)},t.prototype.dimensions=function(t,e){this.width(t,!0),this.height(e)},t.prototype.dimension=function(t,e,i){if(e!==undefined)return null!==e&&e===e||(e=0),-1!==(""+e).indexOf("%")||-1!==(""+e).indexOf("px")?this.el_.style[t]=e:this.el_.style[t]="auto"===e?"":e+"px",void(i||this.trigger("componentresize"));if(!this.el_)return 0;var n=this.el_.style[t],r=n.indexOf("px");return-1!==r?parseInt(n.slice(0,r),10):parseInt(this.el_["offset"+toTitleCase(t)],10)},t.prototype.currentDimension=function(t){var e=0;if("width"!==t&&"height"!==t)throw new Error("currentDimension only accepts width or height value");if("function"==typeof window_1.getComputedStyle){var i=window_1.getComputedStyle(this.el_);e=i.getPropertyValue(t)||i[t]}if(0===(e=parseFloat(e))){var n="offset"+toTitleCase(t);e=this.el_[n]}return e},t.prototype.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},t.prototype.currentWidth=function(){return this.currentDimension("width")},t.prototype.currentHeight=function(){return this.currentDimension("height")},t.prototype.focus=function(){this.el_.focus()},t.prototype.blur=function(){this.el_.blur()},t.prototype.emitTapEvents=function(){var t=0,e=null,i=void 0;this.on("touchstart",function(n){1===n.touches.length&&(e={pageX:n.touches[0].pageX,pageY:n.touches[0].pageY},t=(new Date).getTime(),i=!0)}),this.on("touchmove",function(t){if(t.touches.length>1)i=!1;else if(e){var n=t.touches[0].pageX-e.pageX,r=t.touches[0].pageY-e.pageY,o=Math.sqrt(n*n+r*r);o>10&&(i=!1)}});var n=function(){i=!1};this.on("touchleave",n),this.on("touchcancel",n),this.on("touchend",function(n){if(e=null,!0===i){(new Date).getTime()-t<200&&(n.preventDefault(),this.trigger("tap"))}})},t.prototype.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var t=bind(this.player(),this.player().reportUserActivity),e=void 0;this.on("touchstart",function(){t(),this.clearInterval(e),e=this.setInterval(t,250)});var i=function(i){t(),this.clearInterval(e)};this.on("touchmove",t),this.on("touchend",i),this.on("touchcancel",i)}},t.prototype.setTimeout=function(t,e){var i=this;t=bind(this,t);var n=window_1.setTimeout(t,e),r=function(){return i.clearTimeout(n)};return r.guid="vjs-timeout-"+n,this.on("dispose",r),n},t.prototype.clearTimeout=function(t){window_1.clearTimeout(t);var e=function(){};return e.guid="vjs-timeout-"+t,this.off("dispose",e),t},t.prototype.setInterval=function(t,e){var i=this;t=bind(this,t);var n=window_1.setInterval(t,e),r=function(){return i.clearInterval(n)};return r.guid="vjs-interval-"+n,this.on("dispose",r),n},t.prototype.clearInterval=function(t){window_1.clearInterval(t);var e=function(){};return e.guid="vjs-interval-"+t,this.off("dispose",e),t},t.prototype.requestAnimationFrame=function(t){var e=this;if(this.supportsRaf_){t=bind(this,t);var i=window_1.requestAnimationFrame(t),n=function(){return e.cancelAnimationFrame(i)};return n.guid="vjs-raf-"+i,this.on("dispose",n),i}return this.setTimeout(t,1e3/60)},t.prototype.cancelAnimationFrame=function(t){if(this.supportsRaf_){window_1.cancelAnimationFrame(t);var e=function(){};return e.guid="vjs-raf-"+t,this.off("dispose",e),t}return this.clearTimeout(t)},t.registerComponent=function(e,i){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var n=t.getComponent("Tech"),r=n&&n.isTech(i),o=t===i||t.prototype.isPrototypeOf(i.prototype);if(r||!o){var s=void 0;throw s=r?"techs must be registered using Tech.registerTech()":"must be a Component subclass",new Error('Illegal component, "'+e+'"; '+s+".")}e=toTitleCase(e),t.components_||(t.components_={});var a=t.getComponent("Player");if("Player"===e&&a&&a.players){var l=a.players,u=Object.keys(l);if(l&&u.length>0&&u.map(function(t){return l[t]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return t.components_[e]=i,i},t.getComponent=function(e){if(e)return e=toTitleCase(e),t.components_&&t.components_[e]?t.components_[e]:void 0},t}();Component.prototype.supportsRaf_="function"==typeof window_1.requestAnimationFrame&&"function"==typeof window_1.cancelAnimationFrame,Component.registerComponent("Component",Component);for(var EXT_MIME={m3u8:"application/x-mpegURL",flv:"video/flv",mp4:"video/mp4",webm:"video/webm",rtmp:"rtmp/flv",mpd:"application/dash+xml",mp3:"audio/mpeg"},SERVER_PATH="//playvideo.qcloud.com",SERVER_PATH_V3="//playvideo.qcloud.com",SERVER_PATH_V4="//playvideo.qcloud.com",SERVER_PATH_BACKUP="//bkplayvideo.qcloud.com",LICENSE_PATH="https://drm.vod2.myqcloud.com/getlicense/v1",parseUrl=function(t){var e=["protocol","hostname","port","pathname","search","hash","host"],i=document_1.createElement("a");i.href=t;var n=""===i.host&&"file:"!==i.protocol,r=void 0;n&&(r=document_1.createElement("div"),r.innerHTML='<a href="'+t+'"></a>',i=r.firstChild,r.setAttribute("style","display:none; position:absolute;"),document_1.body.appendChild(r));for(var o={},s=0;s<e.length;s++)o[e[s]]=i[e[s]];return"http:"===o.protocol&&(o.host=o.host.replace(/:80$/,"")),"https:"===o.protocol&&(o.host=o.host.replace(/:443$/,"")),o.protocol||(o.protocol=window_1.location.protocol),n&&document_1.body.removeChild(r),o},getAbsoluteURL=function(t){if(!t.match(/^https?:\/\//)){var e=document_1.createElement("div");e.innerHTML='<a href="'+t+'">x</a>',t=e.firstChild.href}return t},getFileExtension=function(t){if("string"==typeof t){var e=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/i,i=e.exec(t);if(i)return i.pop().toLowerCase()}return""},isCrossOrigin=function(t){var e=window_1.location,i=parseUrl(t);return(":"===i.protocol?e.protocol:i.protocol)+i.host!==e.protocol+e.host},Url=(Object.freeze||Object)({parseUrl:parseUrl,getAbsoluteURL:getAbsoluteURL,getFileExtension:getFileExtension,isCrossOrigin:isCrossOrigin}),FullscreenApi={},apiMap=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],specApi=apiMap[0],browserApi=void 0,prefixedAPI=!1,i$2=0;i$2<apiMap.length;i$2++)if(apiMap[i$2][1]in document_1){browserApi=apiMap[i$2];break}if(browserApi){for(var _i=0;_i<browserApi.length;_i++)FullscreenApi[specApi[_i]]=browserApi[_i];prefixedAPI=browserApi[0]===specApi[0]}MediaError.prototype.code=0,MediaError.prototype.message="",MediaError.prototype.status=null,MediaError.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],MediaError.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var errNum=0;errNum<MediaError.errorTypes.length;errNum++)MediaError[MediaError.errorTypes[errNum]]=errNum,MediaError.prototype[MediaError.errorTypes[errNum]]=errNum;var tuple=SafeParseTuple,trackToJson_=function(t){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,i,n){return t[i]&&(e[i]=t[i]),e},{cues:t.cues&&Array.prototype.map.call(t.cues,function(t){return{startTime:t.startTime,endTime:t.endTime,text:t.text,id:t.id}})})},textTracksToJson=function(t){var e=t.$$("track"),i=Array.prototype.map.call(e,function(t){return t.track});return Array.prototype.map.call(e,function(t){var e=trackToJson_(t.track);return t.src&&(e.src=t.src),e}).concat(Array.prototype.filter.call(t.textTracks(),function(t){return-1===i.indexOf(t)}).map(trackToJson_))},jsonToTextTracks=function(t,e){return t.forEach(function(t){var i=e.addRemoteTextTrack(t).track;!t.src&&t.cues&&t.cues.forEach(function(t){return i.addCue(t)})}),e.textTracks()},textTrackConverter={textTracksToJson:textTracksToJson,jsonToTextTracks:jsonToTextTracks,trackToJson_:trackToJson_},MODAL_CLASS_NAME="vjs-modal-dialog",ESC=27,ModalDialog=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.opened_=r.hasBeenOpened_=r.hasBeenFilled_=!1,r.closeable(!r.options_.uncloseable),r.content(r.options_.content),r.contentEl_=createEl("div",{className:MODAL_CLASS_NAME+"-content"},{role:"document"}),r.descEl_=createEl("p",{className:MODAL_CLASS_NAME+"-description vjs-control-text",id:r.el().getAttribute("aria-describedby")}),textContent(r.descEl_,r.description()),r.el_.appendChild(r.descEl_),r.el_.appendChild(r.contentEl_),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},e.prototype.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,t.prototype.dispose.call(this)},e.prototype.buildCSSClass=function(){return MODAL_CLASS_NAME+" vjs-hidden "+t.prototype.buildCSSClass.call(this)},e.prototype.handleKeyPress=function(t){t.which===ESC&&this.closeable()&&this.close()},e.prototype.label=function(){return this.localize(this.options_.label||"Modal Window")},e.prototype.description=function(){var t=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(t+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),t},e.prototype.open=function(){if(!this.opened_){var t=this.player();this.trigger("beforemodalopen"),this.opened_=!0,(this.options_.fillAlways||!this.hasBeenOpened_&&!this.hasBeenFilled_)&&this.fill(),this.wasPlaying_=!t.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&t.pause(),this.closeable()&&this.on(this.el_.ownerDocument,"keydown",bind(this,this.handleKeyPress)),this.hadControls_=t.controls(),t.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},e.prototype.opened=function(t){return"boolean"==typeof t&&this[t?"open":"close"](),this.opened_},e.prototype.close=function(){if(this.opened_){var t=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&t.play(),this.closeable()&&this.off(this.el_.ownerDocument,"keydown",bind(this,this.handleKeyPress)),this.hadControls_&&t.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},e.prototype.closeable=function(t){if("boolean"==typeof t){var e=this.closeable_=!!t,i=this.getChild("closeButton");if(e&&!i){var n=this.contentEl_;this.contentEl_=this.el_,i=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=n,this.on(i,"close",this.close)}!e&&i&&(this.off(i,"close",this.close),this.removeChild(i),i.dispose())}return this.closeable_},e.prototype.fill=function(){this.fillWith(this.content())},e.prototype.fillWith=function(t){var e=this.contentEl(),i=e.parentNode,n=e.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,i.removeChild(e),this.empty(),insertContent(e,t),this.trigger("modalfill"),n?i.insertBefore(e,n):i.appendChild(e);var r=this.getChild("closeButton");r&&i.appendChild(r.el_)},e.prototype.empty=function(){this.trigger("beforemodalempty"),emptyEl(this.contentEl()),this.trigger("modalempty")},e.prototype.content=function(t){return void 0!==t&&(this.content_=t),this.content_},e.prototype.conditionalFocus_=function(){var t=document_1.activeElement,e=this.player_.el_;this.previouslyActiveEl_=null,(e.contains(t)||e===t)&&(this.previouslyActiveEl_=t,this.focus(),this.on(document_1,"keydown",this.handleKeyDown))},e.prototype.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null),this.off(document_1,"keydown",this.handleKeyDown)},e.prototype.handleKeyDown=function(t){if(9===t.which){for(var e=this.focusableEls_(),i=this.el_.querySelector(":focus"),n=void 0,r=0;r<e.length;r++)if(i===e[r]){n=r;break}document_1.activeElement===this.el_&&(n=0),t.shiftKey&&0===n?(e[e.length-1].focus(),t.preventDefault()):t.shiftKey||n!==e.length-1||(e[0].focus(),t.preventDefault())}},e.prototype.focusableEls_=function(){var t=this.el_.querySelectorAll("*");return Array.prototype.filter.call(t,function(t){return(t instanceof window_1.HTMLAnchorElement||t instanceof window_1.HTMLAreaElement)&&t.hasAttribute("href")||(t instanceof window_1.HTMLInputElement||t instanceof window_1.HTMLSelectElement||t instanceof window_1.HTMLTextAreaElement||t instanceof window_1.HTMLButtonElement)&&!t.hasAttribute("disabled")||t instanceof window_1.HTMLIFrameElement||t instanceof window_1.HTMLObjectElement||t instanceof window_1.HTMLEmbedElement||t.hasAttribute("tabindex")&&-1!==t.getAttribute("tabindex")||t.hasAttribute("contenteditable")})},e}(Component);ModalDialog.prototype.options_={pauseOnOpen:!0,temporary:!0},Component.registerComponent("ModalDialog",ModalDialog);var TrackList=function(t){function e(){var i,n=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[],r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;classCallCheck(this,e);var o=possibleConstructorReturn(this,t.call(this));if(!r&&(r=o,IS_IE8)){r=document_1.createElement("custom");for(var s in e.prototype)"constructor"!==s&&(r[s]=e.prototype[s])}r.tracks_=[],Object.defineProperty(r,"length",{get:function(){return this.tracks_.length}});for(var a=0;a<n.length;a++)r.addTrack(n[a]);return i=r,possibleConstructorReturn(o,i)}return inherits(e,t),e.prototype.addTrack=function(t){var e=this.tracks_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.tracks_[e]}}),-1===this.tracks_.indexOf(t)&&(this.tracks_.push(t),this.trigger({track:t,type:"addtrack"}))},e.prototype.removeTrack=function(t){for(var e=void 0,i=0,n=this.length;i<n;i++)if(this[i]===t){e=this[i],e.off&&e.off(),this.tracks_.splice(i,1);break}e&&this.trigger({track:e,type:"removetrack"})},e.prototype.getTrackById=function(t){for(var e=null,i=0,n=this.length;i<n;i++){var r=this[i];if(r.id===t){e=r;break}}return e},e}(EventTarget);TrackList.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"};for(var event$1 in TrackList.prototype.allowedEvents_)TrackList.prototype["on"+event$1]=null;var disableOthers=function(t,e){for(var i=0;i<t.length;i++)Object.keys(t[i]).length&&e.id!==t[i].id&&(t[i].enabled=!1)},AudioTrackList=function(t){function e(){var i,n,r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];classCallCheck(this,e);for(var o=void 0,s=r.length-1;s>=0;s--)if(r[s].enabled){disableOthers(r,r[s]);break}if(IS_IE8){o=document_1.createElement("custom");for(var a in TrackList.prototype)"constructor"!==a&&(o[a]=TrackList.prototype[a]);for(var l in e.prototype)"constructor"!==l&&(o[l]=e.prototype[l])}return o=i=possibleConstructorReturn(this,t.call(this,r,o)),o.changing_=!1,n=o,possibleConstructorReturn(i,n)}return inherits(e,t),e.prototype.addTrack=function(e){var i=this;e.enabled&&disableOthers(this,e),t.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("enabledchange",function(){i.changing_||(i.changing_=!0,disableOthers(i,e),i.changing_=!1,i.trigger("change"))})},e}(TrackList),disableOthers$1=function(t,e){for(var i=0;i<t.length;i++)Object.keys(t[i]).length&&e.id!==t[i].id&&(t[i].selected=!1)},VideoTrackList=function(t){function e(){var i,n,r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];classCallCheck(this,e);for(var o=void 0,s=r.length-1;s>=0;s--)if(r[s].selected){disableOthers$1(r,r[s]);break}if(IS_IE8){o=document_1.createElement("custom");for(var a in TrackList.prototype)"constructor"!==a&&(o[a]=TrackList.prototype[a]);for(var l in e.prototype)"constructor"!==l&&(o[l]=e.prototype[l])}return o=i=possibleConstructorReturn(this,t.call(this,r,o)),o.changing_=!1,Object.defineProperty(o,"selectedIndex",{get:function(){for(var t=0;t<this.length;t++)if(this[t].selected)return t;return-1},set:function(){}}),n=o,possibleConstructorReturn(i,n)}return inherits(e,t),e.prototype.addTrack=function(e){var i=this;e.selected&&disableOthers$1(this,e),t.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("selectedchange",function(){i.changing_||(i.changing_=!0,disableOthers$1(i,e),i.changing_=!1,i.trigger("change"))})},e}(TrackList),TextTrackList=function(t){function e(){var i,n,r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];classCallCheck(this,e);var o=void 0;if(IS_IE8){o=document_1.createElement("custom");for(var s in TrackList.prototype)"constructor"!==s&&(o[s]=TrackList.prototype[s]);for(var a in e.prototype)"constructor"!==a&&(o[a]=e.prototype[a])}return o=i=possibleConstructorReturn(this,t.call(this,r,o)),n=o,possibleConstructorReturn(i,n)}return inherits(e,t),e.prototype.addTrack=function(e){if("disabled"===e.mode&&!e.label)return!1;t.prototype.addTrack.call(this,e),e.addEventListener("modechange",bind(this,function(){this.trigger("change")})),-1===["metadata","chapters"].indexOf(e.kind)&&e.addEventListener("modechange",bind(this,function(){this.trigger("selectedlanguagechange")}))},e}(TrackList),HtmlTrackElementList=function(){function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];classCallCheck(this,t);var i=this;if(IS_IE8){i=document_1.createElement("custom");for(var n in t.prototype)"constructor"!==n&&(i[n]=t.prototype[n])}i.trackElements_=[],Object.defineProperty(i,"length",{get:function(){return this.trackElements_.length}});for(var r=0,o=e.length;r<o;r++)i.addTrackElement_(e[r]);if(IS_IE8)return i}return t.prototype.addTrackElement_=function(t){var e=this.trackElements_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.trackElements_[e]}}),-1===this.trackElements_.indexOf(t)&&this.trackElements_.push(t)},t.prototype.getTrackElementByTrack_=function(t){for(var e=void 0,i=0,n=this.trackElements_.length;i<n;i++)if(t===this.trackElements_[i].track){e=this.trackElements_[i];break}return e},t.prototype.removeTrackElement_=function(t){for(var e=0,i=this.trackElements_.length;e<i;e++)if(t===this.trackElements_[e]){this.trackElements_.splice(e,1);break}},t}(),TextTrackCueList=function(){function t(e){classCallCheck(this,t);var i=this;if(IS_IE8){i=document_1.createElement("custom");for(var n in t.prototype)"constructor"!==n&&(i[n]=t.prototype[n])}if(t.prototype.setCues_.call(i,e),Object.defineProperty(i,"length",{get:function(){return this.length_}}),IS_IE8)return i}return t.prototype.setCues_=function(t){var e=this.length||0,i=0,n=t.length;this.cues_=t,this.length_=t.length;var r=function(t){""+t in this||Object.defineProperty(this,""+t,{get:function(){return this.cues_[t]}})};if(e<n)for(i=e;i<n;i++)r.call(this,i)},t.prototype.getCueById=function(t){for(var e=null,i=0,n=this.length;i<n;i++){var r=this[i];if(r.id===t){e=r;break}}return e},t}(),VideoTrackKind={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},AudioTrackKind={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},TextTrackKind={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},TextTrackMode={disabled:"disabled",hidden:"hidden",showing:"showing"},Track=function(t){function e(){var i,n=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this)),o=r;if(IS_IE8){o=document_1.createElement("custom");for(var s in e.prototype)"constructor"!==s&&(o[s]=e.prototype[s])}var a={id:n.id||"vjs_track_"+newGUID(),kind:n.kind||"",label:n.label||"",language:n.language||""};for(var l in a)!function(t){Object.defineProperty(o,t,{get:function(){return a[t]},set:function(){}})}(l);return i=o,possibleConstructorReturn(r,i)}return inherits(e,t),e}(EventTarget),isFunction_1=isFunction$1,toString$1=Object.prototype.toString,trim=function(t){return t.replace(/^\s+|\s+$/g,"")},isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)},parseHeaders=function(t){if(!t)return{};for(var e={},i=trim(t).split("\n"),n=0;n<i.length;n++){var r=i[n],o=r.indexOf(":"),s=trim(r.slice(0,o)).toLowerCase(),a=trim(r.slice(o+1));"undefined"==typeof e[s]?e[s]=a:isArray(e[s])?e[s].push(a):e[s]=[e[s],a]}return e},immutable=extend$1,hasOwnProperty=Object.prototype.hasOwnProperty,xhr=createXHR;createXHR.XMLHttpRequest=window_1.XMLHttpRequest||noop,createXHR.XDomainRequest="withCredentials"in new createXHR.XMLHttpRequest?createXHR.XMLHttpRequest:window_1.XDomainRequest,forEachArray(["get","put","post","patch","head","delete"],function(t){createXHR["delete"===t?"del":t]=function(e,i,n){return i=initParams(e,i,n),i.method=t.toUpperCase(),_createXHR(i)}});var parseCues=function(t,e){var i=new window_1.WebVTT.Parser(window_1,window_1.vttjs,window_1.WebVTT.StringDecoder()),n=[];i.oncue=function(t){e.addCue(t)},i.onparsingerror=function(t){n.push(t)},i.onflush=function(){e.trigger({type:"loadeddata",target:e})},i.parse(t),n.length>0&&(window_1.console&&window_1.console.groupCollapsed&&window_1.console.groupCollapsed("Text Track parsing errors for "+e.src),n.forEach(function(t){return log$2.error(t)}),window_1.console&&window_1.console.groupEnd&&window_1.console.groupEnd()),i.flush()},loadTrack=function(t,e){var i={uri:t},n=isCrossOrigin(t);n&&(i.cors=n),xhr(i,bind(this,function(t,i,n){if(t)return log$2.error(t,i);if(e.loaded_=!0,"function"!=typeof window_1.WebVTT){if(e.tech_){var r=function(){return parseCues(n,e)};e.tech_.on("vttjsloaded",r),e.tech_.on("vttjserror",function(){log$2.error("vttjs failed to load, stopping trying to process "+e.src),e.tech_.off("vttjsloaded",r)})}}else parseCues(n,e)}))},TextTrack=function(t){function e(){var i,n,r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};if(classCallCheck(this,e),!r.tech)throw new Error("A tech was not provided.");var o=mergeOptions(r,{kind:TextTrackKind[r.kind]||"subtitles",language:r.language||r.srclang||""}),s=TextTrackMode[o.mode]||"disabled",a=o["default"];"metadata"!==o.kind&&"chapters"!==o.kind||(s="hidden");var l=i=possibleConstructorReturn(this,t.call(this,o));if(l.tech_=o.tech,IS_IE8)for(var u in e.prototype)"constructor"!==u&&(l[u]=e.prototype[u]);l.cues_=[],l.activeCues_=[];var c=new TextTrackCueList(l.cues_),h=new TextTrackCueList(l.activeCues_),p=!1,d=bind(l,function(){this.activeCues,p&&(this.trigger("cuechange"),p=!1)});return"disabled"!==s&&l.tech_.ready(function(){l.tech_.on("timeupdate",d)},!0),Object.defineProperty(l,"default",{get:function(){return a},set:function(){}}),Object.defineProperty(l,"mode",{get:function(){return s},set:function(t){var e=this;TextTrackMode[t]&&(s=t,"showing"===s&&this.tech_.ready(function(){e.tech_.on("timeupdate",d)},!0),this.trigger("modechange"))}}),Object.defineProperty(l,"cues",{get:function(){return this.loaded_?c:null},set:function(){}}),Object.defineProperty(l,"activeCues",{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return h;for(var t=this.tech_.currentTime(),e=[],i=0,n=this.cues.length;i<n;i++){var r=this.cues[i];r.startTime<=t&&r.endTime>=t?e.push(r):r.startTime===r.endTime&&r.startTime<=t&&r.startTime+.5>=t&&e.push(r)}if(p=!1,e.length!==this.activeCues_.length)p=!0;else for(var o=0;o<e.length;o++)-1===this.activeCues_.indexOf(e[o])&&(p=!0);return this.activeCues_=e,h.setCues_(this.activeCues_),h},set:function(){}}),o.src?(l.src=o.src,loadTrack(o.src,l)):l.loaded_=!0,n=l,possibleConstructorReturn(i,n)}return inherits(e,t),e.prototype.addCue=function(t){var e=t;if(window_1.vttjs&&!(t instanceof window_1.vttjs.VTTCue)){e=new window_1.vttjs.VTTCue(t.startTime,t.endTime,t.text);for(var i in t)i in e||(e[i]=t[i]);e.id=t.id,e.originalCue_=t}for(var n=this.tech_.textTracks(),r=0;r<n.length;r++)n[r]!==this&&n[r].removeCue(e);this.cues_.push(e),this.cues.setCues_(this.cues_)},e.prototype.removeCue=function(t){for(var e=this.cues_.length;e--;){var i=this.cues_[e];if(i===t||i.originalCue_&&i.originalCue_===t){this.cues_.splice(e,1),this.cues.setCues_(this.cues_);break}}},e}(Track);TextTrack.prototype.allowedEvents_={cuechange:"cuechange"};var AudioTrack=function(t){function e(){var i,n,r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};classCallCheck(this,e);var o=mergeOptions(r,{kind:AudioTrackKind[r.kind]||""}),s=i=possibleConstructorReturn(this,t.call(this,o)),a=!1;if(IS_IE8)for(var l in e.prototype)"constructor"!==l&&(s[l]=e.prototype[l]);return Object.defineProperty(s,"enabled",{get:function(){return a},set:function(t){"boolean"==typeof t&&t!==a&&(a=t,this.trigger("enabledchange"))}}),o.enabled&&(s.enabled=o.enabled),s.loaded_=!0,n=s,possibleConstructorReturn(i,n)}return inherits(e,t),e}(Track),VideoTrack=function(t){function e(){var i,n,r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};classCallCheck(this,e);var o=mergeOptions(r,{kind:VideoTrackKind[r.kind]||""}),s=i=possibleConstructorReturn(this,t.call(this,o)),a=!1;if(IS_IE8)for(var l in e.prototype)"constructor"!==l&&(s[l]=e.prototype[l]);return Object.defineProperty(s,"selected",{get:function(){return a},set:function(t){"boolean"==typeof t&&t!==a&&(a=t,this.trigger("selectedchange"))}}),o.selected&&(s.selected=o.selected),n=s,possibleConstructorReturn(i,n)}return inherits(e,t),e}(Track),NONE=0,LOADING=1,LOADED=2,ERROR=3,HTMLTrackElement=function(t){function e(){var i=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};classCallCheck(this,e);var n=possibleConstructorReturn(this,t.call(this)),r=void 0,o=n;if(IS_IE8){o=document_1.createElement("custom");for(var s in e.prototype)"constructor"!==s&&(o[s]=e.prototype[s])}var a=new TextTrack(i);if(o.kind=a.kind,o.src=a.src,o.srclang=a.language,o.label=a.label,o["default"]=a["default"],Object.defineProperty(o,"readyState",{get:function(){return r}}),Object.defineProperty(o,"track",{get:function(){return a}}),r=NONE,a.addEventListener("loadeddata",function(){r=LOADED,o.trigger({type:"load",target:o})}),IS_IE8){var l;return l=o,possibleConstructorReturn(n,l)}return n}return inherits(e,t),e}(EventTarget);HTMLTrackElement.prototype.allowedEvents_={load:"load"},HTMLTrackElement.NONE=NONE,HTMLTrackElement.LOADING=LOADING,HTMLTrackElement.LOADED=LOADED,HTMLTrackElement.ERROR=ERROR;var NORMAL={audio:{ListClass:AudioTrackList,TrackClass:AudioTrack,capitalName:"Audio"},video:{ListClass:VideoTrackList,TrackClass:VideoTrack,capitalName:"Video"},text:{ListClass:TextTrackList,TrackClass:TextTrack,capitalName:"Text"}};Object.keys(NORMAL).forEach(function(t){NORMAL[t].getterName=t+"Tracks",NORMAL[t].privateName=t+"Tracks_"});var REMOTE={remoteText:{ListClass:TextTrackList,TrackClass:TextTrack,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:HtmlTrackElementList,TrackClass:HTMLTrackElement,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},ALL=mergeOptions(NORMAL,REMOTE);REMOTE.names=Object.keys(REMOTE),NORMAL.names=Object.keys(NORMAL),ALL.names=[].concat(REMOTE.names).concat(NORMAL.names);var win$1;win$1="undefined"!=typeof window?window:void 0!==commonjsGlobal?commonjsGlobal:"undefined"!=typeof self?self:{};var window_1$1=win$1,_objCreate=Object.create||function(){function t(){}return function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return t.prototype=e,new t}}();ParsingError.prototype=_objCreate(Error.prototype),ParsingError.prototype.constructor=ParsingError,ParsingError.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},Settings.prototype={set:function(t,e){this.get(t)||""===e||(this.values[t]=e)},get:function(t,e,i){return i?this.has(t)?this.values[t]:e[i]:this.has(t)?this.values[t]:e},has:function(t){return t in this.values},alt:function(t,e,i){for(var n=0;n<i.length;++n)if(e===i[n]){this.set(t,e);break}},integer:function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},percent:function(t,e){return!!(e.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(e=parseFloat(e))>=0&&e<=100)&&(this.set(t,e),!0)}};var ESCAPE={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},TAG_NAME={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},TAG_ANNOTATION={v:"title",lang:"lang"},NEEDS_PARENT={rt:"ruby"
},strongRTLRanges=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];StyleBox.prototype.applyStyles=function(t,e){e=e||this.div;for(var i in t)t.hasOwnProperty(i)&&(e.style[i]=t[i])},StyleBox.prototype.formatStyle=function(t,e){return 0===t?0:t+e},CueStyleBox.prototype=_objCreate(StyleBox.prototype),CueStyleBox.prototype.constructor=CueStyleBox,BoxPosition.prototype.move=function(t,e){switch(e=e!==undefined?e:this.lineHeight,t){case"+x":this.left+=e,this.right+=e;break;case"-x":this.left-=e,this.right-=e;break;case"+y":this.top+=e,this.bottom+=e;break;case"-y":this.top-=e,this.bottom-=e}},BoxPosition.prototype.overlaps=function(t){return this.left<t.right&&this.right>t.left&&this.top<t.bottom&&this.bottom>t.top},BoxPosition.prototype.overlapsAny=function(t){for(var e=0;e<t.length;e++)if(this.overlaps(t[e]))return!0;return!1},BoxPosition.prototype.within=function(t){return this.top>=t.top&&this.bottom<=t.bottom&&this.left>=t.left&&this.right<=t.right},BoxPosition.prototype.overlapsOppositeAxis=function(t,e){switch(e){case"+x":return this.left<t.left;case"-x":return this.right>t.right;case"+y":return this.top<t.top;case"-y":return this.bottom>t.bottom}},BoxPosition.prototype.intersectPercentage=function(t){return Math.max(0,Math.min(this.right,t.right)-Math.max(this.left,t.left))*Math.max(0,Math.min(this.bottom,t.bottom)-Math.max(this.top,t.top))/(this.height*this.width)},BoxPosition.prototype.toCSSCompatValues=function(t){return{top:this.top-t.top,bottom:t.bottom-this.bottom,left:this.left-t.left,right:t.right-this.right,height:this.height,width:this.width}},BoxPosition.getSimpleBoxPosition=function(t){var e=t.div?t.div.offsetHeight:t.tagName?t.offsetHeight:0,i=t.div?t.div.offsetWidth:t.tagName?t.offsetWidth:0,n=t.div?t.div.offsetTop:t.tagName?t.offsetTop:0;return t=t.div?t.div.getBoundingClientRect():t.tagName?t.getBoundingClientRect():t,{left:t.left,right:t.right,top:t.top||n,height:t.height||e,bottom:t.bottom||n+(t.height||e),width:t.width||i}},WebVTT$1.StringDecoder=function(){return{decode:function(t){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}},WebVTT$1.convertCueToDOMTree=function(t,e){return t&&e?parseContent(t,e):null},WebVTT$1.processCues=function(t,e,i){if(!t||!e||!i)return null;for(;i.firstChild;)i.removeChild(i.firstChild);var n=t.document.createElement("div");if(n.style.position="absolute",n.style.left="0",n.style.right="0",n.style.top="0",n.style.bottom="0",n.style.margin="1.5%",i.appendChild(n),function(t){for(var e=0;e<t.length;e++)if(t[e].hasBeenReset||!t[e].displayState)return!0;return!1}(e)){var r=[],o=BoxPosition.getSimpleBoxPosition(n),s=Math.round(.05*o.height*100)/100,a={font:s+"px sans-serif"};!function(){for(var i,s,l=0;l<e.length;l++)s=e[l],i=new CueStyleBox(t,s,a),n.appendChild(i.div),moveBoxToLinePosition(t,i,o,r),s.displayState=i.div,r.push(BoxPosition.getSimpleBoxPosition(i))}()}else for(var l=0;l<e.length;l++)n.appendChild(e[l].displayState)},WebVTT$1.Parser=function(t,e,i){i||(i=e,e={}),e||(e={}),this.window=t,this.vttjs=e,this.state="INITIAL",this.buffer="",this.decoder=i||new TextDecoder("utf8"),this.regionList=[]},WebVTT$1.Parser.prototype={reportOrThrowError:function(t){if(!(t instanceof ParsingError))throw t;this.onparsingerror&&this.onparsingerror(t)},parse:function(t){function e(){for(var t=r.buffer,e=0;e<t.length&&"\r"!==t[e]&&"\n"!==t[e];)++e;var i=t.substr(0,e);return"\r"===t[e]&&++e,"\n"===t[e]&&++e,r.buffer=t.substr(e),i}function i(t){var e=new Settings;if(parseOptions(t,function(t,i){switch(t){case"id":e.set(t,i);break;case"width":e.percent(t,i);break;case"lines":e.integer(t,i);break;case"regionanchor":case"viewportanchor":var n=i.split(",");if(2!==n.length)break;var r=new Settings;if(r.percent("x",n[0]),r.percent("y",n[1]),!r.has("x")||!r.has("y"))break;e.set(t+"X",r.get("x")),e.set(t+"Y",r.get("y"));break;case"scroll":e.alt(t,i,["up"])}},/=/,/\s/),e.has("id")){var i=new(r.vttjs.VTTRegion||r.window.VTTRegion);i.width=e.get("width",100),i.lines=e.get("lines",3),i.regionAnchorX=e.get("regionanchorX",0),i.regionAnchorY=e.get("regionanchorY",100),i.viewportAnchorX=e.get("viewportanchorX",0),i.viewportAnchorY=e.get("viewportanchorY",100),i.scroll=e.get("scroll",""),r.onregion&&r.onregion(i),r.regionList.push({id:e.get("id"),region:i})}}function n(t){var e=new Settings;parseOptions(t,function(t,i){switch(t){case"MPEGT":e.integer(t+"S",i);break;case"LOCA":e.set(t+"L",parseTimeStamp(i))}},/[^\d]:/,/,/),r.ontimestampmap&&r.ontimestampmap({MPEGTS:e.get("MPEGTS"),LOCAL:e.get("LOCAL")})}var r=this;t&&(r.buffer+=r.decoder.decode(t,{stream:!0}));try{var o;if("INITIAL"===r.state){if(!/\r\n|\n/.test(r.buffer))return this;o=e();var s=o.match(/^WEBVTT([ \t].*)?$/);if(!s||!s[0])throw new ParsingError(ParsingError.Errors.BadSignature);r.state="HEADER"}for(var a=!1;r.buffer;){if(!/\r\n|\n/.test(r.buffer))return this;switch(a?a=!1:o=e(),r.state){case"HEADER":/:/.test(o)?function(t){t.match(/X-TIMESTAMP-MAP/)?parseOptions(t,function(t,e){switch(t){case"X-TIMESTAMP-MAP":n(e)}},/=/):parseOptions(t,function(t,e){switch(t){case"Region":i(e)}},/:/)}(o):o||(r.state="ID");continue;case"NOTE":o||(r.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(o)){r.state="NOTE";break}if(!o)continue;if(r.cue=new(r.vttjs.VTTCue||r.window.VTTCue)(0,0,""),r.state="CUE",-1===o.indexOf("--\x3e")){r.cue.id=o;continue}case"CUE":try{parseCue(o,r.cue,r.regionList)}catch(u){r.reportOrThrowError(u),r.cue=null,r.state="BADCUE";continue}r.state="CUETEXT";continue;case"CUETEXT":var l=-1!==o.indexOf("--\x3e");if(!o||l&&(a=!0)){r.oncue&&r.oncue(r.cue),r.cue=null,r.state="ID";continue}r.cue.text&&(r.cue.text+="\n"),r.cue.text+=o;continue;case"BADCUE":o||(r.state="ID");continue}}}catch(u){r.reportOrThrowError(u),"CUETEXT"===r.state&&r.cue&&r.oncue&&r.oncue(r.cue),r.cue=null,r.state="INITIAL"===r.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),(t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new ParsingError(ParsingError.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}};var vtt$1=WebVTT$1,autoKeyword="auto",directionSetting={"":!0,lr:!0,rl:!0},alignSetting={start:!0,middle:!0,end:!0,left:!0,right:!0};VTTCue.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var vttcue=VTTCue,scrollSetting={"":!0,up:!0},vttregion=VTTRegion,browserIndex=createCommonjsModule(function(t){var e=t.exports={WebVTT:vtt$1,VTTCue:vttcue,VTTRegion:vttregion};window_1$1.vttjs=e,window_1$1.WebVTT=e.WebVTT;var i=e.VTTCue,n=e.VTTRegion,r=window_1$1.VTTCue,o=window_1$1.VTTRegion;e.shim=function(){window_1$1.VTTCue=i,window_1$1.VTTRegion=n},e.restore=function(){window_1$1.VTTCue=r,window_1$1.VTTRegion=o},window_1$1.VTTCue||e.shim()}),Tech=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:function(){};classCallCheck(this,e),n.reportTouchActivity=!1;var o=possibleConstructorReturn(this,t.call(this,i,n,r));return o.hasStarted_=!1,o.on("playing",function(){this.hasStarted_=!0}),o.on("loadstart",function(){this.hasStarted_=!1}),ALL.names.forEach(function(t){var e=ALL[t];n&&n[e.getterName]&&(o[e.privateName]=n[e.getterName])}),o.featuresProgressEvents||o.manualProgressOn(),o.featuresTimeupdateEvents||o.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(t){!1===n["native"+t+"Tracks"]&&(o["featuresNative"+t+"Tracks"]=!1)}),!1===n.nativeCaptions||!1===n.nativeTextTracks?o.featuresNativeTextTracks=!1:!0!==n.nativeCaptions&&!0!==n.nativeTextTracks||(o.featuresNativeTextTracks=!0),o.featuresNativeTextTracks||o.emulateTextTracks(),o.autoRemoteTextTracks_=new ALL.text.ListClass,o.initTrackListeners(),n.nativeControlsForTouch||o.emitTapEvents(),o.constructor&&(o.name_=o.constructor.name||"Unknown Tech"),o}return inherits(e,t),e.prototype.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},e.prototype.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},e.prototype.trackProgress=function(t){this.stopTrackingProgress(),this.progressInterval=this.setInterval(bind(this,function(){var t=this.bufferedPercent();this.bufferedPercent_!==t&&this.trigger("progress"),this.bufferedPercent_=t,1===t&&this.stopTrackingProgress()}),500)},e.prototype.onDurationChange=function(t){this.duration_=this.duration()},e.prototype.buffered=function(){return createTimeRanges(0,0)},e.prototype.bufferedPercent=function(){return bufferedPercent(this.buffered(),this.duration_)},e.prototype.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},e.prototype.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},e.prototype.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},e.prototype.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},e.prototype.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.prototype.dispose=function(){this.clearTracks(NORMAL.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),t.prototype.dispose.call(this)},e.prototype.clearTracks=function(t){var e=this;t=[].concat(t),t.forEach(function(t){for(var i=e[t+"Tracks"]()||[],n=i.length;n--;){var r=i[n];"text"===t&&e.removeRemoteTextTrack(r),i.removeTrack(r)}})},e.prototype.cleanupAutoTextTracks=function(){for(var t=this.autoRemoteTextTracks_||[],e=t.length;e--;){var i=t[e];this.removeRemoteTextTrack(i)}},e.prototype.reset=function(){},e.prototype.error=function(t){return t!==undefined&&(this.error_=new MediaError(t),this.trigger("error")),this.error_},e.prototype.played=function(){return this.hasStarted_?createTimeRanges(0,0):createTimeRanges()},e.prototype.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.prototype.initTrackListeners=function(){var t=this;NORMAL.names.forEach(function(e){var i=NORMAL[e],n=function(){t.trigger(e+"trackchange")},r=t[i.getterName]();r.addEventListener("removetrack",n),r.addEventListener("addtrack",n),t.on("dispose",function(){r.removeEventListener("removetrack",n),r.removeEventListener("addtrack",n)})})},e.prototype.addWebVttScript_=function(){var t=this;if(!window_1.WebVTT)if(document_1.body.contains(this.el())){if(!this.options_["vtt.js"]&&isPlain(browserIndex)&&Object.keys(browserIndex).length>0)return void this.trigger("vttjsloaded");var e=document_1.createElement("script");e.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.12.4/vtt.min.js",e.onload=function(){t.trigger("vttjsloaded")},e.onerror=function(){t.trigger("vttjserror")},this.on("dispose",function(){e.onload=null,e.onerror=null}),window_1.WebVTT=!0,this.el().parentNode.appendChild(e)}else this.ready(this.addWebVttScript_)},e.prototype.emulateTextTracks=function(){var t=this,e=this.textTracks(),i=this.remoteTextTracks(),n=function(t){return e.addTrack(t.track)},r=function(t){return e.removeTrack(t.track)};i.on("addtrack",n),i.on("removetrack",r),this.addWebVttScript_();var o=function(){return t.trigger("texttrackchange")},s=function(){o();for(var t=0;t<e.length;t++){var i=e[t];i.removeEventListener("cuechange",o),"showing"===i.mode&&i.addEventListener("cuechange",o)}};s(),e.addEventListener("change",s),e.addEventListener("addtrack",s),e.addEventListener("removetrack",s),this.on("dispose",function(){i.off("addtrack",n),i.off("removetrack",r),e.removeEventListener("change",s),e.removeEventListener("addtrack",s),e.removeEventListener("removetrack",s);for(var t=0;t<e.length;t++){e[t].removeEventListener("cuechange",o)}})},e.prototype.addTextTrack=function(t,e,i){if(!t)throw new Error("TextTrack kind is required but was not provided");return createTrackHelper(this,t,e,i)},e.prototype.createRemoteTextTrack=function(t){var e=mergeOptions(t,{tech:this});return new REMOTE.remoteTextEl.TrackClass(e)},e.prototype.addRemoteTextTrack=function(){var t=this,e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},i=arguments[1],n=this.createRemoteTextTrack(e);return!0!==i&&!1!==i&&(log$2.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),i=!0),this.remoteTextTrackEls().addTrackElement_(n),this.remoteTextTracks().addTrack(n.track),!0!==i&&this.ready(function(){return t.autoRemoteTextTracks_.addTrack(n.track)}),n},e.prototype.removeRemoteTextTrack=function(t){var e=this.remoteTextTrackEls().getTrackElementByTrack_(t);this.remoteTextTrackEls().removeTrackElement_(e),this.remoteTextTracks().removeTrack(t),this.autoRemoteTextTracks_.removeTrack(t)},e.prototype.getVideoPlaybackQuality=function(){return{}},e.prototype.setPoster=function(){},e.prototype.playsinline=function(){},e.prototype.setPlaysinline=function(){},e.prototype.canPlayType=function(){return""},e.canPlayType=function(){return""},e.canPlaySource=function(t,i){return e.canPlayType(t.type)},e.isTech=function(t){return t.prototype instanceof e||t instanceof e||t===e},e.registerTech=function(t,i){if(e.techs_||(e.techs_={}),!e.isTech(i))throw new Error("Tech "+t+" must be a Tech");if(!e.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!e.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return t=toTitleCase(t),e.techs_[t]=i,"Tech"!==t&&e.defaultTechOrder_.push(t),i},e.getTech=function(t){if(t)return t=toTitleCase(t),e.techs_&&e.techs_[t]?e.techs_[t]:window_1&&window_1.videojs&&window_1.videojs[t]?(log$2.warn("The "+t+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),window_1.videojs[t]):void 0},e}(Component);ALL.names.forEach(function(t){var e=ALL[t];Tech.prototype[e.getterName]=function(){return this[e.privateName]=this[e.privateName]||new e.ListClass,this[e.privateName]}}),Tech.prototype.featuresVolumeControl=!0,Tech.prototype.featuresMuteControl=!0,Tech.prototype.featuresFullscreenResize=!1,Tech.prototype.featuresPlaybackRate=!1,Tech.prototype.featuresProgressEvents=!1,Tech.prototype.featuresTimeupdateEvents=!1,Tech.prototype.featuresNativeTextTracks=!1,Tech.withSourceHandlers=function(t){t.registerSourceHandler=function(e,i){var n=t.sourceHandlers;n||(n=t.sourceHandlers=[]),i===undefined&&(i=n.length),n.splice(i,0,e)},t.canPlayType=function(e){for(var i=t.sourceHandlers||[],n=void 0,r=0;r<i.length;r++)if(n=i[r].canPlayType(e))return n;return""},t.selectSourceHandler=function(e,i){for(var n=t.sourceHandlers||[],r=0;r<n.length;r++)if(n[r].canHandleSource(e,i))return n[r];return null},t.canPlaySource=function(e,i){var n=t.selectSourceHandler(e,i);return n?n.canHandleSource(e,i):""},["seekable","duration"].forEach(function(t){var e=this[t];"function"==typeof e&&(this[t]=function(){return this.sourceHandler_&&this.sourceHandler_[t]?this.sourceHandler_[t].apply(this.sourceHandler_,arguments):e.apply(this,arguments)})},t.prototype),t.prototype.setSource=function(e){var i=t.selectSourceHandler(e,this.options_);i||(t.nativeSourceHandler?i=t.nativeSourceHandler:log$2.error("No source hander found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),i!==t.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=i.handleSource(e,this,this.options_),this.on("dispose",this.disposeSourceHandler)},t.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},Component.registerComponent("Tech",Tech),Tech.registerTech("Tech",Tech),Tech.defaultTechOrder_=[];var middlewares={},allowedGetters={buffered:1,currentTime:1,duration:1,seekable:1,played:1},allowedSetters={setCurrentTime:1},MimetypesKind={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",m4a:"audio/mp4",mp3:"audio/mpeg",aac:"audio/aac",caf:"audio/x-caf",flac:"audio/flac",oga:"audio/ogg",wav:"audio/wav",m3u8:"application/x-mpegURL",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",svg:"image/svg+xml",webp:"image/webp"},getMimetype=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"",e=getFileExtension(t);return MimetypesKind[e.toLowerCase()]||""},filterSource=function t(e){if(Array.isArray(e)){var i=[];e.forEach(function(e){e=t(e),Array.isArray(e)?i=i.concat(e):isObject$1(e)&&i.push(e)}),e=i}else e="string"==typeof e&&e.trim()?[fixSource({src:e})]:isObject$1(e)&&"string"==typeof e.src&&e.src&&e.src.trim()?[fixSource(e)]:[];return e},fixSource=function(t){if(!t.type){var e=getMimetype(t.src);e&&(t.type=e)}return t},MediaLoader=function(t){function e(i,n,r){classCallCheck(this,e);var o=mergeOptions({createEl:!1},n),s=possibleConstructorReturn(this,t.call(this,i,o,r));if(n.playerOptions.sources&&0!==n.playerOptions.sources.length)i.src(n.playerOptions.sources);else for(var a=0,l=n.playerOptions.techOrder;a<l.length;a++){var u=toTitleCase(l[a]),c=Tech.getTech(u);if(u||(c=Component.getComponent(u)),c&&c.isSupported()){i.loadTech_(u);break}}return s}return inherits(e,t),e}(Component);Component.registerComponent("MediaLoader",MediaLoader);var ClickableComponent=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.emitTapEvents(),r.enable(),r}return inherits(e,t),e.prototype.createEl=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"div",i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};i=assign({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},i),"button"===e&&log$2.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),n=assign({role:"button","aria-live":"polite"},n),this.tabIndex_=i.tabIndex;var r=t.prototype.createEl.call(this,e,i,n);return this.createControlTextEl(r),r},e.prototype.dispose=function(){this.controlTextEl_=null,t.prototype.dispose.call(this)},e.prototype.createControlTextEl=function(t){return this.controlTextEl_=createEl("span",{className:"vjs-control-text"}),t&&t.appendChild(this.controlTextEl_),this.controlText(this.controlText_,t),this.controlTextEl_},e.prototype.controlText=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:this.el();if(t===undefined)return this.controlText_||"Need Text";var i=this.localize(t);this.controlText_=t,textContent(this.controlTextEl_,i),this.nonIconControl||e.setAttribute("title",i)},e.prototype.buildCSSClass=function(){return"vjs-control vjs-button "+t.prototype.buildCSSClass.call(this)},e.prototype.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),"undefined"!=typeof this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur))},e.prototype.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),"undefined"!=typeof this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off(["tap","click"],this.handleClick),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur)},e.prototype.handleClick=function(t){},e.prototype.handleFocus=function(t){on(document_1,"keydown",bind(this,this.handleKeyPress))},e.prototype.handleKeyPress=function(e){32===e.which||13===e.which?(e.preventDefault(),this.trigger("click")):t.prototype.handleKeyPress&&t.prototype.handleKeyPress.call(this,e)},e.prototype.handleBlur=function(t){off(document_1,"keydown",bind(this,this.handleKeyPress))},e}(Component);Component.registerComponent("ClickableComponent",ClickableComponent);var PosterImage=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.update(),i.on("posterchange",bind(r,r.update)),r}return inherits(e,t),e.prototype.dispose=function(){this.player().off("posterchange",this.update),t.prototype.dispose.call(this)},e.prototype.createEl=function(){var t=createEl("div",{className:"vjs-poster",tabIndex:-1});if(!BACKGROUND_SIZE_SUPPORTED){this.fallbackImg_=createEl("img");var e=this.player().height()||parseInt(this.player().el().style.height);this.player().width()||parseInt(this.player().el().style.width);this.fallbackImg_.onload=function(){this.width;this.height>e&&(this.style.maxHeight=e+"px",this.style.width="auto")};var i=createEl("div"),n=createEl("span");n.appendChild(this.fallbackImg_),i.appendChild(n),t.appendChild(i)}return t},e.prototype.update=function(t){var e=this.player().poster();this.setSrc(e),e?this.show():this.hide()},e.prototype.setSrc=function(t){if(this.fallbackImg_)this.fallbackImg_.src=t;else{var e="";t&&(e='url("'+t+'")'),this.el_.style.backgroundImage=e}},e.prototype.handleClick=function(t){this.player_.controls()&&(this.player_.paused()?this.player_.play():this.player_.pause())},e}(ClickableComponent);Component.registerComponent("PosterImage",PosterImage);var darkGray="#222",lightGray="#ccc",fontMap={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'},TextTrackDisplay=function(t){function e(i,n,r){classCallCheck(this,e);var o=possibleConstructorReturn(this,t.call(this,i,n,r));return i.on("loadstart",bind(o,o.toggleDisplay)),i.on("texttrackchange",bind(o,o.updateDisplay)),i.on("loadstart",bind(o,o.preselectTrack)),i.ready(bind(o,function(){if(i.tech_&&i.tech_.featuresNativeTextTracks)return void this.hide();i.on("fullscreenchange",bind(this,this.updateDisplay));for(var t=this.options_.playerOptions.tracks||[],e=0;e<t.length;e++)this.player_.addRemoteTextTrack(t[e],!0);this.preselectTrack()})),o}return inherits(e,t),e.prototype.preselectTrack=function(){for(var t={captions:1,subtitles:1},e=this.player_.textTracks(),i=this.player_.cache_.selectedLanguage,n=void 0,r=void 0,o=void 0,s=0;s<e.length;s++){var a=e[s];i&&i.enabled&&i.language===a.language?a.kind===i.kind?o=a:o||(o=a):i&&!i.enabled?(o=null,n=null,r=null):a["default"]&&("descriptions"!==a.kind||n?a.kind in t&&!r&&(r=a):n=a)}o?o.mode="showing":r?r.mode="showing":n&&(n.mode="showing")},e.prototype.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},e.prototype.clearDisplay=function(){"function"==typeof window_1.WebVTT&&window_1.WebVTT.processCues(window_1,[],this.el_)},e.prototype.updateDisplay=function(){var t=this.player_.textTracks();this.clearDisplay();for(var e=null,i=null,n=t.length;n--;){var r=t[n];"showing"===r.mode&&("descriptions"===r.kind?e=r:i=r)}i?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(i)):e&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(e))},e.prototype.updateForTrack=function(t){if("function"==typeof window_1.WebVTT&&t.activeCues){for(var e=this.player_.textTrackSettings.getValues(),i=[],n=0;n<t.activeCues.length;n++)i.push(t.activeCues[n]);window_1.WebVTT.processCues(window_1,i,this.el_);for(var r=i.length;r--;){var o=i[r];if(o){var s=o.displayState;if(e.color&&(s.firstChild.style.color=e.color),e.textOpacity&&tryUpdateStyle(s.firstChild,"color",constructColor(e.color||"#fff",e.textOpacity)),e.backgroundColor&&(s.firstChild.style.backgroundColor=e.backgroundColor),e.backgroundOpacity&&tryUpdateStyle(s.firstChild,"backgroundColor",constructColor(e.backgroundColor||"#000",e.backgroundOpacity)),e.windowColor&&(e.windowOpacity?tryUpdateStyle(s,"backgroundColor",constructColor(e.windowColor,e.windowOpacity)):s.style.backgroundColor=e.windowColor),e.edgeStyle&&("dropshadow"===e.edgeStyle?s.firstChild.style.textShadow="2px 2px 3px "+darkGray+", 2px 2px 4px "+darkGray+", 2px 2px 5px "+darkGray:"raised"===e.edgeStyle?s.firstChild.style.textShadow="1px 1px "+darkGray+", 2px 2px "+darkGray+", 3px 3px "+darkGray:"depressed"===e.edgeStyle?s.firstChild.style.textShadow="1px 1px "+lightGray+", 0 1px "+lightGray+", -1px -1px "+darkGray+", 0 -1px "+darkGray:"uniform"===e.edgeStyle&&(s.firstChild.style.textShadow="0 0 4px "+darkGray+", 0 0 4px "+darkGray+", 0 0 4px "+darkGray+", 0 0 4px "+darkGray)),e.fontPercent&&1!==e.fontPercent){var a=window_1.parseFloat(s.style.fontSize);s.style.fontSize=a*e.fontPercent+"px",s.style.height="auto",s.style.top="auto",s.style.bottom="2px"}e.fontFamily&&"default"!==e.fontFamily&&("small-caps"===e.fontFamily?s.firstChild.style.fontVariant="small-caps":s.firstChild.style.fontFamily=fontMap[e.fontFamily])}}}},e}(Component);Component.registerComponent("TextTrackDisplay",TextTrackDisplay);var LoadingSpinner=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"})},e}(Component);Component.registerComponent("LoadingSpinner",LoadingSpinner);var Button=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};t="button",e=assign({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},e),i=assign({type:"button","aria-live":"polite"},i);var n=Component.prototype.createEl.call(this,t,e,i);return this.createControlTextEl(n),n},e.prototype.addChild=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},i=this.constructor.name;return log$2.warn("Adding an actionable (user controllable) child to a Button ("+i+") is not supported; use a ClickableComponent instead."),Component.prototype.addChild.call(this,t,e)},e.prototype.enable=function(){t.prototype.enable.call(this),this.el_.removeAttribute("disabled")},e.prototype.disable=function(){t.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},e.prototype.handleKeyPress=function(e){32!==e.which&&13!==e.which&&t.prototype.handleKeyPress.call(this,e)},e}(ClickableComponent);Component.registerComponent("Button",Button);var BigPlayButton=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.mouseused_=!1,r.on("mousedown",r.handleMouseDown),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-big-play-button"},e.prototype.handleClick=function(t){var e=this.player_.play();if(!(this.mouseused_&&t.clientX&&t.clientY)){var i=this.player_.getChild("controlBar"),n=i&&i.getChild("playToggle");if(!n)return void this.player_.focus();var r=function(){return n.focus()};isPromise(e)?e.then(r,function(){}):this.setTimeout(r,1)}},e.prototype.handleKeyPress=function(e){this.mouseused_=!1,t.prototype.handleKeyPress.call(this,e)},e.prototype.handleMouseDown=function(t){this.mouseused_=!0},e}(Button);BigPlayButton.prototype.controlText_="Play Video",Component.registerComponent("BigPlayButton",BigPlayButton);var CloseButton=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.controlText(n&&n.controlText||r.localize("Close")),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-close-button "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){this.trigger({type:"close",bubbles:!1})},e}(Button);Component.registerComponent("CloseButton",CloseButton);var PlayToggle=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on(i,"play",r.handlePlay),r.on(i,"pause",r.handlePause),r.on(i,"ended",r.handleEnded),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-play-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){this.player_.paused()?this.player_.play():this.player_.pause()},e.prototype.handleSeeked=function(t){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(t):this.handlePlay(t)},e.prototype.handlePlay=function(t){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},e.prototype.handlePause=function(t){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},e.prototype.handleEnded=function(t){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},e}(Button);PlayToggle.prototype.controlText_="Play",Component.registerComponent("PlayToggle",PlayToggle);var TimeDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.throttledUpdateContent=throttle(bind(r,r.updateContent),25),r.on(i,"timeupdate",r.throttledUpdateContent),r}return inherits(e,t),e.prototype.createEl=function(e){var i=this.buildCSSClass(),n=t.prototype.createEl.call(this,"div",{className:i+" vjs-time-control vjs-control"});return this.contentEl_=createEl("div",{className:i+"-display"},{"aria-live":"off"},createEl("span",{className:"vjs-control-text",
textContent:this.localize(this.controlText_)})),this.updateTextNode_(),n.appendChild(this.contentEl_),n},e.prototype.dispose=function(){this.contentEl_=null,this.textNode_=null,t.prototype.dispose.call(this)},e.prototype.updateTextNode_=function(){if(this.contentEl_){for(;this.contentEl_.firstChild;)this.contentEl_.removeChild(this.contentEl_.firstChild);this.textNode_=document_1.createTextNode(this.formattedTime_||"0:00"),this.contentEl_.appendChild(this.textNode_)}},e.prototype.formatTime_=function(t){return formatTime(t)},e.prototype.updateFormattedTime_=function(t){var e=this.formatTime_(t);e!==this.formattedTime_&&(this.formattedTime_=e,this.requestAnimationFrame(this.updateTextNode_))},e.prototype.updateContent=function(t){},e}(Component);TimeDisplay.prototype.controlText_="Time",Component.registerComponent("TimeDisplay",TimeDisplay);var CurrentTimeDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on(i,"ended",r.handleEnded),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-current-time"},e.prototype.updateContent=function(t){var e=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();this.updateFormattedTime_(e)},e.prototype.handleEnded=function(t){this.player_.duration()&&this.updateFormattedTime_(this.player_.duration())},e}(TimeDisplay);CurrentTimeDisplay.prototype.controlText_="Current Time",Component.registerComponent("CurrentTimeDisplay",CurrentTimeDisplay);var DurationDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on(i,"durationchange",r.updateContent),r.on(i,"loadedmetadata",r.throttledUpdateContent),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-duration"},e.prototype.updateContent=function(t){var e=this.player_.duration();e&&this.duration_!==e&&(this.duration_=e,this.updateFormattedTime_(e))},e}(TimeDisplay);DurationDisplay.prototype.controlText_="Duration Time",Component.registerComponent("DurationDisplay",DurationDisplay);var TimeDivider=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"})},e}(Component);Component.registerComponent("TimeDivider",TimeDivider);var RemainingTimeDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on(i,"durationchange",r.throttledUpdateContent),r.on(i,"ended",r.handleEnded),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-remaining-time"},e.prototype.formatTime_=function(e){return"-"+t.prototype.formatTime_.call(this,e)},e.prototype.updateContent=function(t){this.player_.duration()&&(this.player_.remainingTimeDisplay?this.updateFormattedTime_(this.player_.remainingTimeDisplay()):this.updateFormattedTime_(this.player_.remainingTime()))},e.prototype.handleEnded=function(t){this.player_.duration()&&this.updateFormattedTime_(0)},e}(TimeDisplay);RemainingTimeDisplay.prototype.controlText_="Remaining Time",Component.registerComponent("RemainingTimeDisplay",RemainingTimeDisplay);var LiveDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.isLoadedMetaData=!1,r.hide(),r.on(r.player(),"durationchange",r.onDurationChange),r.on(r.player(),"loadedmetadata",r.onLoadMetadata),i.on("loadnewvideo",videojs.bind(r,r.resetStatus)),r}return inherits(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=createEl("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+"</span>"+this.localize("LIVE")},{"aria-live":"off"}),e.appendChild(this.contentEl_),e},e.prototype.dispose=function(){this.contentEl_=null,t.prototype.dispose.call(this)},e.prototype.resetStatus=function(){this.setStatus(!1)},e.prototype.onDurationChange=function(){this.isLoadedMetaData&&this.update()},e.prototype.onLoadMetadata=function(){this.setStatus(!0),this.update()},e.prototype.setStatus=function(t){this.isLoadedMetaData=t},e.prototype.update=function(){this.player().duration()===Infinity?this.show():this.hide()},e}(Component);Component.registerComponent("LiveDisplay",LiveDisplay);var Slider=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.bar=r.getChild(r.options_.barName),r.vertical(!!r.options_.vertical),r.enable(),r}return inherits(e,t),e.prototype.enabled=function(){return this.enabled_},e.prototype.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},e.prototype.disable=function(){if(this.enabled()){var t=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(t,"mousemove",this.handleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchmove",this.handleMouseMove),this.off(t,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},e.prototype.createEl=function(e){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};return i.className=i.className+" vjs-slider",i=assign({tabIndex:0},i),n=assign({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},n),t.prototype.createEl.call(this,e,i,n)},e.prototype.handleMouseDown=function(t){var e=this.bar.el_.ownerDocument;t.preventDefault(),blockTextSelection(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(e,"mousemove",this.handleMouseMove),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchmove",this.handleMouseMove),this.on(e,"touchend",this.handleMouseUp),this.handleMouseMove(t)},e.prototype.handleMouseMove=function(t){},e.prototype.handleMouseUp=function(){var t=this.bar.el_.ownerDocument;unblockTextSelection(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(t,"mousemove",this.handleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchmove",this.handleMouseMove),this.off(t,"touchend",this.handleMouseUp),this.update()},e.prototype.update=function(){if(this.el_){var t=this.getPercent(),e=this.bar;if(e){("number"!=typeof t||t!==t||t<0||t===Infinity)&&(t=0);var i=(100*t).toFixed(2)+"%",n=e.el().style;return this.vertical()?n.height=i:n.width=i,t}}},e.prototype.calculateDistance=function(t){var e=getPointerPosition(this.el_,t);return this.vertical()?e.y:e.x},e.prototype.handleFocus=function(){this.on(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},e.prototype.handleKeyPress=function(t){37===t.which||40===t.which?(t.preventDefault(),this.stepBack()):38!==t.which&&39!==t.which||(t.preventDefault(),this.stepForward())},e.prototype.handleBlur=function(){this.off(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},e.prototype.handleClick=function(t){t.stopImmediatePropagation(),t.preventDefault()},e.prototype.vertical=function(t){if(t===undefined)return this.vertical_||!1;this.vertical_=!!t,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},e}(Component);Component.registerComponent("Slider",Slider);var LoadProgressBar=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.partEls_=[],r.on(i,"progress",r.update),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-load-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Loaded")+"</span>: 0%</span>"})},e.prototype.dispose=function(){this.partEls_=null,t.prototype.dispose.call(this)},e.prototype.update=function(t){var e=this.player_.buffered(),i=this.player_.duration(),n=this.player_.bufferedEnd(),r=this.partEls_,o=function(t,e){var i=t/e||0;return 100*(i>=1?1:i)+"%"};this.el_.style.width=o(n,i);for(var s=0;s<e.length;s++){var a=e.start(s),l=e.end(s),u=r[s];u||(u=this.el_.appendChild(createEl()),r[s]=u),u.style.left=o(a,n),u.style.width=o(l-a,n)}for(var c=r.length;c>e.length;c--)this.el_.removeChild(r[c-1]);r.length=e.length},e}(Component);Component.registerComponent("LoadProgressBar",LoadProgressBar);var TimeTooltip=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"})},e.prototype.update=function(t,e,i){var n=getBoundingClientRect(this.el_),r=getBoundingClientRect(this.player_.el()),o=t.width*e;if(r&&n){var s=t.left-r.left+o,a=t.width-o+(r.right-t.right),l=n.width/2;s<l?l+=l-s:a<l&&(l=a),l<0?l=0:l>n.width&&(l=n.width),this.el_.style.right="-"+l+"px",textContent(this.el_,i)}},e}(Component);Component.registerComponent("TimeTooltip",TimeTooltip);var PlayProgressBar=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Progress")+"</span>: 0%</span>"})},e.prototype.update=function(t,e){var i=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var n=i.player_.scrubbing()?i.player_.getCache().currentTime:i.player_.currentTime(),r=formatTime(n,i.player_.duration()),o=i.getChild("timeTooltip");o&&o.update(t,e,r)})},e}(Component);PlayProgressBar.prototype.options_={children:[]},IE_VERSION&&!(IE_VERSION>8)||IS_IOS||IS_ANDROID||PlayProgressBar.prototype.options_.children.push("timeTooltip"),Component.registerComponent("PlayProgressBar",PlayProgressBar);var MouseTimeDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.update=throttle(bind(r,r.update),25),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},e.prototype.update=function(t,e){var i=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var n=i.player_.duration(),r=formatTime(e*n,n);i.el_.style.left=t.width*e+"px",i.getChild("timeTooltip").update(t,e,r)})},e}(Component);MouseTimeDisplay.prototype.options_={children:["timeTooltip"]},Component.registerComponent("MouseTimeDisplay",MouseTimeDisplay);var STEP_SECONDS=5,UPDATE_REFRESH_INTERVAL=30,SeekBar=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.update=throttle(bind(r,r.update),UPDATE_REFRESH_INTERVAL),r.on(i,"timeupdate",r.update),r.on(i,"ended",r.handleEnded),r.updateInterval=null,r.on(i,["playing"],function(){r.clearInterval(r.updateInterval),r.updateInterval=r.setInterval(function(){r.requestAnimationFrame(function(){r.update()})},UPDATE_REFRESH_INTERVAL)}),r.on(i,["ended","pause","waiting"],function(){r.clearInterval(r.updateInterval)}),r.on(i,["timeupdate","ended"],r.update),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},e.prototype.update_=function(t,e){var i=this.player_.duration();this.el_.setAttribute("aria-valuenow",(100*e).toFixed(2)),this.el_.setAttribute("aria-valuetext",this.localize("progress bar timing: currentTime={1} duration={2}",[formatTime(t,i),formatTime(i,i)],"{1} of {2}")),this.bar.update(getBoundingClientRect(this.el_),e)},e.prototype.update=function(e){var i=t.prototype.update.call(this);return this.update_(this.getCurrentTime_(),i),i},e.prototype.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},e.prototype.handleEnded=function(t){this.update_(this.player_.duration(),1)},e.prototype.getPercent=function(){var t=this.getCurrentTime_()/this.player_.duration();return t>=1?1:t},e.prototype.handleMouseDown=function(e){isSingleLeftClick(e)&&(this.player_.trigger("beforeseek"),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),t.prototype.handleMouseDown.call(this,e))},e.prototype.handleMouseMove=function(t){if(isSingleLeftClick(t)){var e=this.calculateDistance(t)*this.player_.duration();e===this.player_.duration()&&(e-=.1),this.player_.currentTime(e)}},e.prototype.enable=function(){t.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},e.prototype.disable=function(){t.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},e.prototype.handleMouseUp=function(e){t.prototype.handleMouseUp.call(this,e),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying&&silencePromise(this.player_.play())},e.prototype.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+STEP_SECONDS)},e.prototype.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-STEP_SECONDS)},e.prototype.handleAction=function(t){this.player_.paused()?this.player_.play():this.player_.pause()},e.prototype.handleKeyPress=function(e){32===e.which||13===e.which?(e.preventDefault(),this.handleAction(e)):t.prototype.handleKeyPress&&t.prototype.handleKeyPress.call(this,e)},e}(Slider);SeekBar.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},IE_VERSION&&!(IE_VERSION>8)||IS_IOS||IS_ANDROID||SeekBar.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),SeekBar.prototype.playerEvent="timeupdate",Component.registerComponent("SeekBar",SeekBar);var ProgressControl=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.handleMouseMove=throttle(bind(r,r.handleMouseMove),25),r.throttledHandleMouseSeek=throttle(bind(r,r.handleMouseSeek),25),r.enable(),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},e.prototype.handleMouseMove=function(t){var e=this.getChild("seekBar"),i=e.getChild("mouseTimeDisplay"),n=e.el(),r=getBoundingClientRect(n),o=getPointerPosition(n,t).x;o>1?o=1:o<0&&(o=0),i&&i.update(r,o)},e.prototype.handleMouseSeek=function(t){this.getChild("seekBar").handleMouseMove(t)},e.prototype.enabled=function(){return this.enabled_},e.prototype.disable=function(){this.children().forEach(function(t){return t.disable&&t.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},e.prototype.enable=function(){this.children().forEach(function(t){return t.enable&&t.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},e.prototype.handleMouseDown=function(t){var e=this.el_.ownerDocument;this.on(e,"mousemove",this.throttledHandleMouseSeek),this.on(e,"touchmove",this.throttledHandleMouseSeek),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseUp=function(t){var e=this.el_.ownerDocument;this.off(e,"mousemove",this.throttledHandleMouseSeek),this.off(e,"touchmove",this.throttledHandleMouseSeek),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchend",this.handleMouseUp)},e}(Component);ProgressControl.prototype.options_={children:["seekBar"]},Component.registerComponent("ProgressControl",ProgressControl);var FullscreenToggle=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on(i,"fullscreenchange",r.handleFullscreenChange),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-fullscreen-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleFullscreenChange=function(t){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},e.prototype.handleClick=function(t){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},e}(Button);FullscreenToggle.prototype.controlText_="Fullscreen",Component.registerComponent("FullscreenToggle",FullscreenToggle);var checkVolumeSupport=function(t,e){!e.tech_||e.tech_.featuresVolumeControl||e.tech_.featuresMuteControl?e.tech_&&e.tech_.featuresVolumeControl&&t.addClass("enable-volume-control"):t.addClass("vjs-hidden"),t.on(e,"loadstart",function(){e.tech_.featuresVolumeControl||e.tech_.featuresMuteControl?e.tech_.featuresVolumeControl&&(t.removeClass("vjs-hidden"),t.addClass("enable-volume-control")):t.addClass("vjs-hidden")})},VolumeLevel=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},e}(Component);Component.registerComponent("VolumeLevel",VolumeLevel);var VolumeBar=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on("slideractive",r.updateLastVolume_),r.on(i,"volumechange",r.updateARIAAttributes),i.ready(function(){return r.updateARIAAttributes()}),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},e.prototype.handleMouseDown=function(e){isSingleLeftClick(e)&&t.prototype.handleMouseDown.call(this,e)},e.prototype.handleMouseMove=function(t){isSingleLeftClick(t)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(t)))},e.prototype.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},e.prototype.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},e.prototype.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},e.prototype.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},e.prototype.updateARIAAttributes=function(t){var e=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",e),this.el_.setAttribute("aria-valuetext",e+"%")},e.prototype.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},e.prototype.updateLastVolume_=function(){var t=this,e=this.player_.volume();this.one("sliderinactive",function(){0===t.player_.volume()&&t.player_.lastVolume_(e)})},e}(Slider);VolumeBar.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},VolumeBar.prototype.playerEvent="volumechange",Component.registerComponent("VolumeBar",VolumeBar);var VolumeControl=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};classCallCheck(this,e),n.vertical=n.vertical||!1,("undefined"==typeof n.volumeBar||isPlain(n.volumeBar))&&(n.volumeBar=n.volumeBar||{},n.volumeBar.vertical=n.vertical);var r=possibleConstructorReturn(this,t.call(this,i,n));return checkVolumeSupport(r,i),r.throttledHandleMouseMove=throttle(bind(r,r.handleMouseMove),25),r.on("mousedown",r.handleMouseDown),r.on("touchstart",r.handleMouseDown),r.on(r.volumeBar,["focus","slideractive"],function(){r.volumeBar.addClass("vjs-slider-active"),r.addClass("vjs-slider-active"),r.trigger("slideractive")}),r.on(r.volumeBar,["blur","sliderinactive"],function(){r.volumeBar.removeClass("vjs-slider-active"),r.removeClass("vjs-slider-active"),r.trigger("sliderinactive")}),r}return inherits(e,t),e.prototype.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),t.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},e.prototype.handleMouseDown=function(t){var e=this.el_.ownerDocument;this.on(e,"mousemove",this.throttledHandleMouseMove),this.on(e,"touchmove",this.throttledHandleMouseMove),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseUp=function(t){var e=this.el_.ownerDocument;this.off(e,"mousemove",this.throttledHandleMouseMove),this.off(e,"touchmove",this.throttledHandleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseMove=function(t){this.volumeBar.handleMouseMove(t)},e}(Component);VolumeControl.prototype.options_={children:["volumeBar"]},Component.registerComponent("VolumeControl",VolumeControl);var MuteToggle=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return checkVolumeSupport(r,i),r.on(i,["loadstart","volumechange"],r.update),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-mute-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){var e=this.player_.volume(),i=this.player_.lastVolume_();if(0===e){var n=i<.1?.1:i;this.player_.volume(n),this.player_.muted(!1)}else this.player_.muted(!this.player_.muted())},e.prototype.update=function(t){this.updateIcon_(),this.updateControlText_()},e.prototype.updateIcon_=function(){var t=this.player_.volume(),e=3;0===t||this.player_.muted()?e=0:t<.33?e=1:t<.67&&(e=2);for(var i=0;i<4;i++)removeClass(this.el_,"vjs-vol-"+i);addClass(this.el_,"vjs-vol-"+e)},e.prototype.updateControlText_=function(){var t=this.player_.muted()||0===this.player_.volume(),e=t?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},e}(Button);MuteToggle.prototype.controlText_="Mute",Component.registerComponent("MuteToggle",MuteToggle);var VolumePanel=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};classCallCheck(this,e),"undefined"!=typeof n.inline?n.inline=n.inline:n.inline=!0,("undefined"==typeof n.volumeControl||isPlain(n.volumeControl))&&(n.volumeControl=n.volumeControl||{},n.volumeControl.vertical=!n.inline);var r=possibleConstructorReturn(this,t.call(this,i,n));return checkVolumeSupport(r,i),r.on(r.volumeControl,["slideractive"],r.sliderActive_),r.on(r.muteToggle,"focus",r.sliderActive_),r.on(r.volumeControl,["sliderinactive"],r.sliderInactive_),r.on(r.muteToggle,"blur",r.sliderInactive_),r}return inherits(e,t),e.prototype.sliderActive_=function(){IS_ANDROID||IS_IOS||this.addClass("vjs-slider-active")},e.prototype.sliderInactive_=function(){IS_ANDROID||IS_IOS||this.removeClass("vjs-slider-active")},e.prototype.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),t.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},e}(Component);VolumePanel.prototype.options_={children:["muteToggle","volumeControl"]},Component.registerComponent("VolumePanel",VolumePanel);var Menu=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return n&&(r.menuButton_=n.menuButton),r.focusedChild_=-1,r.on("keydown",r.handleKeyPress),r}return inherits(e,t),e.prototype.addItem=function(t){this.addChild(t),t.on("click",bind(this,function(e){this.menuButton_&&(this.menuButton_.unpressButton(),"CaptionSettingsMenuItem"!==t.name()&&this.menuButton_.focus())}))},e.prototype.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=createEl(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var i=t.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return i.appendChild(this.contentEl_),on(i,"click",function(t){t.preventDefault(),t.stopImmediatePropagation()}),i},e.prototype.dispose=function(){this.contentEl_=null,t.prototype.dispose.call(this)},e.prototype.handleKeyPress=function(t){37===t.which||40===t.which?(t.preventDefault(),this.stepForward()):38!==t.which&&39!==t.which||(t.preventDefault(),this.stepBack())},e.prototype.stepForward=function(){var t=0;this.focusedChild_!==undefined&&(t=this.focusedChild_+1),this.focus(t)},e.prototype.stepBack=function(){var t=0;this.focusedChild_!==undefined&&(t=this.focusedChild_-1),this.focus(t)},e.prototype.focus=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0,e=this.children().slice();e.length&&e[0].className&&/vjs-menu-title/.test(e[0].className)&&e.shift(),e.length>0&&(t<0?t=0:t>=e.length&&(t=e.length-1),this.focusedChild_=t,e[t].el_.focus())},e}(Component);Component.registerComponent("Menu",Menu);var MenuButton=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));r.menuButton_=new Button(i,n),r.menuButton_.controlText(r.controlText_),r.menuButton_.el_.setAttribute("aria-haspopup","true");var o=Button.prototype.buildCSSClass();return r.menuButton_.el_.className=r.buildCSSClass()+" "+o,r.menuButton_.removeClass("vjs-control"),r.addChild(r.menuButton_),r.update(),r.enabled_=!0,r.on(r.menuButton_,"tap",r.handleClick),r.on(r.menuButton_,"click",r.handleClick),r.on(r.menuButton_,"focus",r.handleFocus),r.on(r.menuButton_,"blur",r.handleBlur),r.on("keydown",r.handleSubmenuKeyPress),r}return inherits(e,t),e.prototype.update=function(){var t=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=t,this.addChild(t),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},e.prototype.createMenu=function(){var t=new Menu(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var e=createEl("li",{className:"vjs-menu-title",innerHTML:toTitleCase(this.options_.title),tabIndex:-1});this.hideThreshold_+=1,t.children_.unshift(e),prependTo(e,t.contentEl())}if(this.items=this.createItems(),this.items)for(var i=0;i<this.items.length;i++)t.addItem(this.items[i]);return t},e.prototype.createItems=function(){},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},e.prototype.buildWrapperCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+Button.prototype.buildCSSClass()+" "+t.prototype.buildCSSClass.call(this)},e.prototype.buildCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+t.prototype.buildCSSClass.call(this)},e.prototype.controlText=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:this.menuButton_.el();return this.menuButton_.controlText(t,e)},e.prototype.handleClick=function(t){this.one(this.menu.contentEl(),"mouseleave",bind(this,function(t){this.unpressButton(),this.el_.blur()})),this.buttonPressed_?this.unpressButton():this.pressButton()},e.prototype.focus=function(){this.menuButton_.focus()},e.prototype.blur=function(){this.menuButton_.blur()},e.prototype.handleFocus=function(){on(document_1,"keydown",bind(this,this.handleKeyPress))},e.prototype.handleBlur=function(){off(document_1,"keydown",bind(this,this.handleKeyPress))},e.prototype.handleKeyPress=function(t){27===t.which||9===t.which?(this.buttonPressed_&&this.unpressButton(),9!==t.which&&(t.preventDefault(),this.menuButton_.el_.focus())):38!==t.which&&40!==t.which||this.buttonPressed_||(this.pressButton(),t.preventDefault())},e.prototype.handleSubmenuKeyPress=function(t){27!==t.which&&9!==t.which||(this.buttonPressed_&&this.unpressButton(),9!==t.which&&(t.preventDefault(),this.menuButton_.el_.focus()))},e.prototype.pressButton=function(){if(this.enabled_){if(this.buttonPressed_=!0,this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),IS_IOS&&isInFrame())return;this.menu.focus()}},e.prototype.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},e.prototype.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},e.prototype.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},e}(Component);Component.registerComponent("MenuButton",MenuButton);var TrackButton=function(t){function e(i,n){classCallCheck(this,e);var r=n.tracks,o=possibleConstructorReturn(this,t.call(this,i,n));if(o.items.length<=1&&o.hide(),!r)return possibleConstructorReturn(o);var s=bind(o,o.update);return r.addEventListener("removetrack",s),r.addEventListener("addtrack",s),o.player_.on("ready",s),o.player_.on("dispose",function(){r.removeEventListener("removetrack",s),r.removeEventListener("addtrack",s)}),o}return inherits(e,t),e}(MenuButton);Component.registerComponent("TrackButton",TrackButton);var MenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.selectable=n.selectable,r.selected(n.selected),r.selectable?r.el_.setAttribute("role","menuitemcheckbox"):r.el_.setAttribute("role","menuitem"),r}return inherits(e,t),e.prototype.createEl=function(e,i,n){return this.nonIconControl=!0,t.prototype.createEl.call(this,"li",assign({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},i),n)},e.prototype.handleClick=function(t){this.selected(!0)},e.prototype.selected=function(t){this.selectable&&(t?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected")):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText("")))},e}(ClickableComponent);Component.registerComponent("MenuItem",MenuItem);var TextTrackMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=n.track,o=i.textTracks();n.label=r.label||r.language||"Unknown",n.selected="showing"===r.mode;var s=possibleConstructorReturn(this,t.call(this,i,n));s.track=r;var a=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];s.handleTracksChange.apply(s,e)},l=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];s.handleSelectedLanguageChange.apply(s,e)};if(i.on(["loadstart","texttrackchange"],a),o.addEventListener("change",a),o.addEventListener("selectedlanguagechange",l),s.on("dispose",function(){i.off(["loadstart","texttrackchange"],a),o.removeEventListener("change",a),o.removeEventListener("selectedlanguagechange",l)}),o.onchange===undefined){var u=void 0;s.on(["tap","click"],function(){if("object"!==_typeof(window_1.Event))try{u=new window_1.Event("change")}catch(t){}u||(u=document_1.createEvent("Event"),u.initEvent("change",!0,!0)),o.dispatchEvent(u)})}return s.handleTracksChange(),s}return inherits(e,t),e.prototype.handleClick=function(e){var i=this.track.kind,n=this.track.kinds,r=this.player_.textTracks();if(n||(n=[i]),t.prototype.handleClick.call(this,e),r)for(var o=0;o<r.length;o++){var s=r[o]
;s===this.track&&n.indexOf(s.kind)>-1?"showing"!==s.mode&&(s.mode="showing"):"disabled"!==s.mode&&(s.mode="disabled")}},e.prototype.handleTracksChange=function(t){this.selected("showing"===this.track.mode)},e.prototype.handleSelectedLanguageChange=function(t){if("showing"===this.track.mode){var e=this.player_.cache_.selectedLanguage;if(e&&e.enabled&&e.language===this.track.language&&e.kind!==this.track.kind)return;this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}}},e.prototype.dispose=function(){this.track=null,t.prototype.dispose.call(this)},e}(MenuItem);Component.registerComponent("TextTrackMenuItem",TextTrackMenuItem);var OffTextTrackMenuItem=function(t){function e(i,n){return classCallCheck(this,e),n.track={player:i,kind:n.kind,kinds:n.kinds,"default":!1,mode:"disabled"},n.kinds||(n.kinds=[n.kind]),n.label?n.track.label=n.label:n.track.label=n.kinds.join(" and ")+" off",n.selectable=!0,possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.handleTracksChange=function(t){for(var e=this.player().textTracks(),i=!0,n=0,r=e.length;n<r;n++){var o=e[n];if(this.options_.kinds.indexOf(o.kind)>-1&&"showing"===o.mode){i=!1;break}}this.selected(i)},e.prototype.handleSelectedLanguageChange=function(t){for(var e=this.player().textTracks(),i=!0,n=0,r=e.length;n<r;n++){var o=e[n];if(["captions","descriptions","subtitles"].indexOf(o.kind)>-1&&"showing"===o.mode){i=!1;break}}i&&(this.player_.cache_.selectedLanguage={enabled:!1})},e}(TextTrackMenuItem);Component.registerComponent("OffTextTrackMenuItem",OffTextTrackMenuItem);var TextTrackButton=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return classCallCheck(this,e),n.tracks=i.textTracks(),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.createItems=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[],e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:TextTrackMenuItem,i=void 0;this.label_&&(i=this.label_+" off"),t.push(new OffTextTrackMenuItem(this.player_,{kinds:this.kinds_,kind:this.kind_,label:i})),this.hideThreshold_+=1;var n=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var r=0;r<n.length;r++){var o=n[r];if(this.kinds_.indexOf(o.kind)>-1){var s=new e(this.player_,{track:o,selectable:!0});s.addClass("vjs-"+o.kind+"-menu-item"),t.push(s)}}return t},e}(TrackButton);Component.registerComponent("TextTrackButton",TextTrackButton);var ChaptersTrackMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=n.track,o=n.cue,s=i.currentTime();n.selectable=!0,n.label=o.text,n.selected=o.startTime<=s&&s<o.endTime;var a=possibleConstructorReturn(this,t.call(this,i,n));return a.track=r,a.cue=o,r.addEventListener("cuechange",bind(a,a.update)),a}return inherits(e,t),e.prototype.handleClick=function(e){t.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},e.prototype.update=function(t){var e=this.cue,i=this.player_.currentTime();this.selected(e.startTime<=i&&i<e.endTime)},e}(MenuItem);Component.registerComponent("ChaptersTrackMenuItem",ChaptersTrackMenuItem);var ChaptersButton=function(t){function e(i,n,r){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n,r))}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-chapters-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-chapters-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.update=function(e){this.track_&&(!e||"addtrack"!==e.type&&"removetrack"!==e.type)||this.setTrack(this.findChaptersTrack()),t.prototype.update.call(this)},e.prototype.setTrack=function(t){if(this.track_!==t){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var e=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);e&&e.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=t,this.track_){this.track_.mode="hidden";var i=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);i&&i.addEventListener("load",this.updateHandler_)}}},e.prototype.findChaptersTrack=function(){for(var t=this.player_.textTracks()||[],e=t.length-1;e>=0;e--){var i=t[e];if(i.kind===this.kind_)return i}},e.prototype.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(toTitleCase(this.kind_))},e.prototype.createMenu=function(){return this.options_.title=this.getMenuCaption(),t.prototype.createMenu.call(this)},e.prototype.createItems=function(){var t=[];if(!this.track_)return t;var e=this.track_.cues;if(!e)return t;for(var i=0,n=e.length;i<n;i++){var r=e[i],o=new ChaptersTrackMenuItem(this.player_,{track:this.track_,cue:r});t.push(o)}return t},e}(TextTrackButton);ChaptersButton.prototype.kind_="chapters",ChaptersButton.prototype.controlText_="Chapters",Component.registerComponent("ChaptersButton",ChaptersButton);var DescriptionsButton=function(t){function e(i,n,r){classCallCheck(this,e);var o=possibleConstructorReturn(this,t.call(this,i,n,r)),s=i.textTracks(),a=bind(o,o.handleTracksChange);return s.addEventListener("change",a),o.on("dispose",function(){s.removeEventListener("change",a)}),o}return inherits(e,t),e.prototype.handleTracksChange=function(t){for(var e=this.player().textTracks(),i=!1,n=0,r=e.length;n<r;n++){var o=e[n];if(o.kind!==this.kind_&&"showing"===o.mode){i=!0;break}}i?this.disable():this.enable()},e.prototype.buildCSSClass=function(){return"vjs-descriptions-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+t.prototype.buildWrapperCSSClass.call(this)},e}(TextTrackButton);DescriptionsButton.prototype.kind_="descriptions",DescriptionsButton.prototype.controlText_="Descriptions",Component.registerComponent("DescriptionsButton",DescriptionsButton);var SubtitlesButton=function(t){function e(i,n,r){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n,r))}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-subtitles-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+t.prototype.buildWrapperCSSClass.call(this)},e}(TextTrackButton);SubtitlesButton.prototype.kind_="subtitles",SubtitlesButton.prototype.controlText_="Subtitles",Component.registerComponent("SubtitlesButton",SubtitlesButton);var CaptionSettingsMenuItem=function(t){function e(i,n){classCallCheck(this,e),n.track={player:i,kind:n.kind,label:n.kind+" settings",selectable:!1,"default":!1,mode:"disabled"},n.selectable=!1,n.name="CaptionSettingsMenuItem";var r=possibleConstructorReturn(this,t.call(this,i,n));return r.addClass("vjs-texttrack-settings"),r.controlText(", opens "+n.kind+" settings dialog"),r}return inherits(e,t),e.prototype.handleClick=function(t){this.player().getChild("textTrackSettings").open()},e}(TextTrackMenuItem);Component.registerComponent("CaptionSettingsMenuItem",CaptionSettingsMenuItem);var CaptionsButton=function(t){function e(i,n,r){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n,r))}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-captions-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-captions-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||(e.push(new CaptionSettingsMenuItem(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),t.prototype.createItems.call(this,e)},e}(TextTrackButton);CaptionsButton.prototype.kind_="captions",CaptionsButton.prototype.controlText_="Captions",Component.registerComponent("CaptionsButton",CaptionsButton);var SubsCapsMenuItem=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(e,i,n){var r='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"captions"===this.options_.track.kind&&(r+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Captions")+"</span>\n      "),r+="</span>",t.prototype.createEl.call(this,e,assign({innerHTML:r},i),n)},e}(TextTrackMenuItem);Component.registerComponent("SubsCapsMenuItem",SubsCapsMenuItem);var SubsCapsButton=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.label_="subtitles",["en","en-us","en-ca","fr-ca"].indexOf(r.player_.language_)>-1&&(r.label_="captions"),r.menuButton_.controlText(toTitleCase(r.label_)),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-subs-caps-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||(e.push(new CaptionSettingsMenuItem(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=t.prototype.createItems.call(this,e,SubsCapsMenuItem)},e}(TextTrackButton);SubsCapsButton.prototype.kinds_=["captions","subtitles"],SubsCapsButton.prototype.controlText_="Subtitles",Component.registerComponent("SubsCapsButton",SubsCapsButton);var AudioTrackMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=n.track,o=i.audioTracks();n.label=r.label||r.language||"Unknown",n.selected=r.enabled;var s=possibleConstructorReturn(this,t.call(this,i,n));s.track=r;var a=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];s.handleTracksChange.apply(s,e)};return o.addEventListener("change",a),s.on("dispose",function(){o.removeEventListener("change",a)}),s}return inherits(e,t),e.prototype.handleClick=function(e){var i=this.player_.audioTracks();t.prototype.handleClick.call(this,e);for(var n=0;n<i.length;n++){var r=i[n];r.enabled=r===this.track}},e.prototype.handleTracksChange=function(t){this.selected(this.track.enabled)},e}(MenuItem);Component.registerComponent("AudioTrackMenuItem",AudioTrackMenuItem);var AudioTrackButton=function(t){function e(i){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return classCallCheck(this,e),n.tracks=i.audioTracks(),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-audio-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-audio-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];this.hideThreshold_=1;for(var e=this.player_.audioTracks(),i=0;i<e.length;i++){var n=e[i];t.push(new AudioTrackMenuItem(this.player_,{track:n,selectable:!0}))}return t},e}(TrackButton);AudioTrackButton.prototype.controlText_="Audio Track",Component.registerComponent("AudioTrackButton",AudioTrackButton);var PlaybackRateMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=n.rate,o=parseFloat(r,10);n.label=r,n.selected=1===o,n.selectable=!0;var s=possibleConstructorReturn(this,t.call(this,i,n));return s.label=r,s.rate=o,s.on(i,"ratechange",s.update),s}return inherits(e,t),e.prototype.handleClick=function(e){t.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},e.prototype.update=function(t){this.selected(this.player().playbackRate()===this.rate)},e}(MenuItem);PlaybackRateMenuItem.prototype.contentElType="button",Component.registerComponent("PlaybackRateMenuItem",PlaybackRateMenuItem);var PlaybackRateMenuButton=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.updateVisibility(),r.updateLabel(),r.on(i,"loadstart",r.updateVisibility),r.on(i,"ratechange",r.updateLabel),r}return inherits(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this);return this.labelEl_=createEl("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),e.appendChild(this.labelEl_),e},e.prototype.dispose=function(){this.labelEl_=null,t.prototype.dispose.call(this)},e.prototype.buildCSSClass=function(){return"vjs-playback-rate "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-playback-rate "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createMenu=function(){var t=new Menu(this.player()),e=this.playbackRates();if(e)for(var i=e.length-1;i>=0;i--)t.addChild(new PlaybackRateMenuItem(this.player(),{rate:e[i]+"x"}));return t},e.prototype.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},e.prototype.handleClick=function(t){for(var e=this.player().playbackRate(),i=this.playbackRates(),n=i[0],r=0;r<i.length;r++)if(i[r]>e){n=i[r];break}this.player().playbackRate(n)},e.prototype.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},e.prototype.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&this.playbackRates().length>0},e.prototype.updateVisibility=function(t){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},e.prototype.updateLabel=function(t){var e=this.player().playbackRate();if(1.1===e)return!1;this.playbackRateSupported()&&(this.labelEl_.innerHTML=e+"x")},e}(MenuButton);PlaybackRateMenuButton.prototype.controlText_="Playback Rate",Component.registerComponent("PlaybackRateMenuButton",PlaybackRateMenuButton);var Spacer=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-spacer "+t.prototype.buildCSSClass.call(this)},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},e}(Component);Component.registerComponent("Spacer",Spacer);var CustomControlSpacer=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-custom-control-spacer "+t.prototype.buildCSSClass.call(this)},e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,{className:this.buildCSSClass()});return e.innerHTML=" ",e},e}(Spacer);Component.registerComponent("CustomControlSpacer",CustomControlSpacer);var ControlBar=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"},{role:"group"})},e}(Component);if(ControlBar.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","fullscreenToggle"]},Hls&&Hls.isSupported()&&!IS_SAFARI){var controlChildren=ControlBar.prototype.options_.children;controlChildren.splice(controlChildren.length-2,1,"subsCapsButton")}Component.registerComponent("ControlBar",ControlBar);var ErrorDisplay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on(i,"error",r.open),r}return inherits(e,t),e.prototype.buildCSSClass=function(){return"vjs-error-display "+t.prototype.buildCSSClass.call(this)},e.prototype.content=function(){var t=this.player().error();return t?this.localize(t.message):""},e}(ModalDialog);ErrorDisplay.prototype.options_=mergeOptions(ModalDialog.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),Component.registerComponent("ErrorDisplay",ErrorDisplay);var LOCAL_STORAGE_KEY="vjs-text-track-settings",COLOR_BLACK=["#000","Black"],COLOR_BLUE=["#00F","Blue"],COLOR_CYAN=["#0FF","Cyan"],COLOR_GREEN=["#0F0","Green"],COLOR_MAGENTA=["#F0F","Magenta"],COLOR_RED=["#F00","Red"],COLOR_WHITE=["#FFF","White"],COLOR_YELLOW=["#FF0","Yellow"],OPACITY_OPAQUE=["1","Opaque"],OPACITY_SEMI=["0.5","Semi-Transparent"],OPACITY_TRANS=["0","Transparent"],selectConfigs={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[COLOR_BLACK,COLOR_WHITE,COLOR_RED,COLOR_GREEN,COLOR_BLUE,COLOR_YELLOW,COLOR_MAGENTA,COLOR_CYAN]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[OPACITY_OPAQUE,OPACITY_SEMI,OPACITY_TRANS]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[COLOR_WHITE,COLOR_BLACK,COLOR_RED,COLOR_GREEN,COLOR_BLUE,COLOR_YELLOW,COLOR_MAGENTA,COLOR_CYAN]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],"default":2,parser:function(t){return"1.00"===t?null:Number(t)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[OPACITY_OPAQUE,OPACITY_SEMI]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[OPACITY_TRANS,OPACITY_SEMI,OPACITY_OPAQUE]}};selectConfigs.windowColor.options=selectConfigs.backgroundColor.options;var TextTrackSettings=function(t){function e(i,n){classCallCheck(this,e),n.temporary=!1;var r=possibleConstructorReturn(this,t.call(this,i,n));return r.updateDisplay=bind(r,r.updateDisplay),r.fill(),r.hasBeenOpened_=r.hasBeenFilled_=!0,r.endDialog=createEl("p",{className:"vjs-control-text",textContent:r.localize("End of dialog window.")}),r.el().appendChild(r.endDialog),r.setDefaults(),n.persistTextTrackSettings===undefined&&(r.options_.persistTextTrackSettings=r.options_.playerOptions.persistTextTrackSettings),r.on(r.$(".vjs-done-button"),"click",function(){r.saveSettings(),r.close()}),r.on(r.$(".vjs-default-button"),"click",function(){r.setDefaults(),r.updateDisplay()}),each(selectConfigs,function(t){r.on(r.$(t.selector),"change",r.updateDisplay)}),r.options_.persistTextTrackSettings&&r.restoreSettings(),r}return inherits(e,t),e.prototype.dispose=function(){this.endDialog=null,t.prototype.dispose.call(this)},e.prototype.createElSelect_=function(t){var e=this,i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"",n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"label",r=selectConfigs[t],o=r.id.replace("%s",this.id_);return["<"+n+' id="'+o+'" class="'+("label"===n?"vjs-label":"")+'">',this.localize(r.label),"</"+n+">",'<select aria-labelledby="'+(""!==i?i+" ":"")+o+'">'].concat(r.options.map(function(t){var n=o+"-"+t[1];return['<option id="'+n+'" value="'+t[0]+'" ','aria-labelledby="'+(""!==i?i+" ":"")+o+" "+n+'">',e.localize(t[1]),"</option>"].join("")})).concat("</select>").join("")},e.prototype.createElFgColor_=function(){var t="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",t),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElBgColor_=function(){var t="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",t),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElWinColor_=function(){var t="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",t),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElColors_=function(){return createEl("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},e.prototype.createElFont_=function(){return createEl("div",{className:'vjs-track-settings-font">',innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},e.prototype.createElControls_=function(){var t=this.localize("restore all settings to the default values");return createEl("div",{className:"vjs-track-settings-controls",innerHTML:['<button class="vjs-default-button" title="'+t+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+t+"</span>","</button>",'<button class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},e.prototype.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},e.prototype.label=function(){return this.localize("Caption Settings Dialog")},e.prototype.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},e.prototype.buildCSSClass=function(){return t.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},e.prototype.getValues=function(){var t=this;return reduce(selectConfigs,function(e,i,n){var r=getSelectedOptionValue(t.$(i.selector),i.parser);return r!==undefined&&(e[n]=r),e},{})},e.prototype.setValues=function(t){var e=this;each(selectConfigs,function(i,n){setSelectedOption(e.$(i.selector),t[n],i.parser)})},e.prototype.setDefaults=function(){var t=this;each(selectConfigs,function(e){var i=e.hasOwnProperty("default")?e["default"]:0;t.$(e.selector).selectedIndex=i})},e.prototype.restoreSettings=function(){var t=void 0;try{t=JSON.parse(window_1.localStorage.getItem(LOCAL_STORAGE_KEY))}catch(e){log$2.warn(e)}t&&this.setValues(t)},e.prototype.saveSettings=function(){if(this.options_.persistTextTrackSettings){var t=this.getValues();try{Object.keys(t).length?window_1.localStorage.setItem(LOCAL_STORAGE_KEY,JSON.stringify(t)):window_1.localStorage.removeItem(LOCAL_STORAGE_KEY)}catch(e){log$2.warn(e)}}},e.prototype.updateDisplay=function(){var t=this.player_.getChild("textTrackDisplay");t&&t.updateDisplay()},e.prototype.conditionalBlur_=function(){this.previouslyActiveEl_=null,this.off(document_1,"keydown",this.handleKeyDown);var t=this.player_.controlBar,e=t&&t.subsCapsButton,i=t&&t.captionsButton;e?e.focus():i&&i.focus()},e}(ModalDialog);Component.registerComponent("TextTrackSettings",TextTrackSettings);var _templateObject$2=taggedTemplateLiteralLoose(["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."],["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."]),Html5=function(t){function e(i,n,r){classCallCheck(this,e);var o=possibleConstructorReturn(this,t.call(this,i,n,r)),s=n.source,a=!1;if(s&&(o.el_.currentSrc!==s.src||n.tag&&3===n.tag.initNetworkState_)?o.setSource(s):o.handleLateInit_(o.el_),o.el_.hasChildNodes()){for(var l=o.el_.childNodes,u=l.length,c=[];u--;){var h=l[u];"track"===h.nodeName.toLowerCase()&&(o.featuresNativeTextTracks?(o.remoteTextTrackEls().addTrackElement_(h),o.remoteTextTracks().addTrack(h.track),o.textTracks().addTrack(h.track),a||o.el_.hasAttribute("crossorigin")||!isCrossOrigin(h.src)||(a=!0)):c.push(h))}for(var p=0;p<c.length;p++)o.el_.removeChild(c[p])}return o.proxyNativeTracks_(),o.featuresNativeTextTracks&&a&&log$2.warn(tsml(_templateObject$2)),o.restoreMetadataTracksInIOSNativePlayer_(),(TOUCH_ENABLED||IS_IPHONE||IS_NATIVE_ANDROID)&&!0===n.nativeControlsForTouch&&o.setControls(!0),o.proxyWebkitFullscreen_(),o.triggerReady(),o}return inherits(e,t),e.prototype.dispose=function(){e.disposeMediaElement(this.el_),this.options_=null,t.prototype.dispose.call(this)},e.prototype.restoreMetadataTracksInIOSNativePlayer_=function(){var t=this.textTracks(),e=void 0,i=function(){e=[];for(var i=0;i<t.length;i++){var n=t[i];"metadata"===n.kind&&e.push({track:n,storedMode:n.mode})}};i(),t.addEventListener("change",i),this.on("dispose",function(){return t.removeEventListener("change",i)});var n=function r(){for(var i=0;i<e.length;i++){var n=e[i];"disabled"===n.track.mode&&n.track.mode!==n.storedMode&&(n.track.mode=n.storedMode)}t.removeEventListener("change",r)};this.on("webkitbeginfullscreen",function(){t.removeEventListener("change",i),t.removeEventListener("change",n),t.addEventListener("change",n)}),this.on("webkitendfullscreen",function(){t.removeEventListener("change",i),t.addEventListener("change",i),t.removeEventListener("change",n)})},e.prototype.proxyNativeTracks_=function(){var t=this;NORMAL.names.forEach(function(e){var i=NORMAL[e],n=t.el()[i.getterName],r=t[i.getterName]();if(t["featuresNative"+i.capitalName+"Tracks"]&&n&&n.addEventListener){var o={change:function(t){r.trigger({type:"change",target:r,currentTarget:r,srcElement:r})},addtrack:function(t){r.addTrack(t.track)},removetrack:function(t){r.removeTrack(t.track)}},s=function(){for(var t=[],e=0;e<r.length;e++){for(var i=!1,o=0;o<n.length;o++)if(n[o]===r[e]){i=!0;break}i||t.push(r[e])}for(;t.length;)r.removeTrack(t.shift())};Object.keys(o).forEach(function(e){var i=o[e];n.addEventListener(e,i),t.on("dispose",function(t){return n.removeEventListener(e,i)})}),t.on("loadstart",s),t.on("dispose",function(e){return t.off("loadstart",s)})}})},e.prototype.createEl=function(){var t=this.options_.tag;if(!t||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(t){var i=t.cloneNode(!0);t.parentNode&&t.parentNode.insertBefore(i,t),e.disposeMediaElement(t),t=i}else{t=document_1.createElement("video");var n=this.options_.tag&&getAttributes(this.options_.tag),r=mergeOptions({},n);TOUCH_ENABLED&&!0===this.options_.nativeControlsForTouch||delete r.controls,setAttributes(t,assign(r,{id:this.options_.techId,"class":"vjs-tech"}))}t.playerId=this.options_.playerId}"undefined"!=typeof this.options_.preload&&setAttribute(t,"preload",this.options_.preload);for(var o=["loop","muted","playsinline","autoplay"],s=0;s<o.length;s++){var a=o[s],l=this.options_[a];void 0!==l&&(l?setAttribute(t,a,a):removeAttribute(t,a),t[a]=l)}return t},e.prototype.handleLateInit_=function(t){if(0!==t.networkState&&3!==t.networkState){if(0===t.readyState){var e=!1,i=function(){e=!0};this.on("loadstart",i);var n=function(){e||this.trigger("loadstart")};return this.on("loadedmetadata",n),void this.ready(function(){this.off("loadstart",i),this.off("loadedmetadata",n),e||this.trigger("loadstart")})}var r=["loadstart"];r.push("loadedmetadata"),t.readyState>=2&&r.push("loadeddata"),t.readyState>=3&&r.push("canplay"),t.readyState>=4&&r.push("canplaythrough"),this.ready(function(){r.forEach(function(t){this.trigger(t)},this)})}},e.prototype.setCurrentTime=function(t){try{this.el_.currentTime=t}catch(e){log$2(e,"Video is not ready. (Video.js)")}},e.prototype.duration=function(){var t=this;if(this.el_.duration===Infinity&&IS_ANDROID&&IS_CHROME&&0===this.el_.currentTime){var e=function i(){t.el_.currentTime>0&&(t.el_.duration===Infinity&&t.trigger("durationchange"),t.off("timeupdate",i))};return this.on("timeupdate",e),NaN}return this.el_.duration||NaN},e.prototype.width=function(){return this.el_.offsetWidth},e.prototype.height=function(){return this.el_.offsetHeight},e.prototype.proxyWebkitFullscreen_=function(){var t=this;if("webkitDisplayingFullscreen"in this.el_){var e=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},i=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",e),this.trigger("fullscreenchange",{isFullscreen:!0}))};this.on("webkitbeginfullscreen",i),this.on("dispose",function(){t.off("webkitbeginfullscreen",i),t.off("webkitendfullscreen",e)})}},e.prototype.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var t=window_1.navigator&&window_1.navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1},e.prototype.enterFullScreen=function(){var t=this.el_;t.paused&&t.networkState<=t.HAVE_METADATA?(this.el_.play(),this.setTimeout(function(){t.pause(),t.webkitEnterFullScreen()},0)):t.webkitEnterFullScreen()},e.prototype.exitFullScreen=function(){this.el_.webkitExitFullScreen()},e.prototype.src=function(t){if(t===undefined)return this.el_.src;this.setSrc(t)},e.prototype.reset=function(){e.resetMediaElement(this.el_)},e.prototype.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},e.prototype.setControls=function(t){this.el_.controls=!!t},e.prototype.addTextTrack=function(e,i,n){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,i,n):t.prototype.addTextTrack.call(this,e,i,n)},e.prototype.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return t.prototype.createRemoteTextTrack.call(this,e);var i=document_1.createElement("track");return e.kind&&(i.kind=e.kind),e.label&&(i.label=e.label),(e.language||e.srclang)&&(i.srclang=e.language||e.srclang),e["default"]&&(i["default"]=e["default"]),e.id&&(i.id=e.id),e.src&&(i.src=e.src),i},e.prototype.addRemoteTextTrack=function(e,i){var n=t.prototype.addRemoteTextTrack.call(this,e,i);return this.featuresNativeTextTracks&&this.el().appendChild(n),n},e.prototype.removeRemoteTextTrack=function(e){if(t.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var i=this.$$("track"),n=i.length;n--;)e!==i[n]&&e!==i[n].track||this.el().removeChild(i[n])},e.prototype.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var t={};return"undefined"!=typeof this.el().webkitDroppedFrameCount&&"undefined"!=typeof this.el().webkitDecodedFrameCount&&(t.droppedVideoFrames=this.el().webkitDroppedFrameCount,t.totalVideoFrames=this.el().webkitDecodedFrameCount),window_1.performance&&"function"==typeof window_1.performance.now?t.creationTime=window_1.performance.now():window_1.performance&&window_1.performance.timing&&"number"==typeof window_1.performance.timing.navigationStart&&(t.creationTime=window_1.Date.now()-window_1.performance.timing.navigationStart),t},e}(Tech);if(isReal()){Html5.TEST_VID=document_1.createElement("video");var track=document_1.createElement("track");track.kind="captions",track.srclang="en",track.label="English",Html5.TEST_VID.appendChild(track)}Html5.isSupported=function(){try{Html5.TEST_VID.volume=.5}catch(t){return!1}
return!(!Html5.TEST_VID||!Html5.TEST_VID.canPlayType)},Html5.canPlayType=function(t){return Html5.TEST_VID.canPlayType(t)},Html5.canPlaySource=function(t,e){return Html5.canPlayType(t.type)},Html5.canControlVolume=function(){try{var t=Html5.TEST_VID.volume;return Html5.TEST_VID.volume=t/2+.1,t!==Html5.TEST_VID.volume}catch(e){return!1}},Html5.canControlMute=function(){try{var t=Html5.TEST_VID.muted;return Html5.TEST_VID.muted=!0,t!==Html5.TEST_VID.muted}catch(e){return!1}},Html5.canControlPlaybackRate=function(){if(IS_ANDROID&&IS_CHROME&&CHROME_VERSION<58)return!1;try{var t=Html5.TEST_VID.playbackRate;return Html5.TEST_VID.playbackRate=t/2+.1,t!==Html5.TEST_VID.playbackRate}catch(e){return!1}},Html5.supportsNativeTextTracks=function(){return IS_ANY_SAFARI},Html5.supportsNativeVideoTracks=function(){return!(!Html5.TEST_VID||!Html5.TEST_VID.videoTracks)},Html5.supportsNativeAudioTracks=function(){return!(!Html5.TEST_VID||!Html5.TEST_VID.audioTracks)},Html5.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],Html5.prototype.featuresVolumeControl=Html5.canControlVolume(),Html5.prototype.featuresMuteControl=Html5.canControlMute(),Html5.prototype.featuresPlaybackRate=Html5.canControlPlaybackRate(),Html5.prototype.movingMediaElementInDOM=!IS_IOS,Html5.prototype.featuresFullscreenResize=!0,Html5.prototype.featuresProgressEvents=!0,Html5.prototype.featuresTimeupdateEvents=!0,Html5.prototype.featuresNativeTextTracks=Html5.supportsNativeTextTracks(),Html5.prototype.featuresNativeVideoTracks=Html5.supportsNativeVideoTracks(),Html5.prototype.featuresNativeAudioTracks=Html5.supportsNativeAudioTracks();var canPlayType=Html5.TEST_VID&&Html5.TEST_VID.constructor.prototype.canPlayType,mpegurlRE=/^application\/(?:x-|vnd\.apple\.)mpegurl/i,mp4RE=/^video\/mp4/i;Html5.patchCanPlayType=function(){ANDROID_VERSION>=4&&!IS_FIREFOX?Html5.TEST_VID.constructor.prototype.canPlayType=function(t){return t&&mpegurlRE.test(t)?"maybe":canPlayType.call(this,t)}:IS_OLD_ANDROID&&(Html5.TEST_VID.constructor.prototype.canPlayType=function(t){return t&&mp4RE.test(t)?"maybe":canPlayType.call(this,t)})},Html5.unpatchCanPlayType=function(){var t=Html5.TEST_VID.constructor.prototype.canPlayType;return Html5.TEST_VID.constructor.prototype.canPlayType=canPlayType,t},Html5.patchCanPlayType(),Html5.disposeMediaElement=function(t){if(t){for(t.parentNode&&t.parentNode.removeChild(t);t.hasChildNodes();)t.removeChild(t.firstChild);t.removeAttribute("src"),"function"==typeof t.load&&function(){try{t.load()}catch(e){}}()}},Html5.resetMediaElement=function(t){if(t){for(var e=t.querySelectorAll("source"),i=e.length;i--;)t.removeChild(e[i]);t.removeAttribute("src"),"function"==typeof t.load&&function(){try{t.load()}catch(e){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(t){Html5.prototype[t]=function(){return this.el_[t]||this.el_.hasAttribute(t)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){Html5.prototype["set"+toTitleCase(t)]=function(e){this.el_[t]=e,e?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight"].forEach(function(t){Html5.prototype[t]=function(){return this.el_[t]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate"].forEach(function(t){Html5.prototype["set"+toTitleCase(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(t){Html5.prototype[t]=function(){return this.el_[t]()}}),Tech.withSourceHandlers(Html5),Html5.nativeSourceHandler={},Html5.nativeSourceHandler.canPlayType=function(t){setTimeout(function(){},2e3);try{return Html5.TEST_VID.canPlayType(t)}catch(e){return""}},Html5.nativeSourceHandler.canHandleSource=function(t,e){if(t.type)return Html5.nativeSourceHandler.canPlayType(t.type);if(t.src){var i=getFileExtension(t.src);return Html5.nativeSourceHandler.canPlayType("video/"+i)}return""},Html5.nativeSourceHandler.handleSource=function(t,e,i){e.setSrc(t.src)},Html5.nativeSourceHandler.dispose=function(){},Html5.registerSourceHandler(Html5.nativeSourceHandler),Tech.registerTech("Html5",Html5);var _templateObject$1=taggedTemplateLiteralLoose(["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "],["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "]),TECH_EVENTS_RETRIGGER=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","ratechange","resize","volumechange","texttrackchange"],Player=function(t){function e(i,n,r){if(classCallCheck(this,e),i.id=i.id||"vjs_video_"+newGUID(),n=assign(e.getTagSettings(i),n),n.initChildren=!1,n.createEl=!1,n.evented=!1,n.reportTouchActivity=!1,!n.language)if("function"==typeof i.closest){var o=i.closest("[lang]");o&&o.getAttribute&&(n.language=o.getAttribute("lang"))}else for(var s=i;s&&1===s.nodeType;){if(getAttributes(s).hasOwnProperty("lang")){n.language=s.getAttribute("lang");break}s=s.parentNode}var a=possibleConstructorReturn(this,t.call(this,null,n,r));if(a.isReady_=!1,a.hasStarted_=!1,a.userActive_=!1,!a.options_||!a.options_.techOrder||!a.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(a.tag=i,a.tagAttributes=i&&getAttributes(i),a.language(a.options_.language),n.languages){var l={};Object.getOwnPropertyNames(n.languages).forEach(function(t){l[t.toLowerCase()]=n.languages[t]}),a.languages_=l}else a.languages_=e.prototype.options_.languages;a.cache_={},a.poster_=n.poster||"",a.controls_=!!n.controls,a.cache_.lastVolume=1,i.controls=!1,i.removeAttribute("controls"),a.scrubbing_=!1,a.el_=a.createEl(),evented(a,{eventBusKey:"el_"});var u=mergeOptions(a.options_);if(n.plugins){var c=n.plugins;Object.keys(c).forEach(function(t){if("function"!=typeof this[t])throw new Error('plugin "'+t+'" does not exist');this[t](c[t])},a)}return a.options_.playerOptions=u,a.middleware_=[],a.initChildren(),a.isAudio("audio"===i.nodeName.toLowerCase()),a.controls()?a.addClass("vjs-controls-enabled"):a.addClass("vjs-controls-disabled"),a.el_.setAttribute("role","region"),a.isAudio()?a.el_.setAttribute("aria-label",a.localize("Audio Player")):a.el_.setAttribute("aria-label",a.localize("Video Player")),a.isAudio()&&a.addClass("vjs-audio"),a.flexNotSupported_()&&a.addClass("vjs-no-flex"),IS_IOS||IS_ANDROID||a.addClass("vjs-workinghover"),e.players[a.id_]=a,a.userActive(!0),a.reportUserActivity(),a.listenForUserActivity_(),a.on("fullscreenchange",a.handleFullscreenChange_),a.on("stageclick",a.handleStageClick_),a.changingSrc_=!1,a.playWaitingForReady_=!1,a.playOnLoadstart_=null,a.forceAutoplayInChrome_(),a}return inherits(e,t),e.prototype.dispose=function(){this.trigger("dispose"),this.off("dispose"),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),e.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&this.tech_.dispose(),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),t.prototype.dispose.call(this)},e.prototype.createEl=function(){var e=this.tag,i=void 0,n=this.playerElIngest_=e.parentNode&&e.parentNode.hasAttribute&&e.parentNode.hasAttribute("data-vjs-player"),r="video-js"===this.tag.tagName.toLowerCase();n?i=this.el_=e.parentNode:r||(i=this.el_=t.prototype.createEl.call(this,"div"));var o=getAttributes(e);if(r){for(i=this.el_=e,e=this.tag=document_1.createElement("video");i.children.length;)e.appendChild(i.firstChild);hasClass(i,"video-js")||addClass(i,"video-js"),i.appendChild(e),n=this.playerElIngest_=i}if(e.setAttribute("tabindex","-1"),e.removeAttribute("width"),e.removeAttribute("height"),Object.getOwnPropertyNames(o).forEach(function(t){"class"===t?(i.className+=" "+o[t],r&&(e.className+=" "+o[t])):(i.setAttribute(t,o[t]),r&&e.setAttribute(t,o[t]))}),e.playerId=e.id,e.id+="_html5_api",e.className="vjs-tech",e.player=i.player=this,this.addClass("vjs-paused"),!0!==window_1.VIDEOJS_NO_DYNAMIC_STYLE){this.styleEl_=createStyleElement("vjs-styles-dimensions");var s=$(".vjs-styles-defaults"),a=$("head");a.insertBefore(this.styleEl_,s?s.nextSibling:a.firstChild)}this.width(this.options_.width),this.height(this.options_.height),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio);for(var l=e.getElementsByTagName("a"),u=0;u<l.length;u++){var c=l.item(u);addClass(c,"vjs-hidden"),c.setAttribute("hidden","hidden")}return e.initNetworkState_=e.networkState,e.parentNode&&!n&&e.parentNode.insertBefore(i,e),prependTo(e,i),this.children_.unshift(e),this.el_.setAttribute("lang",this.language_),this.el_=i,i},e.prototype.width=function(t,e){return this.dimension("width",t,e)},e.prototype.height=function(t,e){return this.dimension("height",t,e)},e.prototype.dimension=function(t,e,i){var n=t+"_";if(e===undefined)return this[n]||0;if(""===e)return this[n]=undefined,void this.updateStyleEl_();var r=parseFloat(e);if(isNaN(r))return void log$2.error('Improper value "'+e+'" supplied for for '+t);this[n]=r,this.updateStyleEl_(),this.isReady_&&!i&&this.trigger("playerresize")},e.prototype.fluid=function(t){if(t===undefined)return!!this.fluid_;this.fluid_=!!t,t?this.addClass("vjs-fluid"):this.removeClass("vjs-fluid"),this.updateStyleEl_()},e.prototype.aspectRatio=function(t){if(t===undefined)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(t))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=t,this.fluid(!0),this.updateStyleEl_()},e.prototype.updateStyleEl_=function(){if(!0===window_1.VIDEOJS_NO_DYNAMIC_STYLE){var t="number"==typeof this.width_?this.width_:this.options_.width,e="number"==typeof this.height_?this.height_:this.options_.height,i=this.tech_&&this.tech_.el();return void(i&&(t>=0&&(i.width=t),e>=0&&(i.height=e)))}var n=void 0,r=void 0,o=void 0,s=void 0;o=this.aspectRatio_!==undefined&&"auto"!==this.aspectRatio_?this.aspectRatio_:this.videoWidth()>0?this.videoWidth()+":"+this.videoHeight():"16:9";var a=o.split(":"),l=a[1]/a[0];n=this.width_!==undefined?this.width_:this.height_!==undefined?this.height_/l:this.videoWidth()||300,r=this.height_!==undefined?this.height_:n*l,s=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(s),setTextContent(this.styleEl_,"\n      ."+s+" {\n        width: "+n+"px;\n        height: "+r+"px;\n      }\n\n      ."+s+".vjs-fluid {\n        padding-top: "+100*l+"%;\n      }\n    ")},e.prototype.loadTech_=function(t,e){var i=this;this.tech_&&this.unloadTech_();var n=toTitleCase(t),r=t.charAt(0).toLowerCase()+t.slice(1);"Html5"!==n&&this.tag&&(Tech.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=n,this.isReady_=!1;var o={source:e,nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+n+"_api",autoplay:this.options_.autoplay,playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],swf:this.options_.swf,hlsConfig:this.options_.hlsConfig,dashConfig:this.options_.dashConfig,flvConfig:this.options_.flvConfig,webrtcConfig:this.options_.webrtcConfig};ALL.names.forEach(function(t){var e=ALL[t];o[e.getterName]=i[e.privateName]}),assign(o,this.options_[n]),assign(o,this.options_[r]),assign(o,this.options_[t.toLowerCase()]),this.tag&&(o.tag=this.tag),e&&e.src===this.cache_.src&&this.cache_.currentTime>0&&(o.startTime=this.cache_.currentTime);var s=Tech.getTech(t);if(!s)throw new Error("No Tech named '"+n+"' exists! '"+n+"' should be registered using videojs.registerTech()'");this.tech_=new s(this,o),this.tech_.ready(bind(this,this.handleTechReady_),!0),textTrackConverter.jsonToTextTracks(this.textTracksJson_||[],this.tech_),TECH_EVENTS_RETRIGGER.forEach(function(t){i.on(i.tech_,t,i["handleTech"+toTitleCase(t)+"_"])}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"canplay",this.handleTechCanPlay_),this.on(this.tech_,"canplaythrough",this.handleTechCanPlayThrough_),this.on(this.tech_,"playing",this.handleTechPlaying_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"seeked",this.handleTechSeeked_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"x5videoexitfullscreen",this.handleX5ExitFullscreen_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===n&&this.tag||prependTo(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},e.prototype.unloadTech_=function(){var t=this;ALL.names.forEach(function(e){var i=ALL[e];t[i.privateName]=t[i.getterName]()}),this.textTracksJson_=textTrackConverter.textTracksToJson(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1},e.prototype.tech=function(t){return t===undefined&&log$2.warn(tsml(_templateObject$1)),this.tech_},e.prototype.addTechControlsListeners_=function(){this.tech_&&(this.removeTechControlsListeners_(),this.on(this.tech_,"mousedown",this.handleTechClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_))},e.prototype.removeTechControlsListeners_=function(){this.tech_&&(this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mousedown",this.handleTechClick_))},e.prototype.handleTechReady_=function(){if(this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_(),(this.src()||this.currentSrc())&&this.tag&&this.options_.autoplay&&this.paused())try{delete this.tag.poster}catch(t){log$2("deleting tag.poster throws in some browsers",t)}},e.prototype.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay"))},e.prototype.hasStarted=function(t){if(t===undefined)return this.hasStarted_;t!==this.hasStarted_&&(this.hasStarted_=t,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},e.prototype.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},e.prototype.handleTechWaiting_=function(){var t=this;this.addClass("vjs-waiting"),this.trigger("waiting"),this.one("timeupdate",function(){return t.removeClass("vjs-waiting")})},e.prototype.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},e.prototype.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},e.prototype.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},e.prototype.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},e.prototype.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.trigger("seeked")},e.prototype.handleTechFirstPlay_=function(){this.options_.starttime&&(log$2.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},e.prototype.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},e.prototype.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},e.prototype.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},e.prototype.handleTechClick_=function(t){isSingleLeftClick(t)&&this.controls_&&(this.paused()?this.play():this.pause())},e.prototype.handleTechTap_=function(){this.userActive(!this.userActive())},e.prototype.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},e.prototype.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},e.prototype.handleTechTouchEnd_=function(t){t.preventDefault()},e.prototype.handleFullscreenChange_=function(){IS_IOS||(this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen"))},e.prototype.handleStageClick_=function(){this.reportUserActivity()},e.prototype.toggleFullscreenClass_=function(){IS_IOS||(this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen"))},e.prototype.documentFullscreenChange_=function(t){var e=FullscreenApi;this.isFullscreen(document_1[e.fullscreenElement]),!1===this.isFullscreen()&&off(document_1,e.fullscreenchange,bind(this,this.documentFullscreenChange_)),prefixedAPI||this.trigger("fullscreenchange")},e.prototype.handleTechFullscreenChange_=function(t,e){e&&this.isFullscreen(e.isFullscreen),this.trigger("fullscreenchange")},e.prototype.handleTechError_=function(){var t=this.tech_.error();this.error(t)},e.prototype.handleTechTextData_=function(){var t=null;arguments.length>1&&(t=arguments[1]),this.trigger("textdata",t)},e.prototype.handleX5ExitFullscreen_=function(){this.removeClass("vjs-fullscreen"),this.isFullscreen_=!this.isFullscreen_},e.prototype.getCache=function(){return this.cache_},e.prototype.techCall_=function(t,e){this.ready(function(){if(t in allowedSetters)return set$1(this.middleware_,this.tech_,t,e);try{this.tech_&&this.tech_[t](e)}catch(i){throw log$2(i),i}},!0)},e.prototype.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in allowedGetters)return get$1(this.middleware_,this.tech_,t);try{return this.tech_[t]()}catch(e){if(this.tech_[t]===undefined)throw log$2("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw log$2("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw log$2(e),e}}},e.prototype.play=function(){var t=this;if(this.playOnLoadstart_&&this.off("loadstart",this.playOnLoadstart_),this.isReady_){if(!this.changingSrc_&&(this.src()||this.currentSrc()))return this.techGet_("play");this.playOnLoadstart_=function(){t.playOnLoadstart_=null,silencePromise(t.play())},this.one("loadstart",this.playOnLoadstart_)}else{if(this.playWaitingForReady_)return;this.playWaitingForReady_=!0,this.ready(function(){t.playWaitingForReady_=!1,silencePromise(t.play())})}},e.prototype.pause=function(){this.techCall_("pause")},e.prototype.paused=function(){return!1!==this.techGet_("paused")},e.prototype.played=function(){return this.techGet_("played")||createTimeRanges(0,0)},e.prototype.scrubbing=function(t){if(void 0===t)return this.scrubbing_;this.scrubbing_=!!t,t?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},e.prototype.currentTime=function(t){return void 0!==t?(t<0&&(t=0),void this.techCall_("setCurrentTime",t)):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},e.prototype.duration=function(t){if(t===undefined)return this.cache_.duration!==undefined?this.cache_.duration:NaN;t=parseFloat(t),t<=0&&(t=Infinity),t!==this.cache_.duration&&(this.cache_.duration=t,t===Infinity?this.addClass("vjs-live"):this.removeClass("vjs-live"),this.trigger("durationchange"))},e.prototype.remainingTime=function(){return this.duration()-this.currentTime()},e.prototype.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},e.prototype.buffered=function(){var t=this.techGet_("buffered");return t&&t.length||(t=createTimeRanges(0,0)),t},e.prototype.bufferedPercent=function(){return bufferedPercent(this.buffered(),this.duration())},e.prototype.bufferedEnd=function(){var t=this.buffered(),e=this.duration(),i=t.end(t.length-1);return i>e&&(i=e),i},e.prototype.volume=function(t){var e=void 0;return t!==undefined?(e=Math.max(0,Math.min(1,parseFloat(t))),this.cache_.volume=e,this.techCall_("setVolume",e),void(e>0&&this.lastVolume_(e))):(e=parseFloat(this.techGet_("volume")),isNaN(e)?1:e)},e.prototype.muted=function(t){return t!==undefined?void this.techCall_("setMuted",t):this.techGet_("muted")||!1},e.prototype.defaultMuted=function(t){return t!==undefined?this.techCall_("setDefaultMuted",t):this.techGet_("defaultMuted")||!1},e.prototype.lastVolume_=function(t){return t!==undefined&&0!==t?void(this.cache_.lastVolume=t):this.cache_.lastVolume},e.prototype.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},e.prototype.isFullscreen=function(t){return t!==undefined?(this.isFullscreen_=!!t,void this.toggleFullscreenClass_()):!!this.isFullscreen_},e.prototype.requestFullscreen=function(){var t=FullscreenApi;this.isFullscreen(!0),t.requestFullscreen?(on(document_1,t.fullscreenchange,bind(this,this.documentFullscreenChange_)),this.el_[t.requestFullscreen]()):this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):(this.enterFullWindow(),this.trigger("fullscreenchange"))},e.prototype.exitFullscreen=function(){var t=FullscreenApi;this.isFullscreen(!1),t.requestFullscreen?document_1[t.exitFullscreen]():this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):(this.exitFullWindow(),this.trigger("fullscreenchange"))},e.prototype.enterFullWindow=function(){this.isFullWindow=!0,this.docOrigOverflow=document_1.documentElement.style.overflow,on(document_1,"keydown",bind(this,this.fullWindowOnEscKey)),document_1.documentElement.style.overflow="hidden",addClass(document_1.body,"vjs-full-window"),this.trigger("enterFullWindow")},e.prototype.fullWindowOnEscKey=function(t){27===t.keyCode&&(!0===this.isFullscreen()?this.exitFullscreen():this.exitFullWindow())},e.prototype.exitFullWindow=function(){this.isFullWindow=!1,off(document_1,"keydown",this.fullWindowOnEscKey),document_1.documentElement.style.overflow=this.docOrigOverflow,removeClass(document_1.body,"vjs-full-window"),this.trigger("exitFullWindow")},e.prototype.canPlayType=function(t){for(var e=void 0,i=0,n=this.options_.techOrder;i<n.length;i++){var r=n[i],o=Tech.getTech(r);if(o||(o=Component.getComponent(r)),o){if(o.isSupported()&&(e=o.canPlayType(t)))return e}else log$2.error('The "'+r+'" tech is undefined. Skipped browser support check for that tech.')}return""},e.prototype.selectSource=function(t){var e=this,i=this.options_.techOrder.map(function(t){return[t,Tech.getTech(t)]}).filter(function(t){var e=t[0],i=t[1];return i?i.isSupported():(log$2.error('The "'+e+'" tech is undefined. Skipped browser support check for that tech.'),!1)}),n=function(t,e,i){var n=void 0;return t.some(function(t){return e.some(function(e){if(n=i(t,e))return!0})}),n},r=function(t,i){var n=t[0];if(t[1].canPlaySource(i,e.options_[n.toLowerCase()]))return{source:i,tech:n}};return(this.options_.sourceOrder?n(t,i,function(t){return function(e,i){return t(i,e)}}(r)):n(i,t,r))||!1},e.prototype.src=function(t){var e=this;if(void 0===t)return this.cache_.src||this.techGet_("src");var i=filterSource(t);if(!i.length)return void this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0);this.cache_.sources=i,this.changingSrc_=!0,this.cache_.source=i[0],setSource(this,i[0],function(t,n){if(e.middleware_=n,e.src_(t))return i.length>1?e.src(i.slice(1)):(e.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void e.triggerReady());e.changingSrc_=!1,e.cache_.src=t.src,setTech(n,e.tech_)})},e.prototype.src_=function(t){var e=this.selectSource([t]);return!e||(window_1.performance&&window_1.performance.mark("firstFrameStart"),titleCaseEquals(e.tech,this.techName_)?(this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",t):this.techCall_("src",t.src),"auto"===this.options_.preload&&this.load()},!0),!1):(this.changingSrc_=!0,this.loadTech_(e.tech,e.source),!1))},e.prototype.load=function(){this.techCall_("load")},e.prototype.reset=function(){this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset")},e.prototype.currentSources=function(){var t=this.currentSource(),e=[];return 0!==Object.keys(t).length&&e.push(t),this.cache_.sources||e},e.prototype.currentSource=function(){return this.cache_.source||{}},e.prototype.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},e.prototype.currentType=function(){return this.currentSource()&&this.currentSource().type||""},e.prototype.preload=function(t){return t!==undefined?(this.techCall_("setPreload",t),void(this.options_.preload=t)):this.techGet_("preload")},e.prototype.autoplay=function(t){return t!==undefined?(this.techCall_("setAutoplay",t),this.options_.autoplay=t,void this.ready(this.forceAutoplayInChrome_)):this.techGet_("autoplay",t)},e.prototype.forceAutoplayInChrome_=function(){this.paused()&&(this.autoplay()||this.options_.autoplay)&&IS_CHROME&&!IS_ANDROID&&this.play()},e.prototype.playsinline=function(t){return t!==undefined?(this.techCall_("setPlaysinline",t),this.options_.playsinline=t,this):this.techGet_("playsinline")},e.prototype.loop=function(t){return t!==undefined?(this.techCall_("setLoop",t),void(this.options_.loop=t)):this.techGet_("loop")},e.prototype.poster=function(t){if(t===undefined)return this.poster_;t||(t=""),this.poster_=t,this.trigger("posterchange")},e.prototype.handleTechPosterChange_=function(){!this.poster_&&this.tech_&&this.tech_.poster&&(this.poster_=this.tech_.poster()||"",this.trigger("posterchange"))},e.prototype.controls=function(t){if(t===undefined)return!!this.controls_;t=!!t,this.controls_!==t&&(this.controls_=t,this.usingNativeControls()&&this.techCall_("setControls",t),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},e.prototype.usingNativeControls=function(t){if(t===undefined)return!!this.usingNativeControls_;t=!!t,this.usingNativeControls_!==t&&(this.usingNativeControls_=t,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},e.prototype.error=function(t){return t===undefined?this.error_||null:null===t?(this.error_=t,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close())):(this.error_=new MediaError(t),this.addClass("vjs-error"),log$2.error("(CODE:"+this.error_.code+" "+MediaError.errorTypes[this.error_.code]+")",this.error_.message,this.error_),void this.trigger({type:"error",data:this.error_}))},e.prototype.reportUserActivity=function(t){this.userActivity_=!0},e.prototype.userActive=function(t){if(t===undefined)return this.userActive_;if((t=!!t)!==this.userActive_){if(this.userActive_=t,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(t){t.stopPropagation(),t.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},e.prototype.listenForUserActivity_=function(){var t=void 0,e=void 0,i=void 0,n=bind(this,this.reportUserActivity),r=function(t){t.screenX===e&&t.screenY===i||(e=t.screenX,i=t.screenY,n())},o=function(){n(),this.clearInterval(t),t=this.setInterval(n,250)},s=function(e){n(),this.clearInterval(t)};this.on("mousedown",o),this.on("mousemove",r),this.on("mouseup",s),this.on("keydown",n),this.on("keyup",n);var a=void 0;this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(a);var t=this.options_.inactivityTimeout;t<=0||(a=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},t))}},250)},e.prototype.playbackRate=function(t){return t!==undefined?void this.techCall_("setPlaybackRate",t):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("playbackRate"):1},e.prototype.defaultPlaybackRate=function(t){return t!==undefined?this.techCall_("setDefaultPlaybackRate",t):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},e.prototype.isAudio=function(t){return t!==undefined?void(this.isAudio_=!!t):!!this.isAudio_},e.prototype.addTextTrack=function(t,e,i){if(this.tech_)return this.tech_.addTextTrack(t,e,i)},e.prototype.addRemoteTextTrack=function(t,e){if(this.tech_)return this.tech_.addRemoteTextTrack(t,e)},e.prototype.removeRemoteTextTrack=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},e=t.track,i=e===undefined?arguments[0]:e;if(this.tech_)return this.tech_.removeRemoteTextTrack(i)},e.prototype.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},e.prototype.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},e.prototype.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},e.prototype.language=function(t){if(t===undefined)return this.language_;this.language_=String(t).toLowerCase()},e.prototype.languages=function(){return mergeOptions(e.prototype.options_.languages,this.languages_)},e.prototype.toJSON=function(){var t=mergeOptions(this.options_),e=t.tracks;t.tracks=[];for(var i=0;i<e.length;i++){var n=e[i];n=mergeOptions(n),n.player=undefined,t.tracks[i]=n}return t},e.prototype.createModal=function(t,e){var i=this;e=e||{},e.content=t||"";var n=new ModalDialog(this,e);return this.addChild(n),n.on("dispose",function(){i.removeChild(n)}),n.open(),n},e.getTagSettings=function(t){var e={sources:[],tracks:[]},i=getAttributes(t),n=i["data-setup"];if(hasClass(t,"vjs-fluid")&&(i.fluid=!0),null!==n){var r=tuple(n||"{}"),o=r[0],s=r[1];o&&log$2.error(o),assign(i,s)}if(assign(e,i),t.hasChildNodes())for(var a=t.childNodes,l=0,u=a.length;l<u;l++){var c=a[l],h=c.nodeName.toLowerCase();"source"===h?e.sources.push(getAttributes(c)):"track"===h&&e.tracks.push(getAttributes(c))}return e},e.prototype.flexNotSupported_=function(){var t=document_1.createElement("i")
;return!("flexBasis"in t.style||"webkitFlexBasis"in t.style||"mozFlexBasis"in t.style||"msFlexBasis"in t.style||"msFlexOrder"in t.style)},e}(Component);ALL.names.forEach(function(t){var e=ALL[t];Player.prototype[e.getterName]=function(){return this.tech_?this.tech_[e.getterName]():(this[e.privateName]=this[e.privateName]||new e.ListClass,this[e.privateName])}}),Player.players={};var navigator$1=window_1.navigator;Player.prototype.options_={techOrder:Tech.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","controlBar","errorDisplay","textTrackSettings","videoStatisticPanel"],language:navigator$1&&(navigator$1.languages&&navigator$1.languages[0]||navigator$1.userLanguage||navigator$1.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media."},["ended","seeking","seekable","networkState","readyState"].forEach(function(t){Player.prototype[t]=function(){return this.techGet_(t)}}),TECH_EVENTS_RETRIGGER.forEach(function(t){Player.prototype["handleTech"+toTitleCase(t)+"_"]=function(){return this.trigger(t)}}),Component.registerComponent("Player",Player);var BASE_PLUGIN_NAME="plugin",PLUGIN_CACHE_KEY="activePlugins_",pluginStorage={},pluginExists=function(t){return pluginStorage.hasOwnProperty(t)},getPlugin=function(t){return pluginExists(t)?pluginStorage[t]:undefined},markPluginAsActive=function(t,e){t[PLUGIN_CACHE_KEY]=t[PLUGIN_CACHE_KEY]||{},t[PLUGIN_CACHE_KEY][e]=!0},triggerSetupEvent=function(t,e,i){var n=(i?"before":"")+"pluginsetup";t.trigger(n,e),t.trigger(n+":"+e.name,e)},createBasicPlugin=function(t,e){var i=function(){triggerSetupEvent(this,{name:t,plugin:e,instance:null},!0);var i=e.apply(this,arguments);return markPluginAsActive(this,t),triggerSetupEvent(this,{name:t,plugin:e,instance:i}),i};return Object.keys(e).forEach(function(t){i[t]=e[t]}),i},createPluginFactory=function(t,e){return e.prototype.name=t,function(){triggerSetupEvent(this,{name:t,plugin:e,instance:null},!0);for(var i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];var o=new(Function.prototype.bind.apply(e,[null].concat([this].concat(n))));return this[t]=function(){return o},triggerSetupEvent(this,o.getEventHash()),o}},Plugin=function(){function t(e){if(classCallCheck(this,t),this.constructor===t)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,evented(this),delete this.trigger,stateful(this,this.constructor.defaultState),markPluginAsActive(e,this.name),this.dispose=bind(this,this.dispose),e.on("dispose",this.dispose)}return t.prototype.version=function(){return this.constructor.VERSION},t.prototype.getEventHash=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return t.name=this.name,t.plugin=this.constructor,t.instance=this,t},t.prototype.trigger=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return trigger(this.eventBusEl_,t,this.getEventHash(e))},t.prototype.handleStateChanged=function(t){},t.prototype.dispose=function(){var t=this.name,e=this.player;this.trigger("dispose"),this.off(),e.off("dispose",this.dispose),e[PLUGIN_CACHE_KEY][t]=!1,this.player=this.state=null,e[t]=createPluginFactory(t,pluginStorage[t])},t.isBasic=function(e){var i="string"==typeof e?getPlugin(e):e;return"function"==typeof i&&!t.prototype.isPrototypeOf(i.prototype)},t.registerPlugin=function(e,i){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+(void 0===e?"undefined":_typeof(e))+".");if(pluginExists(e))log$2.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(Player.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof i)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+(void 0===i?"undefined":_typeof(i))+".");return pluginStorage[e]=i,e!==BASE_PLUGIN_NAME&&(t.isBasic(i)?Player.prototype[e]=createBasicPlugin(e,i):Player.prototype[e]=createPluginFactory(e,i)),i},t.deregisterPlugin=function(t){if(t===BASE_PLUGIN_NAME)throw new Error("Cannot de-register base plugin.");pluginExists(t)&&(delete pluginStorage[t],delete Player.prototype[t])},t.getPlugins=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:Object.keys(pluginStorage),e=void 0;return t.forEach(function(t){var i=getPlugin(t);i&&(e=e||{},e[t]=i)}),e},t.getPluginVersion=function(t){var e=getPlugin(t);return e&&e.VERSION||""},t}();Plugin.getPlugin=getPlugin,Plugin.BASE_PLUGIN_NAME=BASE_PLUGIN_NAME,Plugin.registerPlugin(BASE_PLUGIN_NAME,Plugin),Player.prototype.usingPlugin=function(t){return!!this[PLUGIN_CACHE_KEY]&&!0===this[PLUGIN_CACHE_KEY][t]},Player.prototype.hasPlugin=function(t){return!!pluginExists(t)};var _inherits=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":_typeof(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(t.super_=e)},extendFn=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},i=function(){t.apply(this,arguments)},n={};"object"===(void 0===e?"undefined":_typeof(e))?(e.constructor!==Object.prototype.constructor&&(i=e.constructor),n=e):"function"==typeof e&&(i=e),_inherits(i,t);for(var r in n)n.hasOwnProperty(r)&&(i.prototype[r]=n[r]);return i};if("undefined"==typeof HTMLVideoElement&&isReal()&&(document_1.createElement("video"),document_1.createElement("audio"),document_1.createElement("track"),document_1.createElement("video-js")),videojs.hooks_={},videojs.hooks=function(t,e){return videojs.hooks_[t]=videojs.hooks_[t]||[],e&&(videojs.hooks_[t]=videojs.hooks_[t].concat(e)),videojs.hooks_[t]},videojs.hook=function(t,e){videojs.hooks(t,e)},videojs.hookOnce=function(t,e){videojs.hooks(t,[].concat(e).map(function(e){return function i(){return videojs.removeHook(t,i),e.apply(undefined,arguments)}}))},videojs.removeHook=function(t,e){var i=videojs.hooks(t).indexOf(e);return!(i<=-1)&&(videojs.hooks_[t]=videojs.hooks_[t].slice(),videojs.hooks_[t].splice(i,1),!0)},!0!==window_1.VIDEOJS_NO_DYNAMIC_STYLE&&isReal()){var style=$(".vjs-styles-defaults");if(!style){style=createStyleElement("vjs-styles-defaults");var head=$("head");head&&head.insertBefore(style,head.firstChild),setTextContent(style,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}autoSetupTimeout(1,videojs),videojs.VERSION=version,videojs.options=Player.prototype.options_,videojs.getPlayers=function(){return Player.players},videojs.players=Player.players,videojs.getComponent=Component.getComponent,videojs.registerComponent=function(t,e){Tech.isTech(e)&&log$2.warn("The "+t+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),Component.registerComponent.call(Component,t,e)},videojs.getTech=Tech.getTech,videojs.registerTech=Tech.registerTech,videojs.use=use,videojs.browser=browser,videojs.TOUCH_ENABLED=TOUCH_ENABLED,videojs.extend=extendFn,videojs.mergeOptions=mergeOptions,videojs.bind=bind,videojs.registerPlugin=Plugin.registerPlugin,videojs.plugin=function(t,e){return log$2.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),Plugin.registerPlugin(t,e)},videojs.getPlugins=Plugin.getPlugins,videojs.getPlugin=Plugin.getPlugin,videojs.getPluginVersion=Plugin.getPluginVersion,videojs.addLanguage=function(t,e){var i;return t=(""+t).toLowerCase(),videojs.options.languages=mergeOptions(videojs.options.languages,(i={},i[t]=e,i)),videojs.options.languages[t]},videojs.log=log$2,videojs.createTimeRange=videojs.createTimeRanges=createTimeRanges,videojs.formatTime=formatTime,videojs.parseUrl=parseUrl,videojs.isCrossOrigin=isCrossOrigin,videojs.EventTarget=EventTarget,videojs.on=on,videojs.one=one,videojs.off=off,videojs.trigger=trigger,videojs.xhr=xhr,videojs.TextTrack=TextTrack,videojs.AudioTrack=AudioTrack,videojs.VideoTrack=VideoTrack,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(t){videojs[t]=function(){return log$2.warn("videojs."+t+"() is deprecated; use videojs.dom."+t+"() instead"),Dom[t].apply(null,arguments)}}),videojs.computedStyle=computedStyle,videojs.dom=Dom,videojs.url=Url;var enc=createCommonjsModule(function(module,exports){!function(t,e){module.exports=e()}(self,function(){return function(){function __webpack_require__(t){var e=__webpack_module_cache__[t];if(e!==undefined)return e.exports;var i=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](i,i.exports,__webpack_require__),i.exports}var __webpack_modules__={"./node_modules/@tencent/js-armor-loader/dist/runtime.js":function node_modulesTencentJsArmorLoaderDistRuntimeJs(module){function T(t,e,i,n,r,o,s,a){function l(){return function(t,e,i){return new(Function.bind.apply(t,e))}.apply(null,arguments)}var u=!n;t=+t,e=e||[0],n=n||[[this],[{}]],r=r||{};var c,h=[],p=null;Function.prototype.bind||(c=[].slice,Function.prototype.bind=function(t){if("function"!=typeof this)throw new TypeError("bind101");var e=c.call(arguments,1),i=e.length,n=this,r=function(){},o=function(){return e.length=i,e.push.apply(e,arguments),n.apply(r.prototype.isPrototypeOf(this)?this:t,e)};return this.prototype&&(r.prototype=this.prototype),o.prototype=new r,o});var d=[function(){p=null},function(){n[n.length-2]=n[n.length-2]==n.pop()},function(){n[n.length-1]+=String.fromCharCode(e[t++])},function(){var t=n.pop();r[t]||(i[t]=i[t](),r[t]=1),n.push(i[t])},function(){n[n.length-1]=i[n[n.length-1]]},function(){n.pop()},function(){n[n.length-2]=n[n.length-2]instanceof n.pop()},function(){var i=e[t++],r=n[n.length-2-i];n[n.length-2-i]=n.pop(),n.push(r)},function(){for(var l=n.pop(),u=e[t++],c=[],h=e[t++],p=e[t++],d=[],f=0;f<h;f++)c[e[t++]]=n[e[t++]];for(f=0;f<p;f++)d[f]=e[t++];var g=function y(){var t=c.slice(0);t[0]=[this],t[1]=[arguments],t[2]=[y];for(var n=0;n<d.length&&n<arguments.length;n++)0<d[n]&&(t[d[n]]=[arguments[n]]);return T(u,e,i,t,r,o,s,a)};g.toString=function(){return l},n.push(g)},function(){n.push(~n.pop())},function(){var t,e=[];for(t in n.pop())e.push(t);n.push(e)},function(){h.push([e[t++],n.length,e[t++]])},function(){n.length=e[t++]},function(){var t=n.pop();n.push(delete t[0][t[1]])},function(){n.length-=e[t++]},function(){throw n[n.length-1]},function(){var t=n.pop();n.push(t[0][t[1]])},function(){var t=n[n.length-2],e=Object.getOwnPropertyDescriptor(t[0],t[1])||{configurable:!0,enumerable:!0};e.get=n[n.length-1],Object.defineProperty(t[0],t[1],e)},function(){var i=e[t++];n[i]=n[i]===undefined?[]:n[i]},function(){return!!p},function(){n[n.length-2]=n[n.length-2]in n.pop()},function(){var i=e[t++],r=i?n.slice(-i):[];n.length-=i,r.unshift(null),n.push(l(n.pop(),r))},function(){var i=e[t++];n[n.length-1]&&(t=i)},function(){n[n.length-2]=n[n.length-2]===n.pop()},function(){n[n.length-2]=n[n.length-2]>>n.pop()},function(){n[n.length-1]=e[t++]},function(){n.push(_typeof(n.pop()))},function(){n[n.length-2]=n[n.length-2]>=n.pop()},function(){for(var l=e[t++],u=[],c=e[t++],h=e[t++],p=[],d=0;d<c;d++)u[e[t++]]=n[e[t++]];for(d=0;d<h;d++)p[d]=e[t++];n.push(function f(){var t=u.slice(0);t[0]=[this],t[1]=[arguments],t[2]=[f];for(var n=0;n<p.length&&n<arguments.length;n++)0<p[n]&&(t[p[n]]=[arguments[n]]);return T(l,e,i,t,r,o,s,a)})},function(){var t=n[n.length-2];t[0][t[1]]=n[n.length-1]},function(){var t=n.pop(),e=n.pop();n.push([e[0][e[1]],t])},function(){n.push(null)},function(){var r=e[t++],o=r?n.slice(-r):[];n.length-=r,n.push(n.pop().apply(i,o))},function(){return!0},function(){n[n.length-2]=n[n.length-2]%n.pop()},function(){n.push([i,n.pop()])},function(){n[n.length-2]=n[n.length-2]/n.pop()},function(){var t=n.pop();r[t]||(i[t]=i[t](),r[t]=1),n.push([i,t])},function(){h.pop()},function(){n.push(!1)},function(){var i=e[t++],r=i?n.slice(-i):[];n.length-=i,r.unshift(null),i=n.pop(),n.push(l(i[0][i[1]],r))},function(){n[n.length-2]=n[n.length-2]>>>n.pop()},function(){n[n.length-2]=n[n.length-2]<=n.pop()},function(){n[n.length-2]=n[n.length-2]^n.pop()},function(){var t=n.pop();n.push([n[n.pop()][0],t])},function(){n.push("")},function(){n[n.length-2]=n[n.length-2]&n.pop()},function(){n[n.length-2]=n[n.length-2]<n.pop()},function(){n.push(n[n.length-1])},function(){n.push(n[n.pop()[0]][0])},function(){n.push(!0)},function(){n[n[n.length-2][0]][0]=n[n.length-1]},function(){n.push([e[t++]])},function(){n[n.length-2]=n[n.length-2]+n.pop()},function(){n[n[n.length-1][0]]=n[n[n.length-1][0]]===undefined?[]:n[n[n.length-1][0]]},function(){n.push(undefined)},function(){n[n.length-1].length?n.push(n[n.length-1].shift(),!0):n.push(undefined,!1)},function(){},function(){n[n.length-2]=n[n.length-2]*n.pop()},function(){n.push(!n.pop())},function(){n.push(e[t++])},function(){n[n.length-2]=n[n.length-2]<<n.pop()},function(){var t=n[n.length-2],e=Object.getOwnPropertyDescriptor(t[0],t[1])||{configurable:!0,enumerable:!0};e.set=n[n.length-1],Object.defineProperty(t[0],t[1],e)},function(){n[n.length-2]=n[n.length-2]>n.pop()},function(){n.push([n.pop(),n.pop()].reverse())},function(){n[n.length-2]=n[n.length-2]-n.pop()},function(){t=e[t++]},function(){n.push(n[e[t++]][0])},function(){n[n.length-2]=n[n.length-2]|n.pop()},function(){var i=e[t++],r=i?n.slice(-i):[];n.length-=i,i=n.pop(),n.push(i[0][i[1]].apply(i[0],r))}];for(0;;)try{for(var f=!1;!f;)f=d[e[t++]]();if(p)throw p;return u?(n.pop(),n.slice(3+T.v)):n.pop()}catch(y){var g=h.pop();if(g===undefined)throw y;p=y,t=g[0],n.length=g[1],g[2]&&(n[g[2]][0]=p)}}function arrayIndexOf(t,e,i){if("function"==typeof Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e,i);var n,r=o.length;if(0===r)return-1;var s=0|i;if(s>=r)return-1;for(n=Math.max(s>=0?s:r-Math.abs(s),0);n<r;n++)if(n in o&&o[n]===e)return n;return-1}function base64Decode(t){for(var e,i,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".split(""),r=String(t).replace(/[=]+$/,""),o=0,s=0,a="";i=r.charAt(s++);~i&&(e=o%4?64*e+i:i,o++%4)?a+=String.fromCharCode(255&e>>(-2*o&6)):0)i=arrayIndexOf(n,i);return a}T.v=0,module.exports.r=function(symbols){for(var result=[],i=0;i<symbols.length;i++)try{result.push(eval(symbols[i]))}catch(_){result.push(undefined)}return result},module.exports.d=function(t){function e(){for(;l===s;)r.push(a),l++,s=n.shift(),a=n.shift()}if("object"!=typeof t[1])return t;for(var i=t[0],n=t[1],r=[],o=base64Decode(i),s=n.shift(),a=n.shift(),l=0,u=0;u<o.length;u++){var c=o.charAt(u).charCodeAt(0);e(),r.push(c),l++}return e(),r},module.exports.g=function(t){return t.shift()[0]},module.exports.v=T},"./aes.js":function(t,e,i){"use strict";function n(t){return parseInt(t)===t}function r(t){if(!n(t.length))return!1;for(var e=0;e<t.length;e++)if(!n(t[e])||t[e]<0||t[e]>255)return!1;return!0}function o(t,e){if(t.buffer&&"Uint8Array"===t.name)return e&&(t=t.slice?t.slice():Array.prototype.slice.call(t)),t;if(Array.isArray(t)){if(!r(t))throw new Error("Array contains invalid value: "+t);return new Uint8Array(t)}if(n(t.length)&&r(t))return new Uint8Array(t);throw new Error("unsupported array-like object")}function s(t){return new Uint8Array(t)}function a(t,e,i,n,r){null==n&&null==r||(t=t.slice?t.slice(n,r):Array.prototype.slice.call(t,n,r)),e.set(t,i)}function l(t){for(var e=[],i=0;i<t.length;i+=4)e.push(t[i]<<24|t[i+1]<<16|t[i+2]<<8|t[i+3]);return e}function u(t){t=o(t,!0);var e=16-t.length%16,i=s(t.length+e);a(t,i);for(var n=t.length;n<i.length;n++)i[n]=e;return i}function c(t){if(t=o(t,!0),t.length<16)throw new Error("PKCS#7 invalid length");var e=t[t.length-1];if(e>16)throw new Error("PKCS#7 padding byte out of range");for(var i=t.length-e,n=0;n<e;n++)if(t[i+n]!==e)throw new Error("PKCS#7 invalid padding byte");var r=s(i);return a(t,r,0,0,i),r}i.r(e),i.d(e,{"default":function(){return O}});var h=function(){function t(t){var e=[],i=0;for(t=encodeURI(t);i<t.length;){var n=t.charCodeAt(i++);37===n?(e.push(parseInt(t.substr(i,2),16)),i+=2):e.push(n)}return o(e)}function e(t){for(var e=[],i=0;i<t.length;){var n=t[i];n<128?(e.push(String.fromCharCode(n)),i++):n>191&&n<224?(e.push(String.fromCharCode((31&n)<<6|63&t[i+1])),i+=2):(e.push(String.fromCharCode((15&n)<<12|(63&t[i+1])<<6|63&t[i+2])),i+=3)}return e.join("")}return{toBytes:t,fromBytes:e}}(),p=function(){function t(t){for(var e=[],i=0;i<t.length;i+=2)e.push(parseInt(t.substr(i,2),16));return e}function e(t){for(var e=[],n=0;n<t.length;n++){var r=t[n];e.push(i[(240&r)>>4]+i[15&r])}return e.join("")}var i="0123456789abcdef";return{toBytes:t,fromBytes:e}}(),d={16:10,24:12,32:14
},f=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],g=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],y=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],m=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],v=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],_=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],b=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],T=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],C=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],S=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],E=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],w=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],k=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],I=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],A=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925],R=function x(t){
if(!(this instanceof x))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:o(t,!0)}),this._prepare()};R.prototype._prepare=function(){var t=d[this.key.length];if(null==t)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var e=0;e<=t;e++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);for(var i,n=4*(t+1),r=this.key.length/4,o=l(this.key),e=0;e<r;e++)i=e>>2,this._Ke[i][e%4]=o[e],this._Kd[t-i][e%4]=o[e];for(var s,a=0,u=r;u<n;){if(s=o[r-1],o[0]^=g[s>>16&255]<<24^g[s>>8&255]<<16^g[255&s]<<8^g[s>>24&255]^f[a]<<24,a+=1,8!=r)for(var e=1;e<r;e++)o[e]^=o[e-1];else{for(var e=1;e<r/2;e++)o[e]^=o[e-1];s=o[r/2-1],o[r/2]^=g[255&s]^g[s>>8&255]<<8^g[s>>16&255]<<16^g[s>>24&255]<<24;for(var e=r/2+1;e<r;e++)o[e]^=o[e-1]}for(var c,h,e=0;e<r&&u<n;)c=u>>2,h=u%4,this._Ke[c][h]=o[e],this._Kd[t-c][h]=o[e++],u++}for(var c=1;c<t;c++)for(var h=0;h<4;h++)s=this._Kd[c][h],this._Kd[c][h]=w[s>>24&255]^k[s>>16&255]^I[s>>8&255]^A[255&s]},R.prototype.encrypt=function(t){if(16!=t.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var e=this._Ke.length-1,i=[0,0,0,0],n=l(t),r=0;r<4;r++)n[r]^=this._Ke[0][r];for(var o=1;o<e;o++){for(var r=0;r<4;r++)i[r]=m[n[r]>>24&255]^v[n[(r+1)%4]>>16&255]^_[n[(r+2)%4]>>8&255]^b[255&n[(r+3)%4]]^this._Ke[o][r];n=i.slice()}for(var a,u=s(16),r=0;r<4;r++)a=this._Ke[e][r],u[4*r]=255&(g[n[r]>>24&255]^a>>24),u[4*r+1]=255&(g[n[(r+1)%4]>>16&255]^a>>16),u[4*r+2]=255&(g[n[(r+2)%4]>>8&255]^a>>8),u[4*r+3]=255&(g[255&n[(r+3)%4]]^a);return u},R.prototype.decrypt=function(t){if(16!=t.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var e=this._Kd.length-1,i=[0,0,0,0],n=l(t),r=0;r<4;r++)n[r]^=this._Kd[0][r];for(var o=1;o<e;o++){for(var r=0;r<4;r++)i[r]=T[n[r]>>24&255]^C[n[(r+3)%4]>>16&255]^S[n[(r+2)%4]>>8&255]^E[255&n[(r+1)%4]]^this._Kd[o][r];n=i.slice()}for(var a,u=s(16),r=0;r<4;r++)a=this._Kd[e][r],u[4*r]=255&(y[n[r]>>24&255]^a>>24),u[4*r+1]=255&(y[n[(r+3)%4]>>16&255]^a>>16),u[4*r+2]=255&(y[n[(r+2)%4]>>8&255]^a>>8),u[4*r+3]=255&(y[255&n[(r+1)%4]]^a);return u};var P=function M(t,e){if(!(this instanceof M))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",e){if(16!=e.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else e=s(16);this._lastCipherblock=o(e,!0),this._aes=new R(t)};P.prototype.encrypt=function(t){if(t=o(t),t.length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var e=s(t.length),i=s(16),n=0;n<t.length;n+=16){a(t,i,0,n,n+16);for(var r=0;r<16;r++)i[r]^=this._lastCipherblock[r];this._lastCipherblock=this._aes.encrypt(i),a(this._lastCipherblock,e,n)}return e},P.prototype.decrypt=function(t){if(t=o(t),t.length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var e=s(t.length),i=s(16),n=0;n<t.length;n+=16){a(t,i,0,n,n+16),i=this._aes.decrypt(i);for(var r=0;r<16;r++)e[n+r]=i[r]^this._lastCipherblock[r];a(t,this._lastCipherblock,0,n,n+16)}return e};var j=function L(t){if(!(this instanceof L))throw Error("Counter must be instanitated with `new`");0===t||t||(t=1),"number"==typeof t?(this._counter=s(16),this.setValue(t)):this.setBytes(t)};j.prototype.setValue=function(t){if("number"!=typeof t||parseInt(t)!=t)throw new Error("invalid counter value (must be an integer)");if(t>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var e=15;e>=0;--e)this._counter[e]=t%256,t=parseInt(t/256)},j.prototype.setBytes=function(t){if(t=o(t,!0),16!=t.length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=t},j.prototype.increment=function(){for(var t=15;t>=0;t--){if(255!==this._counter[t]){this._counter[t]++;break}this._counter[t]=0}};var D={AES:R,Counter:j,modeOfOperation:{cbc:P},utils:{hex:p,utf8:h},padding:{pkcs7:{pad:u,strip:c}},_arrayTest:{coerceArray:o,createArray:s,copyArray:a}},O=D},"./gen-overlay-property.js":function(t,e,n){"use strict";function r(){var t="",e=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];for(i=0;i<32;i++){t+=e[o(0,15)]}return t}function o(t,e){return Math.floor(Math.random()*(e-t+1)+t)}n.r(e),n.d(e,{"default":function(){return s}});var s=r},"./node_modules/jsencrypt/lib/JSEncrypt.js":function(t,e,i){"use strict";i.r(e),i.d(e,{JSEncrypt:function(){return s}});var n=i("./node_modules/jsencrypt/lib/lib/jsbn/base64.js"),r=i("./node_modules/jsencrypt/lib/JSEncryptRSAKey.js"),o=i("./node_modules/jsencrypt/lib/version.json"),s=function(){function t(t){t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key,this.key=new r.JSEncryptRSAKey(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt((0,n.b64tohex)(t))}catch(e){return!1}},t.prototype.encrypt=function(t){try{return(0,n.hex2b64)(this.getKey().encrypt(t))}catch(e){return!1}},t.prototype.sign=function(t,e,i){try{return(0,n.hex2b64)(this.getKey().sign(t,e,i))}catch(r){return!1}},t.prototype.verify=function(t,e,i){try{return this.getKey().verify(t,(0,n.b64tohex)(e),i)}catch(r){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new r.JSEncryptRSAKey,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=o.version,t}()},"./node_modules/jsencrypt/lib/JSEncryptRSAKey.js":function(t,e,i){"use strict";i.r(e),i.d(e,{JSEncryptRSAKey:function(){return h}});var n=i("./node_modules/jsencrypt/lib/lib/jsbn/base64.js"),r=i("./node_modules/jsencrypt/lib/lib/asn1js/hex.js"),o=i("./node_modules/jsencrypt/lib/lib/asn1js/base64.js"),s=i("./node_modules/jsencrypt/lib/lib/asn1js/asn1.js"),a=i("./node_modules/jsencrypt/lib/lib/jsbn/rsa.js"),l=i("./node_modules/jsencrypt/lib/lib/jsbn/jsbn.js"),u=i("./node_modules/jsencrypt/lib/lib/jsrsasign/asn1-1.0.js"),c=undefined&&undefined.__extends||function(){var t=function(e,i){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(e,i)};return function(e,i){function n(){this.constructor=e}if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),h=function(t){function e(i){var n=t.call(this)||this;return i&&("string"==typeof i?n.parseKey(i):(e.hasPrivateKeyProperty(i)||e.hasPublicKeyProperty(i))&&n.parsePropertiesFrom(i)),n}return c(e,t),e.prototype.parseKey=function(t){try{var e=0,i=0,n=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/,a=n.test(t)?r.Hex.decode(t):o.Base64.unarmor(t),u=s.ASN1.decode(a);if(3===u.sub.length&&(u=u.sub[2].sub[0]),9===u.sub.length){e=u.sub[1].getHexStringValue(),this.n=(0,l.parseBigInt)(e,16),i=u.sub[2].getHexStringValue(),this.e=parseInt(i,16);var c=u.sub[3].getHexStringValue();this.d=(0,l.parseBigInt)(c,16);var h=u.sub[4].getHexStringValue();this.p=(0,l.parseBigInt)(h,16);var p=u.sub[5].getHexStringValue();this.q=(0,l.parseBigInt)(p,16);var d=u.sub[6].getHexStringValue();this.dmp1=(0,l.parseBigInt)(d,16);var f=u.sub[7].getHexStringValue();this.dmq1=(0,l.parseBigInt)(f,16);var g=u.sub[8].getHexStringValue();this.coeff=(0,l.parseBigInt)(g,16)}else{if(2!==u.sub.length)return!1;var y=u.sub[1],m=y.sub[0];e=m.sub[0].getHexStringValue(),this.n=(0,l.parseBigInt)(e,16),i=m.sub[1].getHexStringValue(),this.e=parseInt(i,16)}return!0}catch(v){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new u.KJUR.asn1.DERInteger({"int":0}),new u.KJUR.asn1.DERInteger({bigint:this.n}),new u.KJUR.asn1.DERInteger({"int":this.e}),new u.KJUR.asn1.DERInteger({bigint:this.d}),new u.KJUR.asn1.DERInteger({bigint:this.p}),new u.KJUR.asn1.DERInteger({bigint:this.q}),new u.KJUR.asn1.DERInteger({bigint:this.dmp1}),new u.KJUR.asn1.DERInteger({bigint:this.dmq1}),new u.KJUR.asn1.DERInteger({bigint:this.coeff})]};return new u.KJUR.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return(0,n.hex2b64)(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new u.KJUR.asn1.DERSequence({array:[new u.KJUR.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new u.KJUR.asn1.DERNull]}),e=new u.KJUR.asn1.DERSequence({array:[new u.KJUR.asn1.DERInteger({bigint:this.n}),new u.KJUR.asn1.DERInteger({"int":this.e})]}),i=new u.KJUR.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new u.KJUR.asn1.DERSequence({array:[t,i]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return(0,n.hex2b64)(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(e=e||64,!t)return t;var i="(.{1,"+e+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(i,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return t=t||{},t.hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return t=t||{},t.hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(a.RSAKey)},"./node_modules/jsencrypt/lib/index.js":function(t,e,i){"use strict";i.r(e),i.d(e,{JSEncrypt:function(){return n.JSEncrypt},"default":function(){return r}});var n=i("./node_modules/jsencrypt/lib/JSEncrypt.js"),r=n.JSEncrypt},"./node_modules/jsencrypt/lib/lib/asn1js/asn1.js":function(t,e,i){"use strict";function n(t,e){return t.length>e&&(t=t.substring(0,e)+o),t}i.r(e),i.d(e,{Stream:function(){return l},ASN1:function(){return u},ASN1Tag:function(){return c}});var r=i("./node_modules/jsencrypt/lib/lib/asn1js/int10.js"),o="…",s=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,a=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,l=function(){function t(e,i){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=i)}return t.prototype.get=function(t){if(t===undefined&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset "+t+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,i){for(var n="",r=t;r<e;++r)if(n+=this.hexByte(this.get(r)),!0!==i)switch(15&r){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n},t.prototype.isASCII=function(t,e){for(var i=t;i<e;++i){var n=this.get(i);if(n<32||n>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var i="",n=t;n<e;++n)i+=String.fromCharCode(this.get(n));return i},t.prototype.parseStringUTF=function(t,e){for(var i="",n=t;n<e;){var r=this.get(n++);i+=r<128?String.fromCharCode(r):r>191&&r<224?String.fromCharCode((31&r)<<6|63&this.get(n++)):String.fromCharCode((15&r)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return i},t.prototype.parseStringBMP=function(t,e){for(var i,n,r="",o=t;o<e;)i=this.get(o++),n=this.get(o++),r+=String.fromCharCode(i<<8|n);return r},t.prototype.parseTime=function(t,e,i){var n=this.parseStringISO(t,e),r=(i?s:a).exec(n);return r?(i&&(r[1]=+r[1],r[1]+=+r[1]<70?2e3:1900),n=r[1]+"-"+r[2]+"-"+r[3]+" "+r[4],r[5]&&(n+=":"+r[5],r[6]&&(n+=":"+r[6],r[7]&&(n+="."+r[7]))),r[8]&&(n+=" UTC","Z"!=r[8]&&(n+=r[8],r[9]&&(n+=":"+r[9]))),n):"Unrecognized time: "+n},t.prototype.parseInteger=function(t,e){for(var i,n=this.get(t),o=n>127,s=o?255:0,a="";n==s&&++t<e;)n=this.get(t);if(0===(i=e-t))return o?-1:0;if(i>4){for(a=n,i<<=3;0==(128&(+a^s));)a=+a<<1,--i;a="("+i+" bit)\n"}o&&(n-=256);for(var l=new r.Int10(n),u=t+1;u<e;++u)l.mulAdd(256,this.get(u));return a+l.toString()},t.prototype.parseBitString=function(t,e,i){for(var r=this.get(t),o=(e-t-1<<3)-r,s="("+o+" bit)\n",a="",l=t+1;l<e;++l){for(var u=this.get(l),c=l==e-1?r:0,h=7;h>=c;--h)a+=u>>h&1?"1":"0";if(a.length>i)return s+n(a,i)}return s+a},t.prototype.parseOctetString=function(t,e,i){if(this.isASCII(t,e))return n(this.parseStringISO(t,e),i);var r=e-t,s="("+r+" byte)\n";i/=2,r>i&&(e=t+i);for(var a=t;a<e;++a)s+=this.hexByte(this.get(a));return r>i&&(s+=o),s},t.prototype.parseOID=function(t,e,i){for(var o="",s=new r.Int10,a=0,l=t;l<e;++l){var u=this.get(l);if(s.mulAdd(128,127&u),a+=7,!(128&u)){if(""===o)if((s=s.simplify())instanceof r.Int10)s.sub(80),o="2."+s.toString();else{var c=s<80?s<40?0:1:2;o=c+"."+(s-40*c)}else o+="."+s.toString();if(o.length>i)return n(o,i);s=new r.Int10,a=0}}return a>0&&(o+=".incomplete"),o},t}(),u=function(){function t(t,e,i,n,r){if(!(n instanceof c))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=i,this.tag=n,this.sub=r}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(this.tag===undefined)return null;t===undefined&&(t=Infinity);var e=this.posContent(),i=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+i);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+i,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);case 6:return this.stream.parseOID(e,e+i,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return n(this.stream.parseStringUTF(e,e+i),t);case 18:case 19:case 20:case 21:case 22:case 26:return n(this.stream.parseStringISO(e,e+i),t);case 30:return n(this.stream.parseStringBMP(e,e+i),t);case 23:case 24:return this.stream.parseTime(e,e+i,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){t===undefined&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var i=0,n=this.sub.length;i<n;++i)e+=this.sub[i].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),i=127&e;if(i==e)return i;if(i>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===i)return null;e=0;for(var n=0;n<i;++n)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,i=2*this.length;return t.substr(e,i)},t.decode=function(e){var i;i=e instanceof l?e:new l(e,0);var n=new l(i),r=new c(i),o=t.decodeLength(i),s=i.pos,a=s-n.pos,u=null,h=function(){var e=[];if(null!==o){for(var n=s+o;i.pos<n;)e[e.length]=t.decode(i);if(i.pos!=n)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var r=t.decode(i);if(r.tag.isEOC())break;e[e.length]=r}o=s-i.pos}catch(a){throw new Error("Exception while decoding undefined length content: "+a)}return e};if(r.tagConstructed)u=h();else if(r.isUniversal()&&(3==r.tagNumber||4==r.tagNumber))try{if(3==r.tagNumber&&0!=i.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");u=h();for(var p=0;p<u.length;++p)if(u[p].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(d){u=null}if(null===u){if(null===o)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);i.pos=s+Math.abs(o)}return new t(n,a,o,r,u)},t}(),c=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){var i=new r.Int10;do{e=t.get(),i.mulAdd(128,127&e)}while(128&e);this.tagNumber=i.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}()},"./node_modules/jsencrypt/lib/lib/asn1js/base64.js":function(t,e,i){"use strict";i.r(e),i.d(e,{Base64:function(){return r}});var n,r={decode:function(t){var e;if(n===undefined){var i="= \f\n\r\t \u2028\u2029";for(n=Object.create(null),e=0;e<64;++e)n["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(n["-"]=62,n._=63,e=0;e<i.length;++e)n[i.charAt(e)]=-1}var r=[],o=0,s=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if("="==a)break;if(-1!=(a=n[a])){if(a===undefined)throw new Error("Illegal character at offset "+e);o|=a,++s>=4?(r[r.length]=o>>16,r[r.length]=o>>8&255,r[r.length]=255&o,o=0,s=0):o<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=o>>10;break;case 3:r[r.length]=o>>16,r[r.length]=o>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=r.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return r.decode(t)}}},"./node_modules/jsencrypt/lib/lib/asn1js/hex.js":function(t,e,i){"use strict";i.r(e),i.d(e,{Hex:function(){return r}});var n,r={decode:function(t){var e;if(n===undefined){var i="0123456789ABCDEF",r=" \f\n\r\t \u2028\u2029";for(n={},e=0;e<16;++e)n[i.charAt(e)]=e;for(i=i.toLowerCase(),e=10;e<16;++e)n[i.charAt(e)]=e;for(e=0;e<r.length;++e)n[r.charAt(e)]=-1}var o=[],s=0,a=0;for(e=0;e<t.length;++e){var l=t.charAt(e);if("="==l)break;if(-1!=(l=n[l])){if(l===undefined)throw new Error("Illegal character at offset "+e);s|=l,++a>=2?(o[o.length]=s,s=0,a=0):s<<=4}}if(a)throw new Error("Hex encoding incomplete: 4 bits missing");return o}}},"./node_modules/jsencrypt/lib/lib/asn1js/int10.js":function(t,e,i){"use strict";i.r(e),i.d(e,{Int10:function(){return n}});var n=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var i,n,r=this.buf,o=r.length;for(i=0;i<o;++i)n=r[i]*t+e,n<1e13?e=0:(e=0|n/1e13,n-=1e13*e),r[i]=n;e>0&&(r[i]=e)},t.prototype.sub=function(t){var e,i,n=this.buf,r=n.length;for(e=0;e<r;++e)i=n[e]-t,i<0?(i+=1e13,t=1):t=0,n[e]=i;for(;0===n[n.length-1];)n.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,i=e[e.length-1].toString(),n=e.length-2;n>=0;--n)i+=(1e13+e[n]).toString().substring(1);return i},t.prototype.valueOf=function(){for(var t=this.buf,e=0,i=t.length-1;i>=0;--i)e=1e13*e+t[i];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}()},"./node_modules/jsencrypt/lib/lib/jsbn/base64.js":function(t,e,i){"use strict";function n(t){var e,i,n="";for(e=0;e+3<=t.length;e+=3)i=parseInt(t.substring(e,e+3),16),n+=a.charAt(i>>6)+a.charAt(63&i);for(e+1==t.length?(i=parseInt(t.substring(e,e+1),16),n+=a.charAt(i<<2)):e+2==t.length&&(i=parseInt(t.substring(e,e+2),16),n+=a.charAt(i>>2)+a.charAt((3&i)<<4));(3&n.length)>0;)n+=l;return n}function r(t){var e,i="",n=0,r=0;for(e=0;e<t.length&&t.charAt(e)!=l;++e){var o=a.indexOf(t.charAt(e));o<0||(0==n?(i+=(0,s.int2char)(o>>2),r=3&o,n=1):1==n?(i+=(0,s.int2char)(r<<2|o>>4),r=15&o,n=2):2==n?(i+=(0,s.int2char)(r),i+=(0,s.int2char)(o>>2),r=3&o,n=3):(i+=(0,s.int2char)(r<<2|o>>4),i+=(0,s.int2char)(15&o),n=0))}return 1==n&&(i+=(0,s.int2char)(r<<2)),i}function o(t){var e,i=r(t),n=[];for(e=0;2*e<i.length;++e)n[e]=parseInt(i.substring(2*e,2*e+2),16);return n}i.r(e),i.d(e,{hex2b64:function(){return n},b64tohex:function(){return r},b64toBA:function(){return o}});var s=i("./node_modules/jsencrypt/lib/lib/jsbn/util.js"),a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l="="},"./node_modules/jsencrypt/lib/lib/jsbn/jsbn.js":function(t,e,i){"use strict";function n(){return new p(null)}function r(t,e){return new p(t,e)}function o(t,e){var i=b[t.charCodeAt(e)];return null==i?-1:i}function s(t){var e=n();return e.fromInt(t),e}function a(t){var e,i=1;return 0!=(e=t>>>16)&&(t=e,i+=16),0!=(e=t>>8)&&(t=e,i+=8),0!=(e=t>>4)&&(t=e,i+=4),0!=(e=t>>2)&&(t=e,i+=2),0!=(e=t>>1)&&(t=e,i+=1),i}i.r(e),i.d(e,{BigInteger:function(){return p},nbi:function(){return n},parseBigInt:function(){return r},intAt:function(){return o},nbv:function(){return s},nbits:function(){return a}});var l,u=i("./node_modules/jsencrypt/lib/lib/jsbn/util.js"),c=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],h=(1<<26)/c[c.length-1],p=function(){function t(t,e,i){null!=t&&("number"==typeof t?this.fromNumber(t,e,i):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var i,n=(1<<e)-1,r=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(i=this[s]>>a)>0&&(r=!0,o=(0,u.int2char)(i));s>=0;)a<e?(i=(this[s]&(1<<a)-1)<<e-a,i|=this[--s]>>(a+=this.DB-e)):(i=this[s]>>(a-=e)&n,a<=0&&(a+=this.DB,--s)),i>0&&(r=!0),r&&(o+=(0,u.int2char)(i));return r?o:"0"},t.prototype.negate=function(){var e=n();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var i=this.t;if(0!=(e=i-t.t))return this.s<0?-e:e;for(;--i>=0;)if(0!=(e=this[i]-t[i]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+a(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var i=n();return this.abs().divRemTo(e,null,i),this.s<0&&i.compareTo(t.ZERO)>0&&e.subTo(i,i),i},t.prototype.modPowInt=function(t,e){var i;return i=t<256||e.isEven()?new f(e):new g(e),this.exp(t,i)},t.prototype.clone=function(){var t=n();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var i,n=this.DB-t*this.DB%8,r=0;if(t-- >0)for(n<this.DB&&(i=this[t]>>n)!=(this.s&this.DM)>>n&&(e[r++]=i|this.s<<this.DB-n);t>=0;)n<8?(i=(this[t]&(1<<n)-1)<<8-n,i|=this[--t]>>(n+=this.DB-8)):(i=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&i)&&(i|=-256),0==r&&(128&this.s)!=(128&i)&&++r,(r>0||i!=this.s)&&(e[r++]=i);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=n();return this.bitwiseTo(t,u.op_and,e),e},t.prototype.or=function(t){var e=n();return this.bitwiseTo(t,u.op_or,e),e},t.prototype.xor=function(t){var e=n();return this.bitwiseTo(t,u.op_xor,e),e},t.prototype.andNot=function(t){var e=n();return this.bitwiseTo(t,u.op_andnot,e),e},t.prototype.not=function(){for(var t=n(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=n();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=n();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+(0,u.lbit)(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,i=0;i<this.t;++i)t+=(0,u.cbit)(this[i]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,u.op_or)},t.prototype.clearBit=function(t){return this.changeBit(t,u.op_andnot)},t.prototype.flipBit=function(t){return this.changeBit(t,u.op_xor)},t.prototype.add=function(t){var e=n();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=n();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=n();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=n();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=n();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=n(),i=n();return this.divRemTo(t,e,i),[e,i]},t.prototype.modPow=function(t,e){var i,r,o=t.bitLength(),l=s(1);if(o<=0)return l;i=o<18?1:o<48?3:o<144?4:o<768?5:6,r=o<8?new f(e):e.isEven()?new y(e):new g(e);var u=[],c=3,h=i-1,p=(1<<i)-1;if(u[1]=r.convert(this),i>1){var d=n();for(r.sqrTo(u[1],d);c<=p;)u[c]=n(),r.mulTo(d,u[c-2],u[c]),c+=2}var m,v,_=t.t-1,b=!0,T=n();for(o=a(t[_])-1;_>=0;){for(o>=h?m=t[_]>>o-h&p:(m=(t[_]&(1<<o+1)-1)<<h-o,_>0&&(m|=t[_-1]>>this.DB+o-h)),c=i;0==(1&m);)m>>=1,--c;if((o-=c)<0&&(o+=this.DB,--_),b)u[m].copyTo(l),b=!1;else{for(;c>1;)r.sqrTo(l,T),r.sqrTo(T,l),c-=2;c>0?r.sqrTo(l,T):(v=l,l=T,T=v),r.mulTo(T,u[m],l)}for(;_>=0&&0==(t[_]&1<<o);)r.sqrTo(l,T),v=l,l=T,T=v,--o<0&&(o=this.DB-1,--_)}return r.revert(l)},t.prototype.modInverse=function(e){var i=e.isEven();if(this.isEven()&&i||0==e.signum())return t.ZERO;for(var n=e.clone(),r=this.clone(),o=s(1),a=s(0),l=s(0),u=s(1);0!=n.signum();){for(;n.isEven();)n.rShiftTo(1,n),i?(o.isEven()&&a.isEven()||(o.addTo(this,o),a.subTo(e,a)),o.rShiftTo(1,o)):a.isEven()||a.subTo(e,a),a.rShiftTo(1,a);for(;r.isEven();)r.rShiftTo(1,r),i?(l.isEven()&&u.isEven()||(l.addTo(this,l),u.subTo(e,u)),l.rShiftTo(1,l)):u.isEven()||u.subTo(e,u),u.rShiftTo(1,u);n.compareTo(r)>=0?(n.subTo(r,n),i&&o.subTo(l,o),a.subTo(u,a)):(r.subTo(n,r),i&&l.subTo(o,l),u.subTo(a,u))}return 0!=r.compareTo(t.ONE)?t.ZERO:u.compareTo(e)>=0?u.subtract(e):u.signum()<0?(u.addTo(e,u),u.signum()<0?u.add(e):u):u},t.prototype.pow=function(t){return this.exp(t,new d)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(e.compareTo(i)<0){var n=e;e=i,i=n}var r=e.getLowestSetBit(),o=i.getLowestSetBit();if(o<0)return e;for(r<o&&(o=r),o>0&&(e.rShiftTo(o,e),i.rShiftTo(o,i));e.signum()>0;)(r=e.getLowestSetBit())>0&&e.rShiftTo(r,e),(r=i.getLowestSetBit())>0&&i.rShiftTo(r,i),e.compareTo(i)>=0?(e.subTo(i,e),e.rShiftTo(1,e)):(i.subTo(e,i),i.rShiftTo(1,i));return o>0&&i.lShiftTo(o,i),i},t.prototype.isProbablePrime=function(t){var e,i=this.abs();if(1==i.t&&i[0]<=c[c.length-1]){for(e=0;e<c.length;++e)if(i[0]==c[e])return!0;return!1}if(i.isEven())return!1;for(e=1;e<c.length;){for(var n=c[e],r=e+1;r<c.length&&n<h;)n*=c[r++];for(n=i.modInt(n);e<r;)if(n%c[e++]==0)return!1}return i.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,i){var n;if(16==i)n=4;else if(8==i)n=3;else if(256==i)n=8;else if(2==i)n=1;else if(32==i)n=5;else{if(4!=i)return void this.fromRadix(e,i);n=2}this.t=0,this.s=0;for(var r=e.length,s=!1,a=0;--r>=0;){var l=8==n?255&+e[r]:o(e,r);l<0?"-"==e.charAt(r)&&(s=!0):(s=!1,0==a?this[this.t++]=l:a+n>this.DB?(this[this.t-1]|=(l&(1<<this.DB-a)-1)<<a,this[this.t++]=l>>this.DB-a):this[this.t-1]|=l<<a,(a+=n)>=this.DB&&(a-=this.DB))}8==n&&0!=(128&+e[0])&&(this.s=-1,a>0&&(this[this.t-1]|=(1<<this.DB-a)-1<<a)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var i;for(i=this.t-1;i>=0;--i)e[i+t]=this[i];for(i=t-1;i>=0;--i)e[i]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var i=t;i<this.t;++i)e[i-t]=this[i];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var i=t%this.DB,n=this.DB-i,r=(1<<n)-1,o=Math.floor(t/this.DB),s=this.s<<i&this.DM,a=this.t-1;a>=0;--a)e[a+o+1]=this[a]>>n|s,s=(this[a]&r)<<i;for(var a=o-1;a>=0;--a)e[a]=0;e[o]=s,e.t=this.t+o+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var i=Math.floor(t/this.DB);if(i>=this.t)return void(e.t=0);var n=t%this.DB,r=this.DB-n,o=(1<<n)-1;e[0]=this[i]>>n;for(var s=i+1;s<this.t;++s)e[s-i-1]|=(this[s]&o)<<r,e[s-i]=this[s]>>n;n>0&&(e[this.t-i-1]|=(this.s&o)<<r),e.t=this.t-i,e.clamp()},t.prototype.subTo=function(t,e){for(var i=0,n=0,r=Math.min(t.t,this.t);i<r;)n+=this[i]-t[i],e[i++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;i<this.t;)n+=this[i],e[i++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;i<t.t;)n-=t[i],e[i++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[i++]=this.DV+n:n>0&&(e[i++]=n),e.t=i,e.clamp()},t.prototype.multiplyTo=function(e,i){var n=this.abs(),r=e.abs(),o=n.t;for(i.t=o+r.t;--o>=0;)i[o]=0;for(o=0;o<r.t;++o)i[o+n.t]=n.am(0,r[o],i,o,0,n.t);i.s=0,i.clamp(),
this.s!=e.s&&t.ZERO.subTo(i,i)},t.prototype.squareTo=function(t){for(var e=this.abs(),i=t.t=2*e.t;--i>=0;)t[i]=0;for(i=0;i<e.t-1;++i){var n=e.am(i,e[i],t,2*i,0,1);(t[i+e.t]+=e.am(i+1,2*e[i],t,2*i+1,n,e.t-i-1))>=e.DV&&(t[i+e.t]-=e.DV,t[i+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(i,e[i],t,2*i,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,i,r){var o=e.abs();if(!(o.t<=0)){var s=this.abs();if(s.t<o.t)return null!=i&&i.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=n());var l=n(),u=this.s,c=e.s,h=this.DB-a(o[o.t-1]);h>0?(o.lShiftTo(h,l),s.lShiftTo(h,r)):(o.copyTo(l),s.copyTo(r));var p=l.t,d=l[p-1];if(0!=d){var f=d*(1<<this.F1)+(p>1?l[p-2]>>this.F2:0),g=this.FV/f,y=(1<<this.F1)/f,m=1<<this.F2,v=r.t,_=v-p,b=null==i?n():i;for(l.dlShiftTo(_,b),r.compareTo(b)>=0&&(r[r.t++]=1,r.subTo(b,r)),t.ONE.dlShiftTo(p,b),b.subTo(l,l);l.t<p;)l[l.t++]=0;for(;--_>=0;){var T=r[--v]==d?this.DM:Math.floor(r[v]*g+(r[v-1]+m)*y);if((r[v]+=l.am(0,T,r,_,0,p))<T)for(l.dlShiftTo(_,b),r.subTo(b,r);r[v]<--T;)r.subTo(b,r)}null!=i&&(r.drShiftTo(p,i),u!=c&&t.ZERO.subTo(i,i)),r.t=p,r.clamp(),h>0&&r.rShiftTo(h,r),u<0&&t.ZERO.subTo(r,r)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return e=e*(2-(15&t)*e)&15,e=e*(2-(255&t)*e)&255,e=e*(2-((65535&t)*e&65535))&65535,e=e*(2-t*e%this.DV)%this.DV,e>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,i){if(e>4294967295||e<1)return t.ONE;var r=n(),o=n(),s=i.convert(this),l=a(e)-1;for(s.copyTo(r);--l>=0;)if(i.sqrTo(r,o),(e&1<<l)>0)i.mulTo(o,s,r);else{var u=r;r=o,o=u}return i.revert(r)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),i=Math.pow(t,e),r=s(i),o=n(),a=n(),l="";for(this.divRemTo(r,o,a);o.signum()>0;)l=(i+a.intValue()).toString(t).substr(1)+l,o.divRemTo(r,o,a);return a.intValue().toString(t)+l},t.prototype.fromRadix=function(e,i){this.fromInt(0),null==i&&(i=10);for(var n=this.chunkSize(i),r=Math.pow(i,n),s=!1,a=0,l=0,u=0;u<e.length;++u){var c=o(e,u);c<0?"-"==e.charAt(u)&&0==this.signum()&&(s=!0):(l=i*l+c,++a>=n&&(this.dMultiply(r),this.dAddOffset(l,0),a=0,l=0))}a>0&&(this.dMultiply(Math.pow(i,a)),this.dAddOffset(l,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,i,n){if("number"==typeof i)if(e<2)this.fromInt(1);else for(this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),u.op_or,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(i);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var r=[],o=7&e;r.length=1+(e>>3),i.nextBytes(r),o>0?r[0]&=(1<<o)-1:r[0]=0,this.fromString(r,256)}},t.prototype.bitwiseTo=function(t,e,i){var n,r,o=Math.min(t.t,this.t);for(n=0;n<o;++n)i[n]=e(this[n],t[n]);if(t.t<this.t){for(r=t.s&this.DM,n=o;n<this.t;++n)i[n]=e(this[n],r);i.t=this.t}else{for(r=this.s&this.DM,n=o;n<t.t;++n)i[n]=e(r,t[n]);i.t=t.t}i.s=e(this.s,t.s),i.clamp()},t.prototype.changeBit=function(e,i){var n=t.ONE.shiftLeft(e);return this.bitwiseTo(n,i,n),n},t.prototype.addTo=function(t,e){for(var i=0,n=0,r=Math.min(t.t,this.t);i<r;)n+=this[i]+t[i],e[i++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;i<this.t;)n+=this[i],e[i++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;i<t.t;)n+=t[i],e[i++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[i++]=n:n<-1&&(e[i++]=this.DV+n),e.t=i,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,i){var n=Math.min(this.t+t.t,e);for(i.s=0,i.t=n;n>0;)i[--n]=0;for(var r=i.t-this.t;n<r;++n)i[n+this.t]=this.am(0,t[n],i,n,0,this.t);for(var r=Math.min(t.t,e);n<r;++n)this.am(0,t[n],i,n,0,e-n);i.clamp()},t.prototype.multiplyUpperTo=function(t,e,i){--e;var n=i.t=this.t+t.t-e;for(i.s=0;--n>=0;)i[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)i[this.t+n-e]=this.am(e-n,t[n],i,0,0,this.t+n-e);i.clamp(),i.drShiftTo(1,i)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,i=this.s<0?t-1:0;if(this.t>0)if(0==e)i=this[0]%t;else for(var n=this.t-1;n>=0;--n)i=(e*i+this[n])%t;return i},t.prototype.millerRabin=function(e){var i=this.subtract(t.ONE),r=i.getLowestSetBit();if(r<=0)return!1;var o=i.shiftRight(r);(e=e+1>>1)>c.length&&(e=c.length);for(var s=n(),a=0;a<e;++a){s.fromInt(c[Math.floor(Math.random()*c.length)]);var l=s.modPow(o,this);if(0!=l.compareTo(t.ONE)&&0!=l.compareTo(i)){for(var u=1;u++<r&&0!=l.compareTo(i);)if(l=l.modPowInt(2,this),0==l.compareTo(t.ONE))return!1;if(0!=l.compareTo(i))return!1}}return!0},t.prototype.square=function(){var t=n();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var i=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(i.compareTo(n)<0){var r=i;i=n,n=r}var o=i.getLowestSetBit(),s=n.getLowestSetBit();if(s<0)return void e(i);o<s&&(s=o),s>0&&(i.rShiftTo(s,i),n.rShiftTo(s,n));var a=function l(){(o=i.getLowestSetBit())>0&&i.rShiftTo(o,i),(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),i.compareTo(n)>=0?(i.subTo(n,i),i.rShiftTo(1,i)):(n.subTo(i,n),n.rShiftTo(1,n)),i.signum()>0?setTimeout(l,0):(s>0&&n.lShiftTo(s,n),setTimeout(function(){e(n)},0))};setTimeout(a,10)},t.prototype.fromNumberAsync=function(e,i,n,r){if("number"==typeof i)if(e<2)this.fromInt(1);else{this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),u.op_or,this),this.isEven()&&this.dAddOffset(1,0);var o=this,s=function c(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(t.ONE.shiftLeft(e-1),o),o.isProbablePrime(i)?setTimeout(function(){r()},0):setTimeout(c,0)};setTimeout(s,0)}else{var a=[],l=7&e;a.length=1+(e>>3),i.nextBytes(a),l>0?a[0]&=(1<<l)-1:a[0]=0,this.fromString(a,256)}},t}(),d=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),f=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),g=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=n();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(p.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=n();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var i=32767&t[e],n=i*this.mpl+((i*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(i=e+this.m.t,t[i]+=this.m.am(0,n,t,e,0,this.m.t);t[i]>=t.DV;)t[i]-=t.DV,t[++i]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),y=function(){function t(t){this.m=t,this.r2=n(),this.q3=n(),p.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=n();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),m="undefined"!=typeof navigator;m&&"Microsoft Internet Explorer"==navigator.appName?(p.prototype.am=function(t,e,i,n,r,o){for(var s=32767&e,a=e>>15;--o>=0;){var l=32767&this[t],u=this[t++]>>15,c=a*l+u*s;l=s*l+((32767&c)<<15)+i[n]+(1073741823&r),r=(l>>>30)+(c>>>15)+a*u+(r>>>30),i[n++]=1073741823&l}return r},l=30):m&&"Netscape"!=navigator.appName?(p.prototype.am=function(t,e,i,n,r,o){for(;--o>=0;){var s=e*this[t++]+i[n]+r;r=Math.floor(s/67108864),i[n++]=67108863&s}return r},l=26):(p.prototype.am=function(t,e,i,n,r,o){for(var s=16383&e,a=e>>14;--o>=0;){var l=16383&this[t],u=this[t++]>>14,c=a*l+u*s;l=s*l+((16383&c)<<14)+i[n]+r,r=(l>>28)+(c>>14)+a*u,i[n++]=268435455&l}return r},l=28),p.prototype.DB=l,p.prototype.DM=(1<<l)-1,p.prototype.DV=1<<l;p.prototype.FV=Math.pow(2,52),p.prototype.F1=52-l,p.prototype.F2=2*l-52;var v,_,b=[];for(v="0".charCodeAt(0),_=0;_<=9;++_)b[v++]=_;for(v="a".charCodeAt(0),_=10;_<36;++_)b[v++]=_;for(v="A".charCodeAt(0),_=10;_<36;++_)b[v++]=_;p.ZERO=s(0),p.ONE=s(1)},"./node_modules/jsencrypt/lib/lib/jsbn/prng4.js":function(t,e,i){"use strict";function n(){return new r}i.r(e),i.d(e,{Arcfour:function(){return r},prng_newstate:function(){return n},rng_psize:function(){return o}});var r=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,i,n;for(e=0;e<256;++e)this.S[e]=e;for(i=0,e=0;e<256;++e)i=i+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[i],this.S[i]=n;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}(),o=256},"./node_modules/jsencrypt/lib/lib/jsbn/rng.js":function(t,e,i){"use strict";function n(){if(null==r){for(r=(0,s.prng_newstate)();o<s.rng_psize;){var t=Math.floor(65536*Math.random());a[o++]=255&t}for(r.init(a),o=0;o<a.length;++o)a[o]=0;o=0}return r.next()}i.r(e),i.d(e,{SecureRandom:function(){return p}});var r,o,s=i("./node_modules/jsencrypt/lib/lib/jsbn/prng4.js"),a=null;if(null==a){a=[],o=0;var l=void 0;if(window.crypto&&window.crypto.getRandomValues){var u=new Uint32Array(256);for(window.crypto.getRandomValues(u),l=0;l<u.length;++l)a[o++]=255&u[l]}var c=0,h=function d(t){if((c=c||0)>=256||o>=s.rng_psize)return void(window.removeEventListener?window.removeEventListener("mousemove",d,!1):window.detachEvent&&window.detachEvent("onmousemove",d));try{var e=t.x+t.y;a[o++]=255&e,c+=1}catch(i){}};window.addEventListener?window.addEventListener("mousemove",h,!1):window.attachEvent&&window.attachEvent("onmousemove",h)}var p=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=n()},t}()},"./node_modules/jsencrypt/lib/lib/jsbn/rsa.js":function(t,e,i){"use strict";function n(t,e){if(e<t.length+22)return null;for(var i=e-t.length-6,n="",r=0;r<i;r+=2)n+="ff";var o="0001"+n+"00"+t;return(0,l.parseBigInt)(o,16)}function r(t,e){if(e<t.length+11)return null;for(var i=[],n=t.length-1;n>=0&&e>0;){var r=t.charCodeAt(n--);r<128?i[--e]=r:r>127&&r<2048?(i[--e]=63&r|128,i[--e]=r>>6|192):(i[--e]=63&r|128,i[--e]=r>>6&63|128,i[--e]=r>>12|224)}i[--e]=0;for(var o=new u.SecureRandom,s=[];e>2;){for(s[0]=0;0==s[0];)o.nextBytes(s);i[--e]=s[0]}return i[--e]=2,i[--e]=0,new l.BigInteger(i)}function o(t,e){for(var i=t.toByteArray(),n=0;n<i.length&&0==i[n];)++n;if(i.length-n!=e-1||2!=i[n])return null;for(++n;0!=i[n];)if(++n>=i.length)return null;for(var r="";++n<i.length;){var o=255&i[n];o<128?r+=String.fromCharCode(o):o>191&&o<224?(r+=String.fromCharCode((31&o)<<6|63&i[n+1]),++n):(r+=String.fromCharCode((15&o)<<12|(63&i[n+1])<<6|63&i[n+2]),n+=2)}return r}function s(t){return h[t]||""}function a(t){for(var e in h)if(h.hasOwnProperty(e)){var i=h[e],n=i.length;if(t.substr(0,n)==i)return t.substr(n)}return t}i.r(e),i.d(e,{RSAKey:function(){return c}});var l=i("./node_modules/jsencrypt/lib/lib/jsbn/jsbn.js"),u=i("./node_modules/jsencrypt/lib/lib/jsbn/rng.js"),c=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),i=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(i)<0;)e=e.add(this.p);return e.subtract(i).multiply(this.coeff).mod(this.p).multiply(this.q).add(i)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0&&(this.n=(0,l.parseBigInt)(t,16),this.e=parseInt(e,16))},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,i=r(t,e);if(null==i)return null;var n=this.doPublic(i);if(null==n)return null;for(var o=n.toString(16),s=o.length,a=0;a<2*e-s;a++)o="0"+o;return o},t.prototype.setPrivate=function(t,e,i){null!=t&&null!=e&&t.length>0&&e.length>0&&(this.n=(0,l.parseBigInt)(t,16),this.e=parseInt(e,16),this.d=(0,l.parseBigInt)(i,16))},t.prototype.setPrivateEx=function(t,e,i,n,r,o,s,a){null!=t&&null!=e&&t.length>0&&e.length>0&&(this.n=(0,l.parseBigInt)(t,16),this.e=parseInt(e,16),this.d=(0,l.parseBigInt)(i,16),this.p=(0,l.parseBigInt)(n,16),this.q=(0,l.parseBigInt)(r,16),this.dmp1=(0,l.parseBigInt)(o,16),this.dmq1=(0,l.parseBigInt)(s,16),this.coeff=(0,l.parseBigInt)(a,16))},t.prototype.generate=function(t,e){var i=new u.SecureRandom,n=t>>1;this.e=parseInt(e,16);for(var r=new l.BigInteger(e,16);;){for(;this.p=new l.BigInteger(t-n,1,i),0!=this.p.subtract(l.BigInteger.ONE).gcd(r).compareTo(l.BigInteger.ONE)||!this.p.isProbablePrime(10););for(;this.q=new l.BigInteger(n,1,i),0!=this.q.subtract(l.BigInteger.ONE).gcd(r).compareTo(l.BigInteger.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var s=this.p.subtract(l.BigInteger.ONE),a=this.q.subtract(l.BigInteger.ONE),c=s.multiply(a);if(0==c.gcd(r).compareTo(l.BigInteger.ONE)){this.n=this.p.multiply(this.q),this.d=r.modInverse(c),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=(0,l.parseBigInt)(t,16),i=this.doPrivate(e);return null==i?null:o(i,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,i){var n=new u.SecureRandom,r=t>>1;this.e=parseInt(e,16);var o=new l.BigInteger(e,16),s=this,a=function c(){var e=function(){if(s.p.compareTo(s.q)<=0){var t=s.p;s.p=s.q,s.q=t}var e=s.p.subtract(l.BigInteger.ONE),n=s.q.subtract(l.BigInteger.ONE),r=e.multiply(n);0==r.gcd(o).compareTo(l.BigInteger.ONE)?(s.n=s.p.multiply(s.q),s.d=o.modInverse(r),s.dmp1=s.d.mod(e),s.dmq1=s.d.mod(n),s.coeff=s.q.modInverse(s.p),setTimeout(function(){i()},0)):setTimeout(c,0)},a=function h(){s.q=(0,l.nbi)(),s.q.fromNumberAsync(r,1,n,function(){s.q.subtract(l.BigInteger.ONE).gcda(o,function(t){0==t.compareTo(l.BigInteger.ONE)&&s.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(h,0)})})},u=function p(){s.p=(0,l.nbi)(),s.p.fromNumberAsync(t-r,1,n,function(){s.p.subtract(l.BigInteger.ONE).gcda(o,function(t){0==t.compareTo(l.BigInteger.ONE)&&s.p.isProbablePrime(10)?setTimeout(a,0):setTimeout(p,0)})})};setTimeout(u,0)};setTimeout(a,0)},t.prototype.sign=function(t,e,i){var r=s(i),o=r+e(t).toString(),a=n(o,this.n.bitLength()/4);if(null==a)return null;var l=this.doPrivate(a);if(null==l)return null;var u=l.toString(16);return 0==(1&u.length)?u:"0"+u},t.prototype.verify=function(t,e,i){var n=(0,l.parseBigInt)(e,16),r=this.doPublic(n);return null==r?null:a(r.toString(16).replace(/^1f+00/,""))==i(t).toString()},t}(),h={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"}},"./node_modules/jsencrypt/lib/lib/jsbn/util.js":function(t,e,i){"use strict";function n(t){return c.charAt(t)}function r(t,e){return t&e}function o(t,e){return t|e}function s(t,e){return t^e}function a(t,e){return t&~e}function l(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function u(t){for(var e=0;0!=t;)t&=t-1,++e;return e}i.r(e),i.d(e,{int2char:function(){return n},op_and:function(){return r},op_or:function(){return o},op_xor:function(){return s},op_andnot:function(){return a},lbit:function(){return l},cbit:function(){return u}});var c="0123456789abcdefghijklmnopqrstuvwxyz"},"./node_modules/jsencrypt/lib/lib/jsrsasign/asn1-1.0.js":function(t,e,i){"use strict";i.r(e),i.d(e,{KJUR:function(){return o}});var n=i("./node_modules/jsencrypt/lib/lib/jsbn/jsbn.js"),r=i("./node_modules/jsencrypt/lib/lib/jsrsasign/yahoo.js"),o={};"undefined"!=typeof o.asn1&&o.asn1||(o.asn1={}),o.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var i=e.substr(1),r=i.length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var o="",s=0;s<r;s++)o+="f";e=new n.BigInteger(o,16).xor(t).add(n.BigInteger.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=o,i=e.asn1,n=i.DERBoolean,r=i.DERInteger,s=i.DERBitString,a=i.DEROctetString,l=i.DERNull,u=i.DERObjectIdentifier,c=i.DEREnumerated,h=i.DERUTF8String,p=i.DERNumericString,d=i.DERPrintableString,f=i.DERTeletexString,g=i.DERIA5String,y=i.DERUTCTime,m=i.DERGeneralizedTime,v=i.DERSequence,_=i.DERSet,b=i.DERTaggedObject,T=i.ASN1Util.newObject,C=Object.keys(t);if(1!=C.length)throw"key of param shall be only one.";var S=C[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+S+":"))throw"undefined key: "+S;if("bool"==S)return new n(t[S]);if("int"==S)return new r(t[S]);if("bitstr"==S)return new s(t[S]);if("octstr"==S)return new a(t[S]);if("null"==S)return new l(t[S]);if("oid"==S)return new u(t[S]);if("enum"==S)return new c(t[S]);if("utf8str"==S)return new h(t[S]);if("numstr"==S)return new p(t[S]);if("prnstr"==S)return new d(t[S]);if("telstr"==S)return new f(t[S]);if("ia5str"==S)return new g(t[S]);if("utctime"==S)return new y(t[S]);if("gentime"==S)return new m(t[S]);if("seq"==S){for(var E=t[S],w=[],k=0;k<E.length;k++){var I=T(E[k]);w.push(I)}return new v({array:w})}if("set"==S){for(var E=t[S],w=[],k=0;k<E.length;k++){var I=T(E[k]);w.push(I)}return new _({array:w})}if("tag"==S){var A=t[S];if("[object Array]"===Object.prototype.toString.call(A)&&3==A.length){var R=T(A[2]);return new b({tag:A[0],explicit:A[1],obj:R})}var P={};if(A.explicit!==undefined&&(P.explicit=A.explicit),A.tag!==undefined&&(P.tag=A.tag),A.obj===undefined)throw"obj shall be specified for 'tag'.";return P.obj=T(A.obj),new b(P)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},o.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",i=parseInt(t.substr(0,2),16),r=Math.floor(i/40),o=i%40,e=r+"."+o,s="",a=2;a<t.length;a+=2){var l=parseInt(t.substr(a,2),16),u=("00000000"+l.toString(2)).slice(-8);if(s+=u.substr(1,7),"0"==u.substr(0,1)){e=e+"."+new n.BigInteger(s,2).toString(10),s=""}}return e},o.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",r=t.split("."),o=40*parseInt(r[0])+parseInt(r[1]);i+=e(o),r.splice(0,2);for(var s=0;s<r.length;s++)i+=function(t){var i="",r=new n.BigInteger(t,10),o=r.toString(2),s=7-o.length%7;7==s&&(s=0);for(var a="",l=0;l<s;l++)a+="0";o=a+o;for(var l=0;l<o.length-1;l+=7){var u=o.substr(l,7);l!=o.length-7&&(u="1"+u),i+=e(parseInt(u,2))}return i}(r[s]);return i},o.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if("undefined"==typeof this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var i=e.length/2;if(i>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+i).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},o.asn1.DERAbstractString=function(t){o.asn1.DERAbstractString.superclass.constructor.call(this);this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):"undefined"!=typeof t.str?this.setString(t.str):"undefined"!=typeof t.hex&&this.setStringHex(t.hex))},r.YAHOO.lang.extend(o.asn1.DERAbstractString,o.asn1.ASN1Object),o.asn1.DERAbstractTime=function(t){o.asn1.DERAbstractTime.superclass.constructor.call(this);this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,i){var n=this.zeroPadding,r=this.localDateToUTC(t),o=String(r.getFullYear());"utc"==e&&(o=o.substr(2,2));var s=n(String(r.getMonth()+1),2),a=n(String(r.getDate()),2),l=n(String(r.getHours()),2),u=n(String(r.getMinutes()),2),c=n(String(r.getSeconds()),2),h=o+s+a+l+u+c;if(!0===i){var p=r.getMilliseconds();if(0!=p){var d=n(String(p),3);d=d.replace(/[0]+$/,""),h=h+"."+d}}return h+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,i,n,r,o){var s=new Date(Date.UTC(t,e-1,i,n,r,o,0));this.setByDate(s)},this.getFreshValueHex=function(){return this.hV}},r.YAHOO.lang.extend(o.asn1.DERAbstractTime,o.asn1.ASN1Object),o.asn1.DERAbstractStructured=function(t){o.asn1.DERAbstractString.superclass.constructor.call(this);this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&"undefined"!=typeof t.array&&(this.asn1Array=t.array)},r.YAHOO.lang.extend(o.asn1.DERAbstractStructured,o.asn1.ASN1Object),o.asn1.DERBoolean=function(){o.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},r.YAHOO.lang.extend(o.asn1.DERBoolean,o.asn1.ASN1Object),o.asn1.DERInteger=function(t){o.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=o.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new n.BigInteger(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("undefined"!=typeof t.bigint?this.setByBigInteger(t.bigint):"undefined"!=typeof t["int"]?this.setByInteger(t["int"]):"number"==typeof t?this.setByInteger(t):"undefined"!=typeof t.hex&&this.setValueHex(t.hex))},r.YAHOO.lang.extend(o.asn1.DERInteger,o.asn1.ASN1Object),o.asn1.DERBitString=function(t){if(t!==undefined&&"undefined"!=typeof t.obj){var e=o.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}o.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var i="0"+t;this.hTLV=null,this.isModified=!0,this.hV=i+e},this.setByBinaryString=function(t){t=t.replace(/0+$/,"");var e=8-t.length%8;8==e&&(e=0);for(var i=0;i<=e;i++)t+="0";for(var n="",i=0;i<t.length-1;i+=8){var r=t.substr(i,8),o=parseInt(r,2).toString(16);1==o.length&&(o="0"+o),n+=o}this.hTLV=null,this.isModified=!0,this.hV="0"+e+n},this.setByBooleanArray=function(t){for(var e="",i=0;i<t.length;i++)1==t[i]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),i=0;i<t;i++)e[i]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):"undefined"!=typeof t.hex?this.setHexValueIncludingUnusedBits(t.hex):"undefined"!=typeof t.bin?this.setByBinaryString(t.bin):"undefined"!=typeof t.array&&this.setByBooleanArray(t.array))},r.YAHOO.lang.extend(o.asn1.DERBitString,o.asn1.ASN1Object),o.asn1.DEROctetString=function(t){if(t!==undefined&&"undefined"!=typeof t.obj){var e=o.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}o.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},r.YAHOO.lang.extend(o.asn1.DEROctetString,o.asn1.DERAbstractString),o.asn1.DERNull=function(){o.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},r.YAHOO.lang.extend(o.asn1.DERNull,o.asn1.ASN1Object),o.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},i=function(t){var i="",r=new n.BigInteger(t,10),o=r.toString(2),s=7-o.length%7;7==s&&(s=0);for(var a="",l=0;l<s;l++)a+="0";o=a+o;for(var l=0;l<o.length-1;l+=7){var u=o.substr(l,7);l!=o.length-7&&(u="1"+u),i+=e(parseInt(u,2))}return i};o.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",r=t.split("."),o=40*parseInt(r[0])+parseInt(r[1]);n+=e(o),r.splice(0,2);for(var s=0;s<r.length;s++)n+=i(r[s]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(t){var e=o.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},t!==undefined&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):t.oid!==undefined?this.setValueOidString(t.oid):t.hex!==undefined?this.setValueHex(t.hex):t.name!==undefined&&this.setValueName(t.name))},r.YAHOO.lang.extend(o.asn1.DERObjectIdentifier,o.asn1.ASN1Object),o.asn1.DEREnumerated=function(t){o.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=o.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new n.BigInteger(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("undefined"!=typeof t["int"]?this.setByInteger(t["int"]):"number"==typeof t?this.setByInteger(t):"undefined"!=typeof t.hex&&this.setValueHex(t.hex))},r.YAHOO.lang.extend(o.asn1.DEREnumerated,o.asn1.ASN1Object),o.asn1.DERUTF8String=function(t){o.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},r.YAHOO.lang.extend(o.asn1.DERUTF8String,o.asn1.DERAbstractString),o.asn1.DERNumericString=function(t){o.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},r.YAHOO.lang.extend(o.asn1.DERNumericString,o.asn1.DERAbstractString),o.asn1.DERPrintableString=function(t){o.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},r.YAHOO.lang.extend(o.asn1.DERPrintableString,o.asn1.DERAbstractString),o.asn1.DERTeletexString=function(t){o.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},r.YAHOO.lang.extend(o.asn1.DERTeletexString,o.asn1.DERAbstractString),o.asn1.DERIA5String=function(t){o.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},r.YAHOO.lang.extend(o.asn1.DERIA5String,o.asn1.DERAbstractString),o.asn1.DERUTCTime=function(t){o.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return"undefined"==typeof this.date&&"undefined"==typeof this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},t!==undefined&&(t.str!==undefined?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):t.hex!==undefined?this.setStringHex(t.hex):t.date!==undefined&&this.setByDate(t.date))},r.YAHOO.lang.extend(o.asn1.DERUTCTime,o.asn1.DERAbstractTime),o.asn1.DERGeneralizedTime=function(t){o.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===undefined&&this.s===undefined&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},t!==undefined&&(t.str!==undefined?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):t.hex!==undefined?this.setStringHex(t.hex):t.date!==undefined&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},r.YAHOO.lang.extend(o.asn1.DERGeneralizedTime,o.asn1.DERAbstractTime),o.asn1.DERSequence=function(t){o.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},r.YAHOO.lang.extend(o.asn1.DERSequence,o.asn1.DERAbstractStructured),o.asn1.DERSet=function(t){o.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var i=this.asn1Array[e];t.push(i.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&"undefined"!=typeof t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},r.YAHOO.lang.extend(o.asn1.DERSet,o.asn1.DERAbstractStructured),o.asn1.DERTaggedObject=function(t){o.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,i){this.hT=e,this.isExplicit=t,this.asn1Object=i,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=i.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("undefined"!=typeof t.tag&&(this.hT=t.tag),"undefined"!=typeof t.explicit&&(this.isExplicit=t.explicit),"undefined"!=typeof t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},r.YAHOO.lang.extend(o.asn1.DERTaggedObject,o.asn1.ASN1Object)},"./node_modules/jsencrypt/lib/lib/jsrsasign/yahoo.js":function(t,e,i){"use strict";i.r(e),i.d(e,{YAHOO:function(){return n}});var n={};n.lang={extend:function(t,e,i){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var n=function(){};if(n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),i){var r;for(r in i)t.prototype[r]=i[r];var o=function(){},s=["toString","valueOf"];try{
/MSIE/.test(navigator.userAgent)&&(o=function(t,e){for(r=0;r<s.length;r+=1){var i=s[r],n=e[i];"function"==typeof n&&n!=Object.prototype[i]&&(t[i]=n)}})}catch(a){}o(t.prototype,i)}}}},"./node_modules/jsencrypt/lib/version.json":function(t){"use strict";t.exports={version:"3.2.0"}}},__webpack_module_cache__={};!function(){__webpack_require__.d=function(t,e){for(var i in e)__webpack_require__.o(e,i)&&!__webpack_require__.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}}(),function(){__webpack_require__.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){__webpack_require__.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}();var __webpack_exports__={};return function(){"use strict";function t(t){return t&&t.__esModule?t:{"default":t}}function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function n(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}var r=__webpack_exports__,o=__webpack_require__("./node_modules/@tencent/js-armor-loader/dist/runtime.js");Object.defineProperty(r,"__esModule",{value:!0}),r["default"]=void 0;var s=t(__webpack_require__("./node_modules/jsencrypt/lib/index.js")),a=t(__webpack_require__("./gen-overlay-property.js")),l=t(__webpack_require__("./aes.js")),u=function(){function t(){e(this,t),this.overlayKey=(0,a["default"])(),this.overlayIv=(0,a["default"])()}return n(t,[{key:"base64ToHex",value:function(t){for(var e=atob(t),i="",n=0;n<e.length;n++){var r=e.charCodeAt(n).toString(16);i+=2===r.length?r:"0"+r}return i}},{key:"checkEnvironment",value:function(){return!(!Hls||!Hls.isSupported())}},{key:"generatePlayCgiUrl",value:function(t){return o.g(o.v(0,o.d(["DAQSAhIDQg4MBxICEgMSBBIFEgY0AzwAJS0CZAJlAmYCYQJ1AmwCdB4oADMFBTQELQJNAkkCRwJmAk0CQQIwAkcCQwJTAnECRwJTAkkCYgIzAkQCUQJFAkICQQJRAlUCQQJBAjQCRwJOAkECRAJDAkICaQJRAksCQgJnAlECQwIzAnACRAJBAjcCRwJUAngCTwJ2Ak4CYgJYAlICRwJNAmkCOQJRAlMCSQJ6AlECRQJJAisCRQJNAkQCMQJIAmMCVQJQAkoCUwJRAlMCRgJ1AlICawJaAmsCVwJvAjQCVgJRAkUCQwJ1AlACUgJnAi8CeAJWAmoCcQJ3AlgCMQJ5AlUCcgJIAlUCdgJHAlECSgJzAkICdwJUAlMCLwI2AkwCSQJjAlECaQJTAncCWQJzAk8CcQJmAisCOAJUAlcCeAJHAlECTwJKAnkCVwI0AjYCZwJQAlACUQJWAnoCVAJqAk4CVAJpAlUCbwJxAjQCMwI1AlECQgIwAnYCMQIxAmwCTgJ4AnYCSwJXAkICUQJJAloCTAJtAmECYwJVAloCMgJyAjECQQJQAnQCYQI3AmkCLwJNAlkCNAJMAngCOQJYAmwCWgJWAk0CWgJOAlUCZAJVAnkCdwJJAkQCQQJRAkECQjMFBTQDLQJzAmUCdAJQAnUCYgJsAmkCYwJLAmUCeSxDBEUBBTQFNAAtAmICYQJzAmUCNgI0AlQCbwJIAmUCeCw0Ay0CZQJuAmMCcgJ5AnACdCw0AC0CbwJ2AmUCcgJsAmECeQJLAmUCeSwQRQFFATMFBTQGNAAtAmICYQJzAmUCNgI0AlQCbwJIAmUCeCw0Ay0CZQJuAmMCcgJ5AnACdCw0AC0CbwJ2AmUCcgJsAmECeQJJAnYsEEUBRQEzBRkBJS0CTQJlAmQCaQJhAlMCbwJ1AnICYwJlHi0CcAJyAm8CdAJvAnQCeQJwAmUeLQJlAm4CZAJPAmYCUwJ0AnICZQJhAm0eLQJ0Am8CUwJ0AnICaQJuAmceRQAtAnICZQJwAmwCYQJjAmVAPAIDLQJcAnMtAmcgAi1FAi0CZgJ1Am4CYwJ0AmkCbwJuAmUCbgJkAk8CZgJTAnQCcgJlAmECbQIoAikCewJbAm4CYQJ0AmkCdgJlAmMCbwJkAmUCXQJ9FzsWGQElLQJNAmUCZAJpAmECUwJvAnUCcgJjAmUeLQJwAnICbwJ0Am8CdAJ5AnACZR4tAmECZAJkAlMCbwJ1AnICYwJlAkICdQJmAmYCZQJyHi0CdAJvAlMCdAJyAmkCbgJnHkUALQJyAmUCcAJsAmECYwJlQDwCAy0CXAJzLQJnIAItRQItAmYCdQJuAmMCdAJpAm8CbgJhAmQCZAJTAm8CdQJyAmMCZQJCAnUCZgJmAmUCcgIoAikCewJbAm4CYQJ0AmkCdgJlAmMCbwJkAmUCXQJ9FzsWBUIVBTQFNAAtAmICYQJzAmUCNgI0AlQCbwJIAmUCeCw0Ay0CZQJuAmMCcgJ5AnACdCw8ABkDJS0CZAJlAmYCYQJ1AmwCdB4QIABFAUUBMwUFNAY0AC0CYgJhAnMCZQI2AjQCVAJvAkgCZQJ4LDQDLQJlAm4CYwJyAnkCcAJ0LDwAGQMlLQJkAmUCZgJhAnUCbAJ0HhAgAEUBRQEzBQU8BCUwEC0CJgJjAmkCcAJoAmUCcgJlAmQCTwJ2AmUCcgJsAmECeQJLAmUCeQI9LQJjAm8CbgJjAmECdEBDBS0CJgJjAmkCcAJoAmUCcgJlAmQCTwJ2AmUCcgJsAmECeQJJAnYCPUUCLQJjAm8CbgJjAmECdEBDBi0CJgJrAmUCeQJJAmQCPQIxRQI1HQcABRkEAyE0AxwJAAAzBQU3IQ==",[7,1389,868,1081,1082,1087,1085,1242]]),[function(){return void 0===s?undefined:s},function(){return"undefined"==typeof window?undefined:window},function(){return"undefined"==typeof RegExp?undefined:RegExp},function(){return void 0===a?undefined:a},function(){return void 0===t?undefined:t}])).call(this)}},{key:"generateKey",value:function(t){var e,i=new Uint8Array(t);if(this.overlayKey&&this.overlayIv){for(var n=[],r=[],o=0;o<16;o++){var s=this.overlayKey.substring(2*o,2*o+2),a=this.overlayIv.substring(2*o,2*o+2);n.push(parseInt(s,16)),r.push(parseInt(a,16))}e=new l["default"].modeOfOperation.cbc(n,r).decrypt(i)}return e||i}}]),t}(),c=u;r["default"]=c}(),__webpack_exports__=__webpack_exports__["default"]}()})}),Enc=unwrapExports(enc),strictUriEncode=function(t){return encodeURIComponent(t).replace(/[!'()*]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})},getOwnPropertySymbols=Object.getOwnPropertySymbols,hasOwnProperty$1=Object.prototype.hasOwnProperty,propIsEnumerable=Object.prototype.propertyIsEnumerable,objectAssign=shouldUseNative()?Object.assign:function(t,e){for(var i,n,r=toObject(t),o=1;o<arguments.length;o++){i=Object(arguments[o]);for(var s in i)hasOwnProperty$1.call(i,s)&&(r[s]=i[s]);if(getOwnPropertySymbols){n=getOwnPropertySymbols(i);for(var a=0;a<n.length;a++)propIsEnumerable.call(i,n[a])&&(r[n[a]]=i[n[a]])}}return r},token="%[a-f0-9]{2}",singleMatcher=new RegExp(token,"gi"),multiMatcher=new RegExp("("+token+")+","gi"),decodeUriComponent=function(t){if("string"!=typeof t)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof t+"`");try{return t=t.replace(/\+/g," "),decodeURIComponent(t)}catch(e){return customDecodeURIComponent(t)}},extract_1=extract,parse_1=parse,stringify=function(t,e){e=objectAssign({encode:!0,strict:!0,arrayFormat:"none"},e),!1===e.sort&&(e.sort=function(){});var i=encoderForArrayFormat(e);return t?Object.keys(t).sort(e.sort).map(function(n){var r=t[n];if(r===undefined)return"";if(null===r)return encode(n,e);if(Array.isArray(r)){var o=[];return r.slice().forEach(function(t){t!==undefined&&o.push(i(n,t,o.length))}),o.join("&")}return encode(n,e)+"="+encode(r,e)}).filter(function(t){return t.length>0}).join("&"):""},parseUrl$1=function(t,e){return{url:t.split("?")[0]||"",query:parse(extract(t),e)}},queryString={extract:extract_1,parse:parse_1,stringify:stringify,parseUrl:parseUrl$1},count=0,MediaAsyncLoader=function(t){function e(i,n,r){classCallCheck(this,e),log$2("MediaAsyncLoader initializing"),!(n.playerOptions&&n.playerOptions.psign&&Hls$1&&Hls$1.isSupported())||videojs.browser.IS_TBS||videojs.browser.IS_MQQB||videojs.browser.IS_SAFARI||videojs.browser.IE_VERSION&&videojs.browser.IE_VERSION<11||(n.playerOptions.overlayKey=genOverlay(),n.playerOptions.overlayIv=genOverlay(),n.playerOptions.dKeyDebug=n.dKeyDebug||!1);var o=mergeOptions({createEl:!1},n),s=possibleConstructorReturn(this,t.call(this,i,o,r)),a=o.playerOptions||o;return s.getInfo(a),i.loadVideoByID=bind(s,s.loadVideoByID),log$2("MediaAsyncLoader initialized"),s}return inherits(e,t),e.prototype.getInfo=function(t){t&&t.fileID&&t.appID&&(performance&&performance.mark("firstFrameStart"),t.playDefinition?this.getInfoV3(t):t.sign||t.ssign?this.getInfoV2(t):this.getInfoV4(t))},e.prototype.getInfoV2=function(t){var e=this.player(),i=this.assemblyPath("getplayinfo",t.appID,t.fileID),n=t.t,r=t.us,o=t.exper,s=t.sign,a={};t.ssign?a.super_sign=t.ssign:s&&(a={t:n,sign:s},(r||0==r)&&(a.us=r),(o||0==o)&&(a.exper=o,e.trigger({type:"feature",data:"exper"})),e.trigger({type:"feature",data:"key"})),t.playerID&&(a.playerid=t.playerID),this.requestTimestamp=+(new Date).getTime(),e.trigger({type:"playcgistart",data:{time:this.requestTimestamp,url:i}}),log$2("playcgi loading");var l=queryString.stringify(a);l&&(i+=(~i.indexOf("?")?"&":"?")+l,i=i.replace("?&","?")),this.getInfoRetryTimes=0,this.getInfoRequest=function(t){t&&(i=i.replace(SERVER_PATH,SERVER_PATH_BACKUP)),IE_VERSION&&IE_VERSION<10?jsonp(i,{param:a,timeout:3e3,prefix:"TcCallBack"},bind(this,this.onResult)):xhr({url:i,timeout:3e3},bind(this,this.onResult))},this.getInfoRequest()},e.prototype.onResult=function(t,e,i){try{i?e=JSON.parse(i):t||e||(t=new Error("ServerError"))}catch(l){t=new Error("ServerError")}var n=this.player();if(t){if(log$2("playcgi loaded failure"),this.getInfoRetryTimes<3)return this.getInfoRetryTimes++,void this.getInfoRequest();if(this.getInfoRetryTimes<6)return this.getInfoRetryTimes++,void this.getInfoRequest({isBackup:!0});n.trigger({type:"playcgiend",data:{time:+(new Date).getTime(),startTime:this.requestTimestamp,error:t,result:e}});var r=12;switch(t.message){case"XMLHttpRequest timeout":case"Timeout":r=10;break;case"ServerError":r=11}this.player().error({code:r})}else{if(n.trigger({type:"playcgiend",data:{time:+(new Date).getTime(),startTime:this.requestTimestamp,error:t,result:e}}),0!=e.code)return log$2("playcgi loaded failure"),void this.player().error({code:e.code});log$2("playcgi loaded success");var o=e.coverInfo,s=e.videoInfo,a=e.playerInfo;this.setPoster(o),this.setLogo(a),this.setSrc(e),this.setPatch(a),this.setVttThumbnail(e),this.setDots(e),this.setOriginDuration(s),this.setPlayList(a)}},e.prototype.getInfoV3=function(t){var e=this.player(),i=this.assemblyPathV3("getplayinfo",t.appID,t.fileID,t.playDefinition),n=t.t,r=t.us,o=t.rlimit,s=t.sign,a={};t.ssign?a.super_sign=t.ssign:s&&(a={t:n,sign:s},(r||0==r)&&(a.us=r),o&&(a.rlimit=o),e.trigger({type:"feature",data:"key"})),t.playerID&&(a.playerid=t.playerID),this.requestTimestamp=+(new Date).getTime(),e.trigger({type:"playcgistart",data:{time:this.requestTimestamp,url:i}}),log$2("playcgi v3 loading");var l=queryString.stringify(a);l&&(i+=(~i.indexOf("?")?"&":"?")+l,i=i.replace("?&","?")),this.getInfoRetryTimes=0,this.getInfoRequest=function(t){t&&(i=i.replace(SERVER_PATH_V3,SERVER_PATH_BACKUP)),IE_VERSION&&IE_VERSION<10?jsonp(i,{param:a,timeout:3e3,prefix:"TcCallBack"},bind(this,this.onResultV3)):xhr({url:i,timeout:3e3},bind(this,this.onResultV3))},this.getInfoRequest()},e.prototype.onResultV3=function(t,e,i){try{i?e=JSON.parse(i):t||e.body||(t=new Error("ServerError"))}catch(a){t=new Error("ServerError")}var n=this.player();if(t){if(log$2("playcgi v3 loaded failure"),this.getInfoRetryTimes<3)return this.getInfoRetryTimes++,void this.getInfoRequest();if(this.getInfoRetryTimes<6)return this.getInfoRetryTimes++,void this.getInfoRequest({isBackup:!0});n.trigger({type:"playcgiend",data:{time:+(new Date).getTime(),startTime:this.requestTimestamp,error:t,result:e}});var r=12;switch(t.message){case"XMLHttpRequest timeout":case"Timeout":r=10;break;case"ServerError":r=11}this.player().error({code:r})}else{if(n.trigger({type:"playcgiend",data:{time:+(new Date).getTime(),startTime:this.requestTimestamp,error:t,result:e}}),0!=e.code)return log$2("playcgi v3 loaded failure"),void this.player().error({code:e.code});log$2("playcgi v3 loaded success");var o=e.mediaInfo,s=e.playerInfo;this.setPoster({coverUrl:o.basicInfo.coverUrl}),this.setLogo(s),this.setSrcV3(o),this.setPatch(s),this.setVttThumbnail(o),this.setDots(o)}},e.prototype.setSrcV3=function(t,e){var i=this,n=this.player(),r=n.options_.plugins,o=n.DRM().options||r.DRM,s=void 0,a=void 0,l=void 0,u=[],c={},h={};if(e=e||(o?o.skipPlan:""),t.previewStreamingInfo&&t.previewStreamingInfo.previewStreamingList.length>0)t.previewStreamingInfo.previewStreamingList.forEach(function(t,e){t.mineType=i.getMIMEType(t.url),c[getFileExtension(t.url)]=t});else{if(!(t.dynamicStreamingInfo.adaptiveStreamingInfoList&&t.dynamicStreamingInfo.adaptiveStreamingInfoList.length>0))return void this.player().error({code:13,message:"no video stream"});if(t.dynamicStreamingInfo.adaptiveStreamingInfoList.forEach(function(t,e){t.mineType=i.getMIMEType(t.url),h[t.drmType.toLowerCase()||getFileExtension(t.url)]=t}),o&&o.token?(s=o.token,l=o.certificateUri,e=IS_SAFARI&&!l?"fairplay":e,a=window_1.encodeURIComponent(s)):e="all",(IS_CHROME||IS_FIREFOX)&&window_1.navigator.requestMediaKeySystemAccess&&h.widevine&&"widevine"!=e&&"all"!=e?(u.push({src:unifyProtocol(h.widevine.url),type:this.getMIMEType(h.widevine.url),keySystems:{"com.widevine.alpha":{serverURL:LICENSE_PATH+"?token="+a+"&drmType="+h.widevine.drmType}}}),log$2("DRM source widevine",u)):IS_SAFARI&&!IS_MQQB&&h.fairplay&&"fairplay"!=e&&"all"!=e&&(u.push({src:unifyProtocol(h.fairplay.url),type:this.getMIMEType(h.fairplay.url),keySystems:{"com.apple.fps.1_0":{certificateUri:l,licenseUri:LICENSE_PATH+"?token="+a+"&drmType="+h.fairplay.drmType}}}),log$2("DRM source fairplay",u)),h.simpleaes&&"all"!=e){var p=h.simpleaes.url.split("/");p[p.length-1]="voddrm.token."+a+"."+p[p.length-1],u.push({src:unifyProtocol(p.join("/")),type:this.getMIMEType(h.simpleaes.url)}),log$2("DRM source simpleaes",u)}h.m3u8&&u.push({src:unifyProtocol(h.m3u8.url),type:this.getMIMEType(h.m3u8.url)}),h.mpd&&u.push({src:unifyProtocol(h.mpd.url),type:this.getMIMEType(h.mpd.url)})}if(!(u.length>0))return void this.player().error({code:4});n.src(u);var d=function(e){return log$2("DRM init Error",u),5!==e.data.code&&2!==e.data.code||!u[0].keySystems["com.widevine.alpha"]?3===e.data.code&&u[0].keySystems["com.apple.fps.1_0"]?void i.setSrcV3(t,"fairplay"):14===e.data.code||3===e.data.code?void i.setSrcV3(t,"all"):void 0:void i.setSrcV3(t,"widevine")};"all"!==e&&(window_1.setTimeout(function(){n.one("error",d)},0),n.one("playing",function(t){}))},e.prototype.getInfoV4=function(t){var e=this.player(),i=this.assemblyPathV4("getplayinfo",t.appID,t.fileID),n=t.t,r=t.us,o=t.rlimit,s=t.psign,a=t.exper,l={};t.ssign?l.super_sign=t.ssign:s&&(l={t:n,psign:s},(r||0==r)&&(l.us=r),o&&(l.rlimit=o),a&&(l.exper=a)),t.playerConfig&&(l.pcfg=t.playerConfig),t.playerID&&(l.playerid=t.playerID),this.requestTimestamp=+(new Date).getTime(),e.trigger({type:"playcgistart",data:{time:this.requestTimestamp,url:i}}),log$2("playcgi v4 loading");var u=queryString.stringify(l);u&&(i+=(~i.indexOf("?")?"&":"?")+u,i=i.replace("?&","?"),Hls$1&&Hls$1.isSupported()&&!IS_ANY_SAFARI&&(this.enModule=new Enc,i=this.enModule.generatePlayCgiUrl(i))),this.getInfoRetryTimes=0,this.getInfoRequest=function(t){t&&(i=i.replace(SERVER_PATH_V4,SERVER_PATH_BACKUP)),IE_VERSION&&IE_VERSION<10?jsonp(i,{param:l,timeout:3e3,prefix:"TcCallBack"},bind(this,this.onResultV4)):xhr({url:i,timeout:3e3},bind(this,this.onResultV4))},this.getInfoRequest()},e.prototype.onResultV4=function(t,e,i){try{i?e=JSON.parse(i):t||e.body||(t=new Error("ServerError"))}catch(u){t=new Error("ServerError")}var n=this.player();if(t){if(log$2("playcgi v4 loaded failure"),this.getInfoRetryTimes<3)return this.getInfoRetryTimes++,void this.getInfoRequest();if(this.getInfoRetryTimes<6)return this.getInfoRetryTimes++,void this.getInfoRequest({isBackup:!0});n.trigger({type:"playcgiend",data:{time:+(new Date).getTime(),startTime:this.requestTimestamp,error:t,result:e}});var r=12;switch(t.message){case"XMLHttpRequest timeout":case"Timeout":r=10;break;case"ServerError":r=11}this.player().error({code:r})}else{if(n.trigger({type:"playcgiend",data:{time:+(new Date).getTime(),startTime:this.requestTimestamp,error:t,result:e}}),0!=e.code){if(log$2("playcgi v4 loaded failure"),e.code>=2e3&&e.code<3e3){if(this.getInfoRetryTimes<3)return this.getInfoRetryTimes++,void this.getInfoRequest();if(this.getInfoRetryTimes<6)return this.getInfoRetryTimes++,void this.getInfoRequest({isBackup:!0})}return void this.player().error({code:e.code})}if(log$2("playcgi v4 loaded success",e),4===e.version){var o=e.media;this.setPoster({coverUrl:o.basicInfo.coverUrl}),this.setSrcV4(o),this.setVttThumbnailV4(o),this.setDots(o),this.setOriginDurationV4(o)}else if(2===e.version){var s=e.coverInfo,a=e.videoInfo,l=e.playerInfo;this.setPoster(s),this.setLogo(l),this.setSrc(e),this.setPatch(l),this.setVttThumbnail(e),this.setDots(e),this.setOriginDuration(a)}}},e.prototype.setSrcV4=function(t){var e=this.player(),i=e.options_,n=void 0,r=void 0,o="plain";if("Original"===t.audioVideoType||"Transcode"===t.audioVideoType)return this.setSrc(t),!1;if(!t.streamingInfo)return void this.player().error({code:13,message:"no stream info"});if(t.streamingInfo){if(i.hlsConfig?(i.hlsConfig.overlayKey=this.enModule&&this.enModule.overlayKey,i.hlsConfig.overlayIv=this.enModule&&this.enModule.overlayIv,i.hlsConfig.startLevel||(i.hlsConfig.startLevel=5)):i.hlsConfig={overlayKey:this.enModule&&this.enModule.overlayKey,overlayIv:this.enModule&&this.enModule.overlayIv,startLevel:5},t.streamingInfo.plainOutput&&(n=t.streamingInfo.plainOutput.url,r=t.streamingInfo.plainOutput.subStreams),t.streamingInfo.drmOutput&&Array.isArray(t.streamingInfo.drmOutput)){var s=t.streamingInfo.drmOutput;if("SimpleAES"===s[0].type){o="SimpleAES";var a=s[0].url.split("/");a[a.length-1]="voddrm.token."+t.streamingInfo.drmToken+"."+a[a.length-1],n=a.join("/"),r=s[0].subStreams}}if(n){var l;e.src([{src:unifyProtocol(n),type:EXT_MIME.m3u8,keySystems:(l={},l[o]={},l)}])}else this.player().error({code:13,message:"no stream info"});this.setQualityLabelList(r)}},e.prototype.playOriginalVideo=function(t){this.player().src(t.url)},e.prototype.setQualityLabelList=function(t){this.player().QualitySwitcher().setOptions({qualityLabelList:{video:t}})},e.prototype.setLogo=function(t){var e=this.player();t.logoPic?(e.logoImage.update({img:{url:unifyProtocol(t.logoPic),position:t.logoLocation},link:unifyProtocol(t.logoUrl)}),e.trigger({type:"feature",data:"logo"})):e.logoImage.reset()},e.prototype.setPoster=function(t){var e=this.player();t&&t.coverUrl?e.poster(unifyProtocol(t.coverUrl)):e.options_.playerOptions.poster||e.poster("")},e.prototype.setDots=function(t){var e=this.player();if(e.options_.dots=null,t.keyFrameDescInfo&&t.keyFrameDescInfo.keyFrameDescList&&t.keyFrameDescInfo.keyFrameDescList.length>0){var i=t.keyFrameDescInfo.keyFrameDescList;e.options_.dots=i}var n=e.options_.plugins;n&&n.ProgressMarker&&n.ProgressMarker.markers&&n.ProgressMarker.markers.length>0&&(e.options_.dots=e.options_.dots.concat(n.ProgressMarker.markers)),e.ProgressMarker().init()},e.prototype.setSrc=function(t){var e=this.player(),i=t.videoInfo||{};if("Original"===t.audioVideoType&&(i.sourceVideo=t.originalInfo),"Transcode"===t.audioVideoType&&(i.sourceVideo=t.transcodeInfo),(null!=e.options_.definition||e.options_.definition!=undefined)&&i.transcodeList&&i.transcodeList.length>0){e.trigger({type:"feature",data:"definition"});var n=this.getSrcByDefinition(i,e.options_.definition);if(n)return e.src({src:unifyProtocol(n.url),type:this.getMIMEType(n.url)}),!0}if(e.MultiResolution().reset(),i.masterPlayList)e.src(this.getMasterSouces(i)),e.MultiResolution().store(this.getMultiResolutionData(t));else if(i.transcodeList&&i.transcodeList.length>0){var r=this.getMultiResolutionData(t);if(r.showOrder.length>0)e.MultiResolution().init(r);else{var o=i.transcodeList[0].url;e.src({src:unifyProtocol(o),type:this.getMIMEType(o)})}}else i.sourceVideo&&this.getMIMEType(i.sourceVideo.url)?e.src({src:unifyProtocol(i.sourceVideo.url),type:this.getMIMEType(i.sourceVideo.url)}):this.player().error({code:13})},e.prototype.setPatch=function(t){var e=this.player();t.patchInfo?e.ImagePatch().init(t.patchInfo):e.ImagePatch().reset()},e.prototype.setVttThumbnail=function(t){var e=this.player();t.imageSpriteInfo&&t.imageSpriteInfo.imageSpriteList?e.ready(bind(this,function(){e.VttThumbnail().init({vttUrl:unifyProtocol(t.imageSpriteInfo.imageSpriteList[0].webVttUrl)})})):e.VttThumbnail().reset()},e.prototype.setVttThumbnailV4=function(t){var e=this.player();t.imageSpriteInfo&&t.imageSpriteInfo.webVttUrl?e.ready(bind(this,function(){e.VttThumbnail().init({vttUrl:unifyProtocol(t.imageSpriteInfo.webVttUrl)})})):e.VttThumbnail().reset()},e.prototype.setOriginDuration=function(t){var e=this.player();if(e.options_.exper>0){var i=t.sourceVideo?t.sourceVideo.floatDuration:t.transcodeList[0].floatDuration;e.one("play",function(){e.duration(i)})}},e.prototype.setOriginDurationV4=function(t){var e=this,i=this.player();i.cache_.originDuration=t.basicInfo.duration,i.off("durationchange",this.keepOriginDuration),i.one("durationchange",function(t){(!i.duration()||i.duration()>0&&Math.floor(i.cache_.originDuration)!==Math.floor(i.duration()))&&(e.keepOriginDuration(t),i.on("durationchange",e.keepOriginDuration))})},e.prototype.keepOriginDuration=function(t){var e=this.player(),i=e.cache_.originDuration;Math.floor(i)!==Math.floor(e.duration())&&(log$2("试看视频"),e.duration(Math.round(i)))},e.prototype.getMasterSouces=function(t){var e=t.masterPlayList,i=[];return i.push({src:unifyProtocol(e.url),type:EXT_MIME.m3u8}),i},e.prototype.getMultiResolutionData=function(t){var e={sources:{},labels:{},showOrder:[],defaultRes:""},i=[],n=t.videoInfo.transcodeList,r=this;return t.playerInfo.videoClassification.forEach(function(t,o){n.length>0&&(n=n.filter(function(n){if(!(t.definitionList.indexOf(n.definition)>-1))return!0;i[t.id]||(e.showOrder.push(t.id),e.labels[t.id]=t.name,i[t.id]=[]),i[t.id].push({src:unifyProtocol(n.url),type:r.getMIMEType(n.url)})}))}),e.sources=i,e.defaultRes=t.playerInfo.defaultVideoClassification||Object.keys(i)[0],e},e.prototype.getSrcByDefinition=function(t,e){var i=t.transcodeList,n=t.sourceVideo;if(0==e)return n;for(var r=0;r<i.length;r++)if(i[r].definition==e)return i[r]},e.prototype.getMIMEType=function(t){var e=EXT_MIME[getFileExtension(t)];return e||(log$2.error("MIME type no found! Ext = "+getFileExtension(t)),"")},e.prototype.assemblyPath=function(t,e,i){var n=window_1.location.protocol;return"http:"!=n&&"https:"!=n&&(n="https:"),""+n+SERVER_PATH+"/"+t+"/v2/"+e+"/"+i},e.prototype.assemblyPathV3=function(t,e,i,n){var r=window_1.location.protocol;return"http:"!=r&&"https:"!=r&&(r="https:"),""+r+SERVER_PATH_V3+"/"+t+"/v3/"+e+"/"+i+"/"+n},e.prototype.assemblyPathV4=function(t,e,i){var n=window_1.location.protocol;return"http:"!=n&&"https:"!=n&&(n="https:"),""+n+SERVER_PATH_V4+"/"+t+"/v4/"+e+"/"+i},e.prototype.customHost=function(t){var e=this.player();e.options_.customHost&&/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/.test(e.options_.customHost)&&(t.masterPlayList&&(t.masterPlayList.url=replaceHost(t.masterPlayList.url,e.options_.customHost)),t.sourceVideo&&(t.sourceVideo.url=replaceHost(t.sourceVideo.url,e.options_.customHost)),t.transcodeList.length>0&&t.transcodeList.forEach(function(t,i){t.url=replaceHost(t.url,e.options_.customHost)}),e.trigger({type:"feature",data:"customHost"}))},e.prototype.loadVideoByID=function(t){var e=this.player();e.trigger({type:"loadnewvideo"}),!(t.psign&&Hls$1&&Hls$1.isSupported())||videojs.browser.IS_TBS||videojs.browser.IS_MQQB||videojs.browser.IS_SAFARI||videojs.browser.IE_VERSION&&videojs.browser.IE_VERSION<11||(t.overlayKey=e.options_.playerOptions.overlayKey,t.overlayIv=e.options_.playerOptions.overlayIv),e.hasStarted(!1),this.getInfo(t),e.duration(0),e.playbackRate(1),e.options_.appID=t.appID,e.options_.fileID=t.fileID,e.options_.psign=t.psign,t.definition&&(e.options_.definition=t.definition),e.bigPlayButton&&e.bigPlayButton.show(),e.options_.plugins.ContinuePlay&&e.ContinuePlay().init()},e.prototype.setPlayList=function(t){this.player().options_.plugins.PlayList||t.playlist&&t.playlist.data instanceof Array&&t.playlist.data.length>0&&this.player_.PlayList(t.playlist)},e}(Component);Component.registerComponent("MediaAsyncLoader",MediaAsyncLoader);var Component$2=videojs.getComponent("Component"),ClickableComponent$2=videojs.getComponent("ClickableComponent"),positionMap=["left-top","left-bottom","right-top","right-bottom"],LogoImage=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return i.on("logochange",videojs.bind(r,function(t){this.update(t.data)})),n.img&&r.update(n),r}return inherits(e,t),e.prototype.createEl=function(){var t=videojs.dom.createEl("div",{className:"tcp-logo vjs-hidden"}),e=videojs.dom.createEl("a",{className:"tcp-logo-link",target:"_blank"}),i=videojs.dom.createEl("img",{className:"tcp-logo-img"});return this.linkEl_=e,this.imgEl_=i,e.appendChild(i),t.appendChild(e),t},e.prototype.update=function(t){var e=t.img,i=t.link;this.setImg(e),this.setHref(i),this.options_=videojs.mergeOptions(this.options_,t),this.show()},e.prototype.setImg=function(t){if(this.imgEl_){this.imgEl_.src=t.url;var e=positionMap[t.position]||t.position||"left-top";videojs.dom.addClass(this.el_,e)}},e.prototype.setHref=function(t){this.linkEl_&&t&&(this.linkEl_.href=t)},e.prototype.reset=function(){this.hide(),this.imgEl_.removeAttribute("src"),this.linkEl_.removeAttribute("href")},e}(ClickableComponent$2);videojs.registerComponent("LogoImage",LogoImage);var Component$3=videojs.getComponent("Component"),Button$2=videojs.getComponent("Button"),ContinuePlayTips=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.init(n),r}return inherits(e,t),e.prototype.createEl=function(){return videojs.dom.createEl("div",{className:"tcp-continue-play-tips"})},e.prototype.close=function(){this.hide()},e.prototype.init=function(t){var e=this.addChild("closeButton",{controlText:"Close"});this.on(e,"close",this.close),this.addChild("ContinuePlayTipsContent",t);var i=this.addChild("ContinuePlayButton",t);this.on(i,["tap","click"],this.close),this.setTimeout(function(){this.close()},3500)},e}(Component$3);videojs.registerComponent("ContinuePlayTips",ContinuePlayTips);var ContinuePlayTipsContent=function(t){function e(i,n){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.createEl=function(){var t=videojs.dom.createEl("span",{className:"tcp-continue-play-tips-content"}),e=videojs.dom.createEl("span",{className:"tcp-text",textContent:this.localize(this.options_.text||"Last time play at ")}),i=videojs.dom.createEl("span",{className:"tcp-time",textContent:videojs.formatTime(this.options_.time)});return this.textEl_=e,this.timeEl_=i,t.appendChild(e),t.appendChild(i),t},e}(Component$3);videojs.registerComponent("ContinuePlayTipsContent",ContinuePlayTipsContent);var ContinuePlayButton=function(t){function e(i,n){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"button",{className:"tcp-continue-play-buttom",textContent:this.localize(this.options_.btnText||"Resume play")})},e.prototype.handleClick=function(){this.options_.resumeCallback.call()},e}(Button$2);videojs.registerComponent("ContinuePlayButton",ContinuePlayButton);var Component$4=videojs.getComponent("Component"),LevelSwitchTips=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.playing=!1,r.timeoutID=undefined,r.levelSwitch=i.options_.plugins.ContextMenu.levelSwitch,r.init(),i.on("playing",videojs.bind(r,function(){r.playing=!0})),i.on("resolutionswitching",videojs.bind(r,function(t){if(r.playing)if(t.data.label)r.switchHandler(r.createSwitching(t.data.label),2e3);else{var e=r.player().QualitySwitcher().options.qualityData;if(e){var i=e.video.filter(function(e){return t.data.newQuality===e.id})[0];r.switchHandler(r.createSwitching(i.label),2e3)}}})),i.on("resolutionswitched",videojs.bind(r,function(){r.playing&&r.switchHandler(r.createSwitched(),2e3)})),i.on("levelSwitchError",videojs.bind(r,function(){r.switchHandler(r.createSwitchError(),2e3)})),r}return inherits(e,t),e.prototype.switchHandler=function(t,e){var i=this;this.timeoutID&&clearTimeout(this.timeoutID),this.textNode.innerText=t,this.show(),this.timeoutID=setTimeout(function(){i.hide()},e)},e.prototype.createEl=function(){var t=videojs.dom.createEl("div",{className:"tcp-switch vjs-hidden"});return this.textNode=videojs.dom.createEl("div",{innerText:""}),t.appendChild(this.textNode),t},e.prototype.init=function(){var t=this.addChild("closeButton",{controlText:"Close"});this.on(t,"close",this.hide)},e.prototype.createSwitching=function(t){return this.levelSwitch.switchingText?this.levelSwitch.switchingText+t:"正在为您切换至"+t},e.prototype.createSwitched=function(){return this.levelSwitch.switchedText?this.levelSwitch.switchedText:"切换成功"},e.prototype.createSwitchError=function(){return this.levelSwitch.switchErrorText?this.levelSwitch.switchErrorText:"切换失败"},e.prototype.show=function(){t.prototype.show.call(this),this.popped=!0},e.prototype.hide=function(){t.prototype.hide.call(this),this.popped=!1},e}(Component$4);videojs.registerComponent("LevelSwitchTips",LevelSwitchTips);var Component$7=videojs.getComponent("Component"),mapKey={fileID:"file ID",requestID:"request ID",mediaType:"media type",mimeType:"mime type",provider:"provider",resolution:"resolution",rate:"rate",frames:"frames",buffer:"buffer",connectionSpeed:"connection speed",info:"info"},VideoTextItem=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.display=!1,r.displayData="loading",r}return inherits(e,t),e.prototype.createEl=function(){var t=videojs.dom.createEl("div",{className:"tcp-statistic-line vjs-hidden"}),e=videojs.dom.createEl("span",{className:"tcp-statistic-title",innerText:mapKey[this.options_.key]+":"}),i=videojs.dom.createEl("span",{className:"tcp-statistic-data",innerText:this.options_.data});return t.appendChild(e),t.appendChild(i),t},e.prototype.updateTextNode_=function(){for(var t=this.el_.lastChild;t.firstChild;)t.removeChild(t.firstChild);this.displayData&&this.show(),this.textNode_=document_1.createTextNode(this.displayData||""),t.appendChild(this.textNode_)},e.prototype.updateContent=function(t){t!==this.displayData&&(this.displayData=t,this.requestAnimationFrame(this.updateTextNode_))},e.prototype.isDisplay=function(){return this.display},e.prototype.show=function(){t.prototype.show.call(this),this.display=!0,this.popped=!0},e.prototype.hide=function(){t.prototype.hide.call(this),this.display=!1,this.popped=!1},e.prototype.reset=function(){t.prototype.hide.call(this),this.display=!1,this.displayData=""},e}(Component$7);videojs.registerComponent("VideoTextItem",VideoTextItem);var defaultConfig={enableRecoverMediaError:!0,recoverMediaErrorMaxRetry:5,fragLoadingMaxRetry:3},Html5HlsJS=function(){function t(e,i,n){classCallCheck(this,t);var r=new Hls$1(videojs.mergeOptions(defaultConfig,n.hlsConfig)),o=i.el();this.Hls=Hls$1,this.tech=i,this.hls=r,this.switchStatus="",this.manifests=[],this.subtitlesLoadedCount=0,this.subtitles=[],this.hlsjsErrorHandler=this.errorHandlerFactory(),r.on(Hls$1.Events.ERROR,this.onError.bind(this)),r.on(Hls$1.Events.MANIFEST_PARSED,videojs.bind(this,this.onMetaData)),r.on(Hls$1.Events.MANIFEST_LOADED,this.loadSubtiles.bind(this)),r.on(Hls$1.Events.LEVEL_LOADED,this.onLevelLoaded.bind(this)),this.tech.player().on("ready",this.addHlsSubtitles.bind(this));for(var s in Hls$1.Events)r.on(Hls$1.Events[s],videojs.bind(this,this.onEvent));r.attachMedia(o),r.loadSource(e.src)}return t.prototype.switchQuality=function(t){t.id!=this.hls.currentLevel&&(this.switchStatus="switching",this.switchData=t,this.tech.trigger({type:"hlsresolutionswitching",data:this.switchData}),this.hls.nextLevel=t.id)},t.prototype.dispose=function(){this.hls.destroy()},t.prototype.onSubtitleLoaded=function(t,e){var i=this,n=this.tech.player(),r=this.subtitlesLoadedCount,o=this.hls.subtitleTracks;this.subtitles[r].url=e.details.fragments[0].url,++this.subtitlesLoadedCount<o.length?this.hls.trigger(Hls$1.Events.SUBTITLE_TRACK_LOADING,{url:o[this.subtitlesLoadedCount].url,id:o[this.subtitlesLoadedCount].id}):n.ready(function(){i.addHlsSubtitles()})},t.prototype.loadSubtiles=function(){var t=this,e=this.hls.subtitleTracks;if(e.length){e.forEach(function(e){t.subtitles.push({name:e.name,lang:e.lang,loaded:!1})});var i=e[0];this.hls.on(Hls$1.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleLoaded.bind(this)),this.hls.trigger(Hls$1.Events.SUBTITLE_TRACK_LOADING,{url:i.url,id:i.id})}},t.prototype.addHlsSubtitles=function(){var t=this;this.subtitles.length&&this.subtitles.some(function(t){return!t.loaded})&&this.subtitles.forEach(function(e){var i=t.tech.addRemoteTextTrack({src:e.url,kind:"subtitles",srclang:e.lang,label:e.name
},!0);i.addEventListener("load",function(){e.loaded=!0})})},t.prototype.onEvent=function(t,e){this.tech.trigger({type:t,data:e});var i=this.tech.player();switch(t){case Hls$1.Events.MANIFEST_LOADED:this.manifests.push(e.networkDetails.response||e.networkDetails.responseText);break;case Hls$1.Events.LEVEL_SWITCHING:break;case Hls$1.Events.LEVEL_SWITCHED:"switching"==this.switchStatus&&(this.switchStatus="switched",this.tech.trigger({type:"hlsresolutionswitched",data:this.switchData}),this.switchData=null);break;case Hls$1.Events.LEVEL_SWITCH_ERROR:i.trigger({type:"levelSwitchError"})}},t.prototype.onMetaData=function(t,e){var i=[],n=this.hls,r=this;if(e.levels.length>1){var o={id:-1,label:"auto",selected:-1===n.manualLevel};i.push(o),e.levels.forEach(function(t,e){var o={};o.id=e,o.selected=e===n.manualLevel,o.label=r.getLevelLabel(t),i.push(o)});var s={qualityData:{video:i},callbacks:{video:videojs.bind(this,this.switchQuality)}};this.tech.setTimeout(function(){this.trigger({type:"masterplaylistchange",data:s})},1)}},t.prototype.getLevelLabel=function(t){return t.height?t.height+"p":t.width?Math.round(9*t.width/16)+"p":t.bitrate?t.bitrate/1e3+"kbps":0},t.prototype.onLevelLoaded=function(t,e){this._duration=e.details.live?Infinity:e.details.totalduration},t.prototype.onError=function(t,e){var i=this.tech.player();if(e.fatal)switch(e.type){case Hls$1.ErrorTypes.NETWORK_ERROR:this.hls.startLoad(),i.error({code:14,source:e}),i.error(null);break;case Hls$1.ErrorTypes.MEDIA_ERROR:this.hls.config.enableRecoverMediaError?this.hlsjsErrorHandler(e):i.error({code:15,source:e});break;case Hls$1.ErrorTypes.MUX_ERROR:i.error({code:16,source:e});break;case Hls$1.ErrorTypes.OTHER_ERROR:default:i.error({code:17,source:e})}else switch(e.details){case Hls$1.ErrorDetails.KEY_LOAD_ERROR:}},t.prototype.errorHandlerFactory=function(){var t=this.hls,e=this.tech.player(),i=null,n=null,r=0;return function(o){var s=Date.now();r==t.config.recoverMediaErrorMaxRetry?e.error({code:15,source:o}):(!i||s-i>2e3?(i=s,t.recoverMediaError()):(!n||s-n>2e3)&&(n=s,t.swapAudioCodec(),t.recoverMediaError()),r++)}},t.prototype.duration=function(){return this._duration},t}(),hlsTypeRE=/^application\/(x-mpegURL|vnd\.apple\.mpegURL)$/i,hlsExtRE=/\.m3u8/i,HlsSourceHandler={name:"hlsSourceHandler",canHandleSource:function(t){return t.skipHlsJs||t.keySystems&&t.keySystems["com.apple.fps.1_0"]?"":hlsTypeRE.test(t.type)?"probably":hlsExtRE.test(t.src)?"maybe":""},handleSource:function(t,e,i){return e.hlsProvider?(e.hlsProvider.dispose(),e.hlsProvider=null):i.hlsConfig&&!1===i.hlsConfig.autoStartLoad&&e.on("play",function(){this.player().hasStarted()||this.hlsProvider.hls.startLoad()}),e.hlsProvider=new Html5HlsJS(t,e,i),e.hlsProvider},canPlayType:function(t){return hlsTypeRE.test(t)?"probably":""}};TCPlayer.mountHlsProvider=mountHlsProvider,mountHlsProvider();var Html5DashJS=function(){function t(e,i,n){var r=this;if(classCallCheck(this,t),n=n||i.options_,this.player=videojs(n.playerId),this.player.dash=this.player.dash||{},this.tech_=i,this.el_=i.el(),this.elParent_=this.el_.parentNode,this.hasFiniteDuration_=!1,e.src){i.isReady_=!1,t.updateSourceData&&(videojs.log.warn('updateSourceData has been deprecated. Please switch to using hook("updatesource", callback).'),e=t.updateSourceData(e)),t.hooks("updatesource").forEach(function(t){e=t(e)});var o=e.src;this.keySystemOptions_=t.buildDashJSProtData(e.keySystemOptions||e.keySystems),this.player.dash.mediaPlayer=dashjs.MediaPlayer().create(),this.mediaPlayer_=this.player.dash.mediaPlayer,t.useVideoJSDebug&&(videojs.log.warn('useVideoJSDebug has been deprecated. Please switch to using hook("beforeinitialize", callback).'),t.useVideoJSDebug(this.mediaPlayer_)),t.beforeInitialize&&(videojs.log.warn('beforeInitialize has been deprecated. Please switch to using hook("beforeinitialize", callback).'),t.beforeInitialize(this.player,this.mediaPlayer_)),t.hooks("beforeinitialize").forEach(function(t){t(r.player,r.mediaPlayer_)}),this.mediaPlayer_.initialize(),this.retriggerError_=function(t){if("capability"===t.error&&"mediasource"===t.event)r.player.error({code:4,message:"The media cannot be played because it requires a feature that your browser does not support."});else if("manifestError"!==t.error||"createParser"!==t.event.id&&"codec"!==t.event.id&&"nostreams"!==t.event.id&&"nostreamscomposed"!==t.event.id&&"parse"!==t.event.id&&"multiplexedrep"!==t.event.id)if("mediasource"===t.error)t.event.match("MEDIA_ERR_ABORTED")?r.player.error({code:1,message:t.event}):t.event.match("MEDIA_ERR_NETWORK")?r.player.error({code:2,message:t.event}):t.event.match("MEDIA_ERR_DECODE")?r.player.error({code:3,message:t.event}):t.event.match("MEDIA_ERR_SRC_NOT_SUPPORTED")?r.player.error({code:4,message:t.event}):t.event.match("MEDIA_ERR_ENCRYPTED")?r.player.error({code:5,message:t.event}):(t.event.match("UNKNOWN"),r.player.error({code:4,message:t.event}));else if("capability"===t.error&&"encryptedmedia"===t.event)r.player.error({code:5,message:"The media cannot be played because it requires encryption features that your browser does not support."});else if("key_session"===t.error)r.player.error({code:5,message:t.event});else if("download"===t.error)r.player.error({code:2,message:"The media playback was aborted because too many consecutive download errors occurred."});else{if("mssError"!==t.error)return;r.player.error({code:3,message:t.event})}else r.player.error({code:4,message:t.event.message});setTimeout(function(){r.mediaPlayer_.reset()},10)},this.mediaPlayer_.on(dashjs.MediaPlayer.events.ERROR,this.retriggerError_),this.getDuration_=function(t){var e=t.data.Period_asArray,i=r.hasFiniteDuration_;t.data.mediaPresentationDuration||e[e.length-1].duration?r.hasFiniteDuration_=!0:r.hasFiniteDuration_=!1,r.hasFiniteDuration_!==i&&r.player.trigger("durationchange")},this.mediaPlayer_.on(dashjs.MediaPlayer.events.MANIFEST_LOADED,this.getDuration_),this.mediaPlayer_.on(dashjs.MediaPlayer.events.STREAM_INITIALIZED,videojs.bind(this,this.onMetaData)),this.currentQuality={video:"",audio:""},Object.keys(dashjs.MediaPlayer.events).forEach(function(t){r.mediaPlayer_.on(dashjs.MediaPlayer.events[t],videojs.bind(r,r.onEvent))}),n.dashConfig&&Object.keys(n.dashConfig).forEach(function(t){var e,i="set"+t.charAt(0).toUpperCase()+t.slice(1),o=n.dashConfig[t];if(r.mediaPlayer_.hasOwnProperty(i)&&(videojs.log.warn("Using dash options in videojs-contrib-dash without the set prefix has been deprecated. Change '"+t+"' to '"+i+"'"),t=i),!r.mediaPlayer_.hasOwnProperty(t))return void videojs.log.warn("Warning: dash configuration option unrecognized: "+t);Array.isArray(o)||(o=[o]),(e=r.mediaPlayer_)[t].apply(e,o)}),this.mediaPlayer_.attachView(this.el_),this.mediaPlayer_.setAutoPlay(!1),setupAudioTracks.call(null,this.player,i),setupTextTracks.call(null,this.player,i,n),this.mediaPlayer_.setProtectionData(this.keySystemOptions_),this.mediaPlayer_.attachSource(o),this.tech_.triggerReady()}}return t.buildDashJSProtData=function(t){var e={};if(!t)return null;if(Array.isArray(t))for(var i=0;i<t.length;i++){var n=t[i],r=videojs.mergeOptions({},n.options);r.licenseUrl&&(r.serverURL=r.licenseUrl,delete r.licenseUrl),e[n.name]=r}else e=t;return e},t.prototype.dispose=function(){var t=this;this.mediaPlayer_&&(this.mediaPlayer_.off(dashjs.MediaPlayer.events.ERROR,this.retriggerError_),this.mediaPlayer_.off(dashjs.MediaPlayer.events.MANIFEST_LOADED,this.getDuration_),Object.keys(dashjs.MediaPlayer.events).forEach(function(e){t.mediaPlayer_.off(dashjs.MediaPlayer.events[e],videojs.bind(t,t.onEvent))}),this.mediaPlayer_.reset()),this.player.dash&&delete this.player.dash},t.prototype.duration=function(){return this.mediaPlayer_.isDynamic()&&!this.hasFiniteDuration_?Infinity:this.mediaPlayer_.duration()},t.hooks=function(e,i){return t.hooks_[e]=t.hooks_[e]||[],i&&(t.hooks_[e]=t.hooks_[e].concat(i)),t.hooks_[e]},t.hook=function(e,i){t.hooks(e,i)},t.removeHook=function(e,i){var n=t.hooks(e).indexOf(i);return-1!==n&&(t.hooks_[e]=t.hooks_[e].slice(),t.hooks_[e].splice(n,1),!0)},t.prototype.onMetaData=function(t){var e=this,i=this.mediaPlayer_,n=i.getBitrateInfoListFor("video"),r=i.getSettings(),o=r.streaming.abr.autoSwitchBitrate.video,s=[];if(n.length>0){s.push({id:-1,label:"auto",selected:o}),n.forEach(function(t,i){s.push({id:i,label:e.getLevelLabel(t),selected:!o&&e.currentQuality.video===i})});var a={qualityData:{video:s},callbacks:{video:videojs.bind(this,this.switchQuality)}};this.tech_.setTimeout(function(){this.trigger({type:"masterplaylistchange",data:a})},1)}},t.prototype.switchQuality=function(t){var e=this.mediaPlayer_;-1===t.id?e.updateSettings({streaming:{abr:{autoSwitchBitrate:{video:!0}}}}):t.id!=this.currentQuality[t.trackType]&&(e.updateSettings({streaming:{abr:{autoSwitchBitrate:{video:!1}}}}),e.setQualityFor(t.trackType,t.id))},t.prototype.getLevelLabel=function(t){return t.height?t.height+"p":t.width?Math.round(9*t.width/16)+"p":t.bitrate?t.bitrate/1e3+"kbps":0},t.prototype.onEvent=function(t){switch(this.tech_.trigger({type:"dash_"+t.type,data:t}),t.type){case dashjs.MediaPlayer.events.QUALITY_CHANGE_REQUESTED:this.tech_.trigger({type:"dashqualityswitching",data:t});break;case dashjs.MediaPlayer.events.QUALITY_CHANGE_RENDERED:this.tech_.trigger({type:"dashqualityswitched",data:t}),this.currentQuality[t.mediaType]=t.newQuality}},t}();Html5DashJS.hooks_={};var canHandleKeySystems=function(t){t=JSON.parse(JSON.stringify(t)),Html5DashJS.updateSourceData&&(videojs.log.warn('updateSourceData has been deprecated. Please switch to using hook("updatesource", callback).'),t=Html5DashJS.updateSourceData(t)),Html5DashJS.hooks("updatesource").forEach(function(e){t=e(t)});var e=document_1.createElement("video");return!(t.keySystemOptions&&!window_1.navigator.requestMediaKeySystemAccess&&!e.msSetMediaKeys)};videojs.DashSourceHandler=function(){return{name:"dashSourceHandler",canHandleSource:function(t){var e=/\.mpd/i;return canHandleKeySystems(t)?videojs.DashSourceHandler.canPlayType(t.type)?"probably":e.test(t.src)?"maybe":"":""},handleSource:function(t,e,i){return new Html5DashJS(t,e,i)},canPlayType:function(t){return videojs.DashSourceHandler.canPlayType(t)}}},videojs.DashSourceHandler.canPlayType=function(t){return/^application\/dash\+xml/i.test(t)?"probably":""},TCPlayer.mountDashProvider=mountDashProvider,mountDashProvider(),videojs.Html5DashJS=Html5DashJS;var Component$6=videojs.getComponent("Component"),hlsOption=["fileID","requestID","mediaType","mimeType","provider","resolution","rate","frames","buffer","connectionSpeed"],mp4Option=["fileID","requestID","mediaType","mimeType","resolution","rate","buffer"],dashOption=["fileID","requestID","mediaType","mimeType","resolution","rate","buffer","provider","connectionSpeed"],infoOption=["info"],VideoStatisticWrapper=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.nodelist={},r.status={fileID:"",requestID:"",mediaType:"",mimeType:"",provider:"",resolution:"",rate:"",frames:"",buffer:"",connectionSpeed:"",info:""},i.on("playcgiend",function(){clearTimeout(r.timeoutID),Object.keys(r.status).forEach(function(t){r.nodelist[t].reset()})}),i.on("statistic",videojs.bind(r,function(t){"open"===t.data.action?r.tick():r.clearTimeout(r.timeoutID)})),r.init(),r}return inherits(e,t),e.prototype.tick=function(){var t=this;this.timeoutID=setTimeout(videojs.bind(this,function(){t.tick()}),1e3),this.updatePanel()},e.prototype.createEl=function(){return videojs.dom.createEl("div",{className:"tcp-statistic-wrapper"})},e.prototype.init=function(){var t=this;Object.keys(this.status).forEach(function(e){var i=new VideoTextItem(t,{key:e,data:t.status[e]});t.nodelist[e]=i,t.addChild(i)})},e.prototype.updatePanel=function(){var t=this;if("Html5"===this.player().techName_)switch(this.getStatus(),this.status.mediaType){case"m3u8":hlsOption.forEach(function(e){t.nodelist[e].updateContent(t.status[e])});break;case"mp4":mp4Option.forEach(function(e){t.nodelist[e].updateContent(t.status[e])});break;case"mpd":dashOption.forEach(function(e){t.nodelist[e].updateContent(t.status[e])})}else"Flash"===this.player().techName_&&(this.getFlashStatus(),infoOption.forEach(function(e){"info"===e&&t.nodelist[e].updateContent(t.status[e])}))},e.prototype.getStatus=function(){this.getFileID(),this.getRequestID(),this.getMediaType(),this.getMIMEType(),this.getResolution(),this.getRate(),this.getProvider(),this.getBuffer(),"m3u8"===this.status.mediaType&&(this.getFrames(),this.getConnectionSpeed()),"mpd"===this.status.mediaType&&this.getConnectionSpeed()},e.prototype.getFlashStatus=function(){this.status.mediaType="flash",this.getSupport()},e.prototype.getFileID=function(){var t=this.player().options_.fileID||"";return this.status.fileID=t,t},e.prototype.getRequestID=function(){var t=this.player().PlayerMetrics().playStatus.requestID;return this.status.requestID=t,t},e.prototype.getMediaType=function(){this.player_.src()&&/\/(.+)\.(.+)\?|\/(.+)\.(.+)/.test(this.player_.src());var t=RegExp.$2||RegExp.$4||"";return this.status.mediaType=t,t},e.prototype.getMIMEType=function(){var t=this.player().src(),e=EXT_MIME[getFileExtension(t)]||"";return this.status.mimeType=e,e},e.prototype.getResolution=function(){var t=this.player_.tech_.el_.videoHeight||0,e=this.player_.tech_.el_.videoWidth||0,i=e+" x "+t;return this.status.resolution=i,i},e.prototype.getRate=function(){var t=this.player().playbackRate()||1;return this.status.rate=t+"x",t},e.prototype.getBuffer=function(){var t=this.player_.bufferedEnd().toFixed(1)||0,e=this.player_.duration().toFixed(1)||0,i=(t/e*100).toFixed(1)+"%",n=t+" / "+e+" ("+i+")";return this.status.buffer=n,n},e.prototype.getSupport=function(){var t=void 0;return t="m3u8"!==this.status.mediaType&&"mp4"!==this.status.mediaType&&"dash"!==this.status.mediaType?"视频统计功能不支持当前视频格式":"",this.status.info=t,t},e.prototype.getProvider=function(){var t=this.player_.tech_.sourceHandler_;return t instanceof Html5HlsJS?(this.status.provider="HlsJS","HlsJS"):t instanceof Html5DashJS?(this.status.provider="DashJS","DashJS"):""},e.prototype.getFrames=function(){var t=this.player_.getVideoPlaybackQuality(),e=(t.droppedVideoFrames||0)+" dropped of "+(t.totalVideoFrames||0);return this.status.frames=e,e},e.prototype.getConnectionSpeed=function(){var t=void 0;if("m3u8"===this.status.mediaType){var e=this.player_.tech_.hlsProvider.hls.abrController._bwEstimator;t=e&&e.getEstimate()?(125e-6*e.getEstimate()).toFixed(0)+"KB/s":"0KB/s",this.status.connectionSpeed=t}if("mpd"===this.status.mediaType){var i=this.player_.dash.mediaPlayer,n=i.getDashMetrics(),r=i.getDashAdapter(),o=i.getActiveStream().getStreamInfo(),s=o.index,a=n.getCurrentRepresentationSwitch("video").to,l=r.getBandwidthForRepresentation(a,s);t=l?(125e-6*l).toFixed(0)+"KB/s":"0KB/s",this.status.connectionSpeed=t}return t},e}(Component$6);videojs.registerComponent("VideoStatisticWrapper",VideoStatisticWrapper);var Component$5=videojs.getComponent("Component"),VideoStatisticPanel=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return i.on("statistic",videojs.bind(r,function(t){"open"===t.data.action?r.show():r.hide()})),i.on("playcgistart",function(){i.trigger({type:"statistic",data:{action:"close"}})}),r.init(),r}return inherits(e,t),e.prototype.createEl=function(){var t=this,e=videojs.dom.createEl("div",{className:"tcp-statistic vjs-hidden"}),i=videojs.dom.createEl("div",{className:"tcp-statistic-close",innerText:"[X]"});return i.addEventListener("click",function(){t.hide(),t.player().trigger({type:"statistic",data:{action:"close"}})}),e.appendChild(i),e},e.prototype.init=function(){this.addChild("VideoStatisticWrapper")},e.prototype.show=function(){t.prototype.show.call(this),this.popped=!0},e.prototype.hide=function(){t.prototype.hide.call(this),this.popped=!1},e}(Component$5);videojs.registerComponent("VideoStatisticPanel",VideoStatisticPanel);var MenuItem$3=videojs.getComponent("MenuItem"),MirrorMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.addClass("tc-menu-list"),r.open=!1,r.hasOpen=!1,r.player=i,r.setup(),r}return inherits(e,t),e.prototype.setup=function(){var t=this;this.on(["tap","click"],function(){var e=t.player.tech().el();t.hasOpen||(window.MtaH5&&MtaH5.clickStat("mirror",{appid:t.player.options_.appID,fileid:t.player.options_.fileID}),t.player.trigger({type:"feature",data:"mirror"})),t.open?(videojs.dom.removeClass(e,"tcp-mirror"),videojs.dom.removeClass(t.el_,"tcp-menu-item-select")):(videojs.dom.addClass(e,"tcp-mirror"),videojs.dom.addClass(t.el_,"tcp-menu-item-select"),t.hasOpen=!0),t.open=!t.open})},e}(MenuItem$3);videojs.registerComponent("MirrorMenuItem",MirrorMenuItem);var MenuItem$4=videojs.getComponent("MenuItem"),PoweredByMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.addClass("tc-menu-list"),r}return inherits(e,t),e}(MenuItem$4);videojs.registerComponent("PoweredByMenuItem",PoweredByMenuItem);var MenuItem$5=videojs.getComponent("MenuItem"),StatusMenuItem=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.addClass("tc-menu-list"),r.hasOpen=!1,r.player=i,i.on("statistic",videojs.bind(r,function(t){"close"===t.data.action&&(videojs.dom.removeClass(r.el_,"tcp-menu-item-select"),r.hasOpen=!1)})),r.setup(),r}return inherits(e,t),e.prototype.setup=function(){var t=this;this.on(["tap","click"],function(){t.hasOpen?(videojs.dom.removeClass(t.el_,"tcp-menu-item-select"),t.player.trigger({type:"statistic",data:{action:"close"}}),t.hasOpen=!1):(videojs.dom.addClass(t.el_,"tcp-menu-item-select"),t.player.trigger({type:"statistic",data:{action:"open"}}),t.hasOpen=!0,window.MtaH5&&MtaH5.clickStat("mirror",{appid:t.player.options_.appID,fileid:t.player.options_.fileID}),t.player.trigger({type:"feature",data:"statistic"}))})},e}(MenuItem$5);videojs.registerComponent("StatusMenuItem",StatusMenuItem);var Component$8=videojs.getComponent("Component"),Menu$2=videojs.getComponent("Menu"),MenuItem$2=videojs.getComponent("MenuItem"),RightClickPopupMenu=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));r.hide(),r.addChild("PoweredByMenuItem",{label:"Powered by Tencent Cloud."}),!n.statistic||IS_IOS||IS_ANDROID||r.addChild("StatusMenuItem",{label:"Video statistic"}),n.mirror&&r.addChild("MirrorMenuItem",{label:"Mirror"}),i.on("contextmenu",videojs.bind(r,r.onContextmenu)),i.on(["tap","click"],videojs.bind(r,function(t){if(this.popped)return this.hide(),t.stopPropagation(),t.preventDefault(),!1})),videojs.on(document,["tap","click"],videojs.bind(r,function(t){this.popped&&this.hide()}));var o=r;return r.children().forEach(function(t){t.on(["tap","click"],function(){o.hide()})}),r}return inherits(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this);return videojs.dom.addClass(e,"tcp-right-click-popup-menu"),e},e.prototype.show=function(){t.prototype.show.call(this),this.popped=!0},e.prototype.hide=function(){t.prototype.hide.call(this),this.popped=!1},e.prototype.onContextmenu=function(t){t.preventDefault(),this.show();var e=this.el(),i=t.clientX,n=t.clientY,r=getViewportSize(),o=r.width,s=r.height,a=i+e.offsetWidth-o+5;a=Math.max(0,a);var l=n+e.offsetHeight-s+5;l=Math.max(0,l);var u=this.player().el().getBoundingClientRect();e.style.left=Math.max(0,i-u.left-a)+"px",e.style.top=Math.max(0,n-u.top-l)+"px"},e}(Menu$2);videojs.registerComponent("RightClickPopupMenu",RightClickPopupMenu);var clickableComponent=videojs.getComponent("ClickableComponent"),TimeTooltip$2=videojs.getComponent("TimeTooltip"),Component$9=videojs.getComponent("Component"),dom=videojs.dom,formatTime$2=videojs.formatTime,COMMENT_WIDTH=140,COMMENT_HEIGHT_SINGLE_LINE=23,COMMENT_SHOW_ON_THE_LEFT={POPUP_CONTAINER_OFFSET:-130,POPUP_CONTAINER_OFFSET_WITHOUTGIT:-100,TRIANGLE_OFFEST:96,TRIANGLE_OFFEST_WITHOUTGIF:95},COMMENT_SHOW_ON_THE_RIGHT={TRIANGLE_OFFEST:6,POPUP_CONTAINER_OFFSET:0},ProgressMarkerGenerator=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.duration=0,r.registerListener(),r}return inherits(e,t),e.prototype.registerListener=function(){this.player_.one("canplay",videojs.bind(this,this.resetDotsElement))},e.prototype.resetDotsElement=function(){var t=this.player_;this.duration=t.duration(),dom.emptyEl(this.el_),this.resetEl()},e.prototype.resetEl=function(){this.setUpEl()},e.prototype.dotMouseLeave=function(){var t=event.currentTarget,e=t.getAttribute("container-id");this.getChildById(e).hide()},e.prototype.dotHover=function(){var t=event.currentTarget.parentElement.offsetWidth,e=event.currentTarget.offsetLeft+COMMENT_WIDTH,i=this.getChildById(event.currentTarget.getAttribute("container-id")),n=i.getAttribute("class").search("tcp-without-gif")>=0,r=i.el_.querySelector(".tcp-triangle-both")||i.el_.querySelector(".tcp-triangle");e>t?n?(i.el_.style.left=COMMENT_SHOW_ON_THE_LEFT.POPUP_CONTAINER_OFFSET_WITHOUTGIT+"px",r.style.left=COMMENT_SHOW_ON_THE_LEFT.TRIANGLE_OFFEST_WITHOUTGIF+"%"):(i.el_.style.left=COMMENT_SHOW_ON_THE_LEFT.POPUP_CONTAINER_OFFSET+"px",r.style.left=COMMENT_SHOW_ON_THE_LEFT.TRIANGLE_OFFEST+"%"):event.currentTarget.offsetLeft<=COMMENT_WIDTH&&!n&&(i.el_.style.left=COMMENT_SHOW_ON_THE_RIGHT.POPUP_CONTAINER_OFFSET+"px",r.style.left=COMMENT_SHOW_ON_THE_RIGHT.TRIANGLE_OFFEST+"px");var o=i.getChild("DotImage");if(o){var s=o.el_.getElementsByClassName("tcp-dot-timestamp")[0],a=event.currentTarget.getAttribute("dotinsecond"),l=formatTime$2(a);s.innerHTML=l}i.show()},e.prototype.setChildInTheContainer=function(t,e){var i=t.content&&""!==t.content?decodeURIComponent(t.content.replace(/\+/g," ")):"";if(""!==i){var n=e.addChild("DotComment");n.el_.innerHTML=encodeHTML(i)}if(t.img){e.el_.querySelector(".tcp-triangle").setAttribute("class","tcp-triangle-both");var r=e.addChild("DotImage");if(r.el_.firstChild.setAttribute("src",t.img),""==i)e.addClass("tcp-only-gif"),r.el_.firstChild.className="tcp-image-in-container-only-image";else{this.countContentSize(i)<=20&&(e.addClass("tcp-single-line"),n.el_.style.height=COMMENT_HEIGHT_SINGLE_LINE+"px")}}else if(""!==i){var o=this.countContentSize(i);o<=14?e.addClass("tcp-single-line"):o<=28&&e.addClass("tcp-two-lines"),e.addClass("tcp-without-gif"),n.addClass("tcp-large-comment")}},e.prototype.markSetUp=function(t){var e=t.timeOffset>0?t.timeOffset/1e3:0,i=this.duration?this.duration:0;if(!("number"!=typeof e||e>i)){var n=document.createElement("div");n.className="tcp-dot-basic-style",n.setAttribute("dotinsecond",e.toString()),t["class"]&&(n.className+=" "+t["class"]);var r=this.addChild("PopUpContainer");return n.setAttribute("container-id",r.id_),this.setChildInTheContainer(t,r),n.appendChild(r.el_),n.style.left=e/i*100+"%",n.addEventListener("mouseover",videojs.bind(this,this.dotHover)),n.addEventListener("mouseleave",videojs.bind(this,this.dotMouseLeave)),n}},e.prototype.setUpEl=function(){var e=this,i=this.player_.options_.dots||0,n=this.el_||t.prototype.createEl.call(this);return videojs.dom.addClass(n,"tcp-dot-component"),i&&i instanceof Array&&i.length>0&&i.forEach(function(t){var i=t.content||"";if(i&&""!=i||t.img){var r=e.markSetUp(t);r&&n.appendChild(r)}}),n},e.prototype.countContentSize=function(t){for(var e=0,i=t.length,n=-1,r=0;r<i;r++)n=t.charCodeAt(r),e+=n>=0&&n<=128?1:2;return e},e.prototype.createEl=function(){return this.setUpEl()},e}(clickableComponent);videojs.registerComponent("ProgressMarkerGenerator",ProgressMarkerGenerator);var DotComment=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.addClass("tcp-dot-comment"),r}return inherits(e,t),e}(Component$9);videojs.registerComponent("DotComment",DotComment);var DotImage=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));r.addClass("tcp-dot-image");var o=r.el_||t.prototype.createEl.call(r),s=dom.createEl("img");s.className="tcp-image-in-container",o.appendChild(s);var a=dom.createEl("span");return a.className="tcp-dot-timestamp",a.innerHTML="",o.appendChild(a),r}return inherits(e,t),e}(Component$9);videojs.registerComponent("DotImage",DotImage);var PopUpContainer=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));r.addClass("tcp-popup-container");var o=dom.createEl("div");return o.setAttribute("class","tcp-triangle"),r.el_.appendChild(o),r.hide(),r}return inherits(e,t),e}(TimeTooltip$2);videojs.registerComponent("PopUpContainer",PopUpContainer);var Plugin$2=videojs.getPlugin("plugin"),SpeedUp=function(t){function e(i){classCallCheck(this,e);var n=possibleConstructorReturn(this,t.call(this,i));return n.TARGET_LANTENCY=1,n.MAX_LATENCY=3,n.RATEVALUE=1.1,i.ready(videojs.bind(n,n.init)),n}return inherits(e,t),e.prototype.checkLatency=function(){var t=this.player.bufferedEnd()-this.player.currentTime();t>this.MAX_LATENCY&&this.player.playbackRate(this.RATEVALUE),t<this.TARGET_LANTENCY&&this.player.playbackRate(1)},e.prototype.init=function(){var t=this,e=this.player,i=void 0;e.on("playing",function(){e.duration()===Infinity&&(i=setInterval(t.checkLatency.bind(t),1e3))}),e.on("waiting",function(){t.reset(i)}),e.on("pause",function(){t.reset(i)})},e.prototype.reset=function(t){this.player.playbackRate(1),clearInterval(t)},e}(Plugin$2);videojs.registerPlugin("SpeedUp",SpeedUp);var Button$3=videojs.getComponent("Button"),BigPlayButton$2=videojs.getComponent("BigPlayButton");BigPlayButton$2.prototype.createEl=function(){var t=Button$3.prototype.createEl.call(this),e=getTemplate();return(this.options_.shapeType||this.options_.styleType)&&(e=getTemplate(this.options_.shapeType,this.options_.styleType)),t.appendChild(videojs.dom.createEl("div",{className:"vjs-button-icon",innerHTML:e})),t},BigPlayButton$2.prototype.controlText_="Play";var Component$10=videojs.getComponent("Component"),VideoStatisticWrapper$1=videojs.getComponent("VideoStatisticWrapper"),LoadingSpinner$2=videojs.getComponent("LoadingSpinner");LoadingSpinner$2.prototype.createEl=function(){function t(t,e){var i=new VideoStatisticWrapper$1(t);if(i.getMediaType(),(i.getBuffer()||"").indexOf("100.0%")>-1)return e.innerText="",!1;e.innerText=i.getConnectionSpeed()}var e=this,i=Component$10.prototype.createEl.call(this,"div",{className:"tcp-loading-spinner",dir:"ltr"});if(!this.player_.options_.loadingSpeed)return i;var n=null;return this.player_.on("waiting",function(){n=setInterval(function(){t(e.player_,i)},100)}),this.player_.on("timeupdate",function(){n&&clearInterval(n)}),i};var Plugin$3=videojs.getPlugin("plugin"),log$3=videojs.log,Skin=function(t){function e(i,n){classCallCheck(this,e),log$3("Skin initializing");var r=possibleConstructorReturn(this,t.call(this,i));return r.player.addClass("tcp-skin"),log$3("Skin initialized"),r}return inherits(e,t),e}(Plugin$3);videojs.registerPlugin("Skin",Skin);var Plugin$4=videojs.getPlugin("plugin"),log$4=videojs.log,VID=function(t){function e(i,n){classCallCheck(this,e),log$4("VID initializing");var r=possibleConstructorReturn(this,t.call(this,i));return r.init(r.player.options_),log$4("VID initialized"),r}return inherits(e,t),e.prototype.init=function(t){var e=(this.player,t.sources);t&&t.appID&&t.fileID||t.plugins.DRM&&0==e.length?t.children.splice(0,"mediaLoader"==t.children[0]?1:0,"mediaAsyncLoader"):"mediaLoader"!=t.children[0]&&t.children.unshift("mediaLoader")},e}(Plugin$4);videojs.registerPlugin("VID",VID);var isObject_1=isObject$2,freeGlobal="object"==typeof commonjsGlobal&&commonjsGlobal&&commonjsGlobal.Object===Object&&commonjsGlobal,_freeGlobal=freeGlobal,freeSelf="object"==typeof self&&self&&self.Object===Object&&self,root=_freeGlobal||freeSelf||Function("return this")(),_root=root,now=function(){return _root.Date.now()},now_1=now,reWhitespace=/\s/,_trimmedEndIndex=trimmedEndIndex,reTrimStart=/^\s+/,_baseTrim=baseTrim,Symbol$1=_root.Symbol,_Symbol=Symbol$1,objectProto=Object.prototype,hasOwnProperty$2=objectProto.hasOwnProperty,nativeObjectToString=objectProto.toString,symToStringTag$1=_Symbol?_Symbol.toStringTag:undefined,_getRawTag=getRawTag,objectProto$1=Object.prototype,nativeObjectToString$1=objectProto$1.toString,_objectToString=objectToString,nullTag="[object Null]",undefinedTag="[object Undefined]",symToStringTag=_Symbol?_Symbol.toStringTag:undefined,_baseGetTag=baseGetTag,isObjectLike_1=isObjectLike,symbolTag="[object Symbol]",isSymbol_1=isSymbol,NAN=NaN,reIsBadHex=/^[-+]0x[0-9a-f]+$/i,reIsBinary=/^0b[01]+$/i,reIsOctal=/^0o[0-7]+$/i,freeParseInt=parseInt,toNumber_1=toNumber,FUNC_ERROR_TEXT$1="Expected a function",nativeMax=Math.max,nativeMin=Math.min,debounce_1=debounce,FUNC_ERROR_TEXT="Expected a function",throttle_1=throttle$1,Component$11=videojs.getComponent("Component"),DvrProgressControl=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.on("mousemove",r.handleMouseMove),r.on("mouseup",r.handleMouseUp),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control tcp-dvr-progress-control"})},e.prototype.update=function(t){this.getChild("DvrSeekBar").update(t)},e.prototype.handleMouseMove=function(t){var e=this.getChild("DvrSeekBar"),i=e.calculateDistance(t),n=e.getChild("DvrMouseTimeDisplay");n&&n.update(videojs.dom.getBoundingClientRect(e.el()),i)},e.prototype.handleMouseUp=function(t){this.getChild("DvrSeekBar").handleMouseUp(t)},e}(Component$11);DvrProgressControl.prototype.options_={children:["DvrSeekBar"]},videojs.registerComponent("DvrProgressControl",DvrProgressControl);var Slider$2=videojs.getComponent("Slider"),DvrSeekBar=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.percent_=1,r.update=throttle_1(videojs.bind(r,r.update),50),r.on(i,"seekToLive",videojs.bind(r,function(t){this.update(t.data)})),r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},e.prototype.update=function(t){t!=undefined&&(this.percent_=t,this.bar.update(videojs.dom.getBoundingClientRect(this.el_),t),this.updateAriaAttributes(t))},e.prototype.handleMouseDown=function(e){t.prototype.handleMouseDown.call(this,e),this.isMouseDown=!0},e.prototype.handleMouseMove=function(t){var e=this.calculateDistance(t);this.update(e)},e.prototype.handleMouseUp=function(e){t.prototype.handleMouseUp.call(this);var i=this.calculateDistance(e);this.update(i),this.player().Dvr().timeShift(i)},e.prototype.stepBack=function(){},e.prototype.stepForward=function(){},e.prototype.updateAriaAttributes=function(t){this.el().setAttribute("aria-valuenow",(100*t).toFixed(2))},e.prototype.getPercent=function(){return this.percent_},e}(Slider$2);DvrSeekBar.prototype.options_={children:["DvrMouseTimeDisplay","DvrTimeShiftBar"],barName:"DvrTimeShiftBar"},videojs.registerComponent("DvrSeekBar",DvrSeekBar);var DvrTimeShiftBar=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.el_.style.width="100%",r}return inherits(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar tcp-dvr-time-shift",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Progress")+"</span>: 100%</span>"})},e.prototype.update=function(t,e){var i=(100*e).toFixed(2)+"%";this.el_.style.width=i},e}(Component$11);videojs.registerComponent("DvrTimeShiftBar",DvrTimeShiftBar);var MouseTimeDisplay$2=videojs.getComponent("MouseTimeDisplay"),DvrMouseTimeDisplay=function(t){function e(){return classCallCheck(this,e),
possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.update=function(t,e){var i=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var n=i.player().Dvr().dvrData.maxTimeShift,r=videojs.formatTime((1-e)*n,n);i.el_.style.left=t.width*e+"px",i.getChild("timeTooltip").update(t,e,r)})},e}(MouseTimeDisplay$2);videojs.registerComponent("DvrMouseTimeDisplay",DvrMouseTimeDisplay);var Button$4=videojs.getComponent("Button"),LiveButton=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){var t=Button$4.prototype.createEl.call(this,"button",{className:"vjs-live-control vjs-control"});return this.contentEl_=videojs.dom.createEl("div",{className:"vjs-live-display",innerHTML:this.localize("LIVE")},{"aria-live":"off"}),t.appendChild(this.contentEl_),t},e.prototype.update=function(){},e.prototype.updateControlText=function(t){this.controlText(t?"直播中":"返回直播")},e.prototype.handleClick=function(t){this.player().Dvr().seekToLive()},e}(Button$4);LiveButton.prototype.controlText_="返回直播",videojs.registerComponent("LiveButton",LiveButton);var Plugin$5=videojs.getPlugin("plugin"),Dvr=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));r.isInit=!1,r.options=n;var o=r;return i.ready(function(){var t=i.tech(!0),e=t&&t.hlsProvider;if(e&&e.Hls){var n=e.Hls;t.one(n.Events.MANIFEST_LOADED,function(t){}),t.one(n.Events.LEVEL_LOADED,function(t){}),i.one("loadedmetadata",function(e){t.el_.setAttribute("poster",""),o.init(),o.options.liveURL&&o.seekToLive()})}}),r}return inherits(e,t),e.prototype.init=function(){var t=this.player,e=t.tech(!0).hlsProvider;if(this.parseM3u8(e.manifests[0]),!this.dvrData.startTime&&!this.isInit)return void t.one("loadedmetadata",videojs.bind(this,function(t){this.init()}));this.initControl(),this.delay=getParams("delay",t.tech_.currentSource_.src)||0,this.updateControl(!this.isLive()),t.on("loadedmetadata",videojs.bind(this,function(){this.parseM3u8(e.manifests[0]),this.delay=getParams("delay",t.tech_.currentSource_.src)||0,this.updateControl()})),this.isInit=!0},e.prototype.initControl=function(){var t=this.player;t.addClass("vjs-dvr"),t.controlBar.getChild("ProgressControl").hide(),t.controlBar.getChild("LiveDisplay").hide(),t.controlBar.addChild("DvrProgressControl",{},5),t.controlBar.addChild("liveButton",{},6)},e.prototype.updateControl=function(t){var e=this.player,i=e.controlBar.getChild("ProgressControl"),n=e.controlBar.getChild("LiveDisplay"),r=e.controlBar.getChild("DvrProgressControl"),o=e.controlBar.getChild("LiveButton");e.toggleClass("vjs-dvr-live",this.isLive()),o.updateControlText(this.isLive()),t&&r.update(1-this.delay/this.dvrData.maxTimeShift),this.dvrData.startTime?(r.show(),o.show(),i.hide(),n.hide()):(r.hide(),o.hide(),i.show(),n.show())},e.prototype.seekToLive=function(){this.isLive()||(this.timeShift(1),this.player.trigger({type:"seekToLive",data:1}))},e.prototype.isLive=function(){return!(this.delay>0)},e.prototype.timeShift=function(t){var e=this.player,i=(e.getChild("ControlBar").getChild("LiveButton"),e.tech_.currentSource_),n=Math.floor(this.dvrData.maxTimeShift*(1-t));this.delay=n,0==n&&this.options.liveURL?e.src(this.options.liveURL):(i.src=(this.options.shiftURL||i.src).replace(/delay=*(\d+)/,"delay="+n),e.src(e.tech_.currentSource_.src)),e.bigPlayButton.hide(),e.posterImage.hide(),e.tech_.one("hlsManifestParsed",videojs.bind(this,function(){e.play()}))},e.prototype.parseM3u8=function(t){this.dvrData={};for(var e=new RegExp([/#EXT-TX-TS-START-TIME:*(.+)/.source,/|#EXT-TX-TS-DURATION:*(.+)/.source].join(""),"g"),i=void 0;null!==(i=e.exec(t));)i[1]?this.dvrData.startTime=i[1]:i[2]&&(this.dvrData.duration=i[2]);this.dvrData.startTime&&(this.dvrData.maxTimeShift=Math.min(Math.floor((new Date).getTime()/1e3-this.dvrData.startTime),this.dvrData.duration))},e}(Plugin$5);videojs.registerPlugin("Dvr",Dvr);var Menu$4=videojs.getComponent("Menu"),QualitySwitcherMenu=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.addItem=function(e){t.prototype.addItem.call(this,e)},e}(Menu$4),MenuItem$6=videojs.getComponent("MenuItem"),QualitySwitcherMenuItem=function(t){function e(i,n){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.handleClick=function(e){if(t.prototype.handleClick.call(this,e),this.updateItems(),this.options_.callback(this.options_),"video"==this.options_.trackType){var i=this.player().controlBar.getChild(this.options_.trackType+"QualitySwitcherMenuButton");i.updateLabel(this.options_),i.unpressButton()}},e.prototype.updateItems=function(){for(var t=this.player().controlBar.getChild(this.options_.trackType+"QualitySwitcherMenuButton"),e=t.getChild(this.options_.trackType+"QualitySwitcherMenu"),i=e.children(),n=0;n<i.length;n++){var r=i[n];this!==r?(r.selected(!1),r.options_.selected=!1):r.options_.selected=!0}},e}(MenuItem$6),MenuButton$2=videojs.getComponent("MenuButton"),Menu$3=videojs.getComponent("Menu"),QualitySwitcherMenuButton=function(t){function e(){return classCallCheck(this,e),possibleConstructorReturn(this,t.apply(this,arguments))}return inherits(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this);return this.labelEl_=videojs.dom.createEl("div",{className:"tcp-quality-switcher-value",innerHTML:""}),e.appendChild(this.labelEl_),e},e.prototype.createMenu=function(){for(var t=this.options_.qualityList,e=new QualitySwitcherMenu(this.player(),{name:this.options_.trackType+"QualitySwitcherMenu"}),i=void 0,n=t.length-1;n>-1;n--){var r=t[n];i=videojs.mergeOptions(r,{trackType:this.options_.trackType,callback:this.options_.callback,selectable:!0}),e.addItem(new QualitySwitcherMenuItem(this.player(),i)),r.selected&&this.updateLabel(r)}return e},e.prototype.updateLabel=function(t){this.labelEl_.innerHTML="<p>"+this.localize(t.label)+"</p>"},e}(MenuButton$2),Plugin$6=videojs.getPlugin("plugin"),TRACK_TYPES=["video","audio","subtitle"],TRACK_CLASS={video:"tcp-video-quality-switcher",audio:"tcp-audio-quality-switcher",subtitle:"tcp-subtitle-quality-switcher"},log$5=videojs.log,QualitySwitcher=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,i.on("qualitydataloaded",videojs.bind(r,r.init)),r}return inherits(e,t),e.prototype.init=function(t){log$5("QualitySwitcher initializing",t);var e=this.player,i=t.data.qualityData,n=t.data.callbacks;if(this.setOptions({qualityData:i,callbacks:n}),!e.controlBar)return void log$5("QualitySwitcher can not initialize without control bar");for(var r=0;r<TRACK_TYPES.length;r++){var o=TRACK_TYPES[r],s=o+"QualitySwitcherMenuButton",a=e.controlBar.getChild(s);i[o]&&i[o].length>0&&e.controlBar.options_.QualitySwitcherMenuButton&&(a&&a.el()&&(a.dispose(),e.controlBar.removeChild(a)),this.repleaceLabel(o,i),a=new QualitySwitcherMenuButton(e,{name:s,qualityList:i[o],callback:n[o],trackType:o}),a.addClass(TRACK_CLASS[o]),e.controlBar.addChild(a,{},10))}log$5("QualitySwitcher initialized",t,this)},e.prototype.setOptions=function(t){this.options=videojs.mergeOptions(this.options,t)},e.prototype.setVideoQuality=function(t){if(this.options.qualityData&&this.options.callbacks.video){for(var e=this.options.qualityData.video,i=0;i<e.length;i++)e[i].selected&&(e[i].selected=!1),e[i].id===t.id&&(e[i].selected=!0);this.options.callbacks.video(t)}},e.prototype.getVideoQualityData=function(){return this.options.qualityData?this.options.qualityData.video:undefined},e.prototype.repleaceLabel=function(t,e){if(this.options.qualityLabelList){var i=this.options.qualityLabelList[t],n=e[t];if(n&&n.length>0&&i&&i.length>0)for(var r=0;r<n.length;r++){var o=n[r].label;o=o.slice(0,o.length-1);for(var s=0;s<i.length;s++)if(i[s].height==o){n[r].label=i[s].resolutionName;break}}}},e.prototype.reset=function(){for(var t=this.player,e=0;e<TRACK_TYPES.length;e++){var i=TRACK_TYPES[e],n=t.controlBar&&t.controlBar.getChild(i+"QualitySwitcherMenuButton");n&&n.el()&&(n.dispose(),t.controlBar.removeChild(n))}},e}(Plugin$6);videojs.registerPlugin("QualitySwitcher",QualitySwitcher);var Plugin$7=videojs.getPlugin("plugin"),log$6=videojs.log,defaultOptions={labels:{FLU:"流畅",SD:"标清",HD:"高清",FHD:"超清"},showOrder:["FLU","SD","HD","FHD"],defaultRes:"SD"},MultiResolution=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,n.useManualOption&&(r.manualOptions=extend({},n)),r.hasInit=!1,i.on("multiresolutionchange",videojs.bind(r,function(t){t.data&&this.update(data)})),i.on(["resolutionswitching","resolutionswitched"],videojs.bind(r,function(t){"resolutionswitching"==t.type?this.player.addClass("tcp-res-switching"):this.player.removeClass("tcp-res-switching")})),i.ready(videojs.bind(r,function(){var t=i.tech(!0);t&&(t.on("masterplaylistchange",videojs.bind(this,this.onMasterPlaylistChange)),t.on("hlsresolutionswitching",videojs.bind(this,this.onHlsLevelChange)),t.on("hlsresolutionswitched",videojs.bind(this,this.onHlsLevelChange)),t.on("dashqualityswitching",videojs.bind(this,this.onDashQualityChange)),t.on("dashqualityswitched",videojs.bind(this,this.onDashQualityChange))),this.hasInit=!0})),r.init(r.player.options_.multiResolution),r}return inherits(e,t),e.prototype.init=function(t){var e=this.player,i=extend({},defaultOptions,t,this.manualOptions),n=i&&i.sources;if(this.options=i,n){log$6("MultiResolution initializing",i);var r=void 0;if(this.currentID=i.defaultRes=n[i.defaultRes]?i.defaultRes:Object.keys(n).shift(),r=n[this.currentID],"mediaLoader"!=e.options_.children[0]||this.hasInit)e.src(r);else{var o;(o=e.options_.sources).push.apply(o,r)}this.hasInit||(this.hasInit=!0),e.ready(videojs.bind(this,function(){e.trigger({type:"qualitydataloaded",data:this.initQualityData(i)}),e.one("loadedmetadata",function(){var t=this.tech(!0);"flash"!=t.name_&&t.el_.getAttribute("poster")&&(t.el_.removeAttribute("poster"),delete t.el_.poster)})})),log$6("MultiResolution initialized")}},e.prototype.reset=function(){this.player.QualitySwitcher().reset()},e.prototype.update=function(t){this.init(t)},e.prototype.store=function(t){if(!t)return this.options;this.options=videojs.mergeOptions(defaultOptions,this.options,t)},e.prototype.onMasterPlaylistChange=function(t){this.player.trigger({type:"qualitydataloaded",data:t.data})},e.prototype.onHlsLevelChange=function(t){"hlsresolutionswitching"==t.type?this.player.trigger({type:"resolutionswitching",data:t.data}):"hlsresolutionswitched"==t.type&&this.player.trigger({type:"resolutionswitched",data:t.data})},e.prototype.onDashQualityChange=function(t){"dashqualityswitching"==t.type?this.player.trigger({type:"resolutionswitching",data:t.data}):"dashqualityswitched"==t.type&&this.player.trigger({type:"resolutionswitched",data:t.data})},e.prototype.switchResolution=function(t){if(this.currentID!=t.id){var e=this.player,i=e.currentTime(),n=(e.paused(),Infinity===e.duration());if(e.trigger({type:"resolutionswitching",data:t}),n)e.bigPlayButton&&e.bigPlayButton.hide(),e.posterImage&&e.posterImage.hide();else{var r=e.ended(),o=e.playbackRate(),s=e.controlBar&&e.controlBar.progressControl&&e.controlBar.progressControl.seekBar.playProgressBar.el().style.width||0,a=videojs.browser.IS_IOS||videojs.browser.IS_ANDROID?"loadeddata":"loadedmetadata";e.one(a,function(){e.controlBar&&e.controlBar.progressControl&&(e.controlBar.progressControl.seekBar.playProgressBar.el().style.width=s),r||e.currentTime(i),"Flash"==e.techName_?(e.play(),e.tech(!0).trigger("seeked")):(e.play(),e.playbackRate()!=o&&e.playbackRate(o)),e.trigger({type:"resolutionswitched",data:t})}),e.bigPlayButton&&e.bigPlayButton.hide(),e.posterImage&&e.posterImage.hide(),"none"==e.options_.preload&&e.one("suspend",function(){e.load()})}e.src(this.options.sources[t.id]),e.load(),n&&setTimeout(function(){e.play(),setTimeout(function(){e.play()},900),(e.src().indexOf("webrtc://")>-1||e.src().indexOf(".sdp")>-1)&&e.tech(!0).webrtcProvider.recovery()},100),this.currentID=t.id}},e.prototype.initQualityData=function(t){var e=[],i=(t.sources,t.labels),n=t.showOrder;return n.length>0&&n.forEach(function(n,r){var o={};o.id=n,o.selected=t.defaultRes===n,o.label=i[n]||n,e.push(o)}),{qualityData:{video:e},callbacks:{video:videojs.bind(this,this.switchResolution)}}},e.prototype.sortSourceOrder=function(t){return[]},e}(Plugin$7);videojs.registerPlugin("MultiResolution",MultiResolution);var FlashObj=videojs.getComponent("Flash"),defaultDismiss=!videojs.browser.IS_IPHONE,registerPlugin=videojs.registerPlugin||videojs.plugin,defaults$1={header:"",code:"",message:"",timeout:45e3,dismiss:defaultDismiss,progressDisabled:!1,errors:{1:{type:"MEDIA_ERR_ABORTED"},2:{type:"MEDIA_ERR_NETWORK"},3:{type:"MEDIA_ERR_DECODE"},4:{type:"MEDIA_ERR_SRC_NOT_SUPPORTED"},5:{type:"MEDIA_ERR_ENCRYPTED"},unknown:{type:"MEDIA_ERR_UNKNOWN"},"-1":{type:"PLAYER_ERR_NO_SRC",message:"No video has been loaded."},"-2":{type:"PLAYER_ERR_TIMEOUT",message:"Could not download the video."},10:{type:"SERVER_ERR",message:"Request timed out."},11:{type:"SERVER_ERR",message:"Server is not respond."},12:{type:"DATA_ERR",message:"Server respond error data."},13:{type:"DATA_ERR",message:"No video transcoding information found."},14:{type:"HLS_NETWORK_ERR",message:"A network error caused the media download to fail part-way."},15:{type:"HLS_MEDIA_ERR",message:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support."},16:{type:"HLS_MUX_ERR",message:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support."},17:{type:"HLS_OTHER_ERR",message:"Rise an internal exception when playing HLS."},403:{type:"SERVER_ERR",message:"Authentication failed."},500:{type:"SERVER_ERR",message:"Server failed."},1001:{type:"SERVER_ERR",message:"The media file does not exist. Please check if the fileID is correct."},1002:{type:"SERVER_ERR",message:"The trial duration is illegal. The trial duration must be within the video duration."},1003:{type:"SERVER_ERR",message:"Param pcfg is not unique."},1004:{type:"SERVER_ERR",message:"The license has expired. Please check whether the expiration time setting is reasonable."},1005:{type:"SERVER_ERR",message:"Did not find an adaptive stream that can be played."},1006:{type:"SERVER_ERR",message:"Invalid request format, please check the request format."},1007:{type:"SERVER_ERR",message:"AppID is not exist, Please check if the AppID is correct."},1008:{type:"SERVER_ERR",message:"Without anti-leech information."},1009:{type:"SERVER_ERR",message:"psign check failed."},1010:{type:"SERVER_ERR",message:"Other errors."},2001:{type:"SERVER_ERR",message:"Internal error."},10008:{type:"SERVER_ERR",message:"The media file does not exist. Please check if the fileID is correct."},"-2001":{type:"SERVER_ERR",message:"Current browser not support play this stream, please select another one."},"-2002":{type:"SERVER_ERR",message:"Server respond error data.(eg. stream not exist)"},"-2003":{type:"SERVER_ERR",message:"Video play failed, please refresh to start play again."},"-2004":{type:"SERVER_ERR",message:"Connection to the server has failed and the number of connection retries has exceeded the set value."},"-2005":{type:"SERVER_ERR",message:"Video decoding failure."},PLAYER_ERR_DOMAIN_RESTRICTED:{message:"This video is restricted from playing on your current domain."},PLAYER_ERR_IP_RESTRICTED:{message:"This video is restricted at your current IP address."},PLAYER_ERR_GEO_RESTRICTED:{message:"This video is restricted from playing in your current geographic region."}}},initPlugin=function e(t,i){var n=void 0,r=void 0,o=void 0,s=[];videojs.getComponent("ErrorDisplay").prototype.options_.fillAlways=!1;var a=function(t){i.errors=videojs.mergeOptions(i.errors,t),Object.keys(i.errors).forEach(function(t){var e=i.errors[t];e.type||(e.type=t)})};a();var l=function(){t.clearTimeout(r),o&&(o=!1,t.removeClass("vjs-waiting")),r=t.setTimeout(function(){t.error()||t.paused()||t.ended()||(o=!0,t.addClass("vjs-waiting"))},1e3),t.clearTimeout(n),n=t.setTimeout(function(){t.error()||t.paused()||t.ended()||t.error({code:-2,type:"PLAYER_ERR_TIMEOUT"})},i.timeout),t.error()&&-2===t.error().code&&t.error(null)},u=function(){for(var e=void 0;s.length;)e=s.shift(),t.off(e[0],e[1]);t.clearTimeout(n),t.clearTimeout(r)},c=function(e,i){var n=function(){if(!t.error()){var e=t.$(".vjs-tech");if(e&&"application/x-shockwave-flash"===e.type&&!e.vjs_getProperty)return void t.error({code:-2,type:"PLAYER_ERR_TIMEOUT"});if(t.paused())return l();if(t.ended())return l()}i.call(this)};t.on(e,n),s.push([e,n])},h=function(){var e=0;u(),l(),c(["timeupdate","adtimeupdate"],function(){var i=t.currentTime();i!==e&&(e=i,l())}),i.progressDisabled||c("progress",l)},p=function(e){var n="",r=t.error(),o=document_1.createElement("div"),s="";if(r){r=videojs.mergeOptions(r,i.errors[r.code||0]),r.message&&(n='<div class="vjs-errors-message">'+t.localize(r.message)+"</div>"),r.code&&(n+='<div class="vjs-errors-code">'+this.localize("Error Code")+" : "+r.code+"</div>"),r.type&&(n+='<div class="vjs-errors-type">'+this.localize("Error Type")+" : "+r.type+"</div>"),"10"!=r.code&&"11"!=r.code||(n+='<a href="https://ping.huatuo.qq.com/playvideo.qcloud.com" target="_blank" class="vjs-error-check-network">诊断网络</a>'),o.className="vjs-errors-dialog",s='<div class="vjs-errors-content-container">\n        '+n+"\n      </div>";var a=t.getChild("errorDisplay");a.closeable(!("dismiss"in r)||r.dismiss)?(o.innerHTML=s,a.fillWith(o),a.contentEl().firstChild.appendChild(a.getChild("closeButton").el())):(o.innerHTML=s,a.fillWith(o)),a.one("modalclose",function(){return t.error(null)})}},d=function g(){u(),t.removeClass("vjs-errors"),t.off("dispose",g),t.off(["aderror","error"],p)},f=function(i){d(),e(t,videojs.mergeOptions(defaults$1,i))};f.extend=function(t){return a(t)},f.getAll=function(){return videojs.mergeOptions(i.errors)},f.disableProgress=function(t){i.progressDisabled=t,h()},t.on("dispose",d),t.on(["aderror","error"],p),t.ready(function(){t.addClass("vjs-errors")}),t.errors=f},errors=function(t){initPlugin(this,videojs.mergeOptions(defaults$1,t))};["extend","getAll","disableProgress"].forEach(function(t){errors[t]=function(){videojs.log.warn("The errors."+t+"() method is not available until the plugin has been initialized!")}}),registerPlugin("Errors",errors);var Plugin$8=videojs.getPlugin("plugin"),cgiSeq={},STATUS_CODE_REPORT_URL="//report.huatuo.qq.com/code.cgi",log$7=videojs.log,Reporter=function(t){function e(i,n){classCallCheck(this,e),log$7("Reporter initilaizing");var r=possibleConstructorReturn(this,t.call(this,i));if(!i.options_.plugins.Reporter){var o;return o=!1,possibleConstructorReturn(r,o)}return r.reportContent={status:"idle"},r.mtaReportCache=[],log$7("Reporter initilaized"),r}return inherits(e,t),e.prototype.setStorage=function(t,e){localStorage.setItem(t,e)},e.prototype.getStorage=function(t){localStorage.getItem(t)},e.prototype.sender=function(t){var e=new Image;e.onload=e.onerror=e.onabort=function(){e.onload=e.onerror=e.onabort=null,e=null},e.src=t},e.prototype.initPlayCgiCodeReport=function(){var t=this.player;t.on("playcgistart",videojs.bind(this,this.onPlayCgiStart)),t.on("playcgiend",videojs.bind(this,this.onPlayCgiEnd))},e.prototype.onPlayCgiStart=function(t){cgiSeq[t.data.time]=cgiSeq[t.data.time]||{},cgiSeq[t.data.time].startTime=t.data.time,cgiSeq[t.data.time].url=t.data.url},e.reportPlayMetrics=function(t,e){xhr.post("https://datacenter.live.qcloud.com",{body:JSON.stringify(t)},function(){e&&e()})},e.prototype.onPlayCgiEnd=function(t){if(cgiSeq[t.data.startTime]){var e=t.data,i=void 0,n=void 0,r=void 0;if(e.error)switch(n=2,e.error.message){case"Timeout":i=10;break;case"ServerError":i=11}else 0==e.result.code?(n=1,i=0):(n=3,i=e.result.code),r=e.result.requestId;this.reportPlayCgiToISD("","",n,i,t.data.time-cgiSeq[t.data.startTime].startTime,r,void 0,void 0)}},e.prototype.reportPlayCgiToISD=function(t,e,i,n,r,o,s,a){var l={domain:t||"playvideo.qcloud.com",cgi:e||"/getplayinfo/v2",type:i,code:n,time:r,appid:20370,platform:videojs.browser.IS_IOS?"ios":videojs.browser.IS_ANDROID?"android":"pc",expansion1:o,expansion2:s,expansion3:a};this.sender(unifyProtocol(STATUS_CODE_REPORT_URL)+"?"+serializeParams(l))},e.prototype.reportMTA=function(t,e){window.MtaH5?(this.mtaReportCache.length>0&&(this.mtaReportCache.forEach(function(t){t.call()}),this.mtaReportCache=0),MtaH5.clickStat(t,e)):this.mtaReportCache.push(function(){MtaH5.clickStat(t,e)})},e}(Plugin$8);videojs.registerPlugin("Reporter",Reporter);var store2=createCommonjsModule(function(t){!function(e,i){var n={version:"2.13.2",areas:{},apis:{},inherit:function(t,e){for(var i in t)e.hasOwnProperty(i)||Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(t,i));return e},stringify:function(t,e){return t===undefined||"function"==typeof t?t+"":JSON.stringify(t,e||n.replace)},parse:function(t,e){try{return JSON.parse(t,e||n.revive)}catch(i){return t}},fn:function(t,e){n.storeAPI[t]=e;for(var i in n.apis)n.apis[i][t]=e},get:function(t,e){return t.getItem(e)},set:function(t,e,i){t.setItem(e,i)},remove:function(t,e){t.removeItem(e)},key:function(t,e){return t.key(e)},length:function(t){return t.length},clear:function(t){t.clear()},Store:function(t,e,i){var r=n.inherit(n.storeAPI,function(t,e,i){return 0===arguments.length?r.getAll():"function"==typeof e?r.transact(t,e,i):e!==undefined?r.set(t,e,i):"string"==typeof t||"number"==typeof t?r.get(t):"function"==typeof t?r.each(t):t?r.setAll(t,e):r.clear()});r._id=t;try{e.setItem("__store2_test","ok"),r._area=e,e.removeItem("__store2_test")}catch(o){r._area=n.storage("fake")}return r._ns=i||"",n.areas[t]||(n.areas[t]=r._area),n.apis[r._ns+r._id]||(n.apis[r._ns+r._id]=r),r},storeAPI:{area:function(t,e){var i=this[t];return i&&i.area||(i=n.Store(t,e,this._ns),this[t]||(this[t]=i)),i},namespace:function(t,e){if(!t)return this._ns?this._ns.substring(0,this._ns.length-1):"";var i=t,r=this[i];if(!(r&&r.namespace||(r=n.Store(this._id,this._area,this._ns+i+"."),this[i]||(this[i]=r),e)))for(var o in n.areas)r.area(o,n.areas[o]);return r},isFake:function(t){return t?(this._real=this._area,this._area=n.storage("fake")):!1===t&&(this._area=this._real||this._area),"fake"===this._area.name},toString:function(){return"store"+(this._ns?"."+this.namespace():"")+"["+this._id+"]"},has:function(t){return this._area.has?this._area.has(this._in(t)):!!(this._in(t)in this._area)},size:function(){return this.keys().length},each:function(t,e){for(var i=0,r=n.length(this._area);i<r;i++){var o=this._out(n.key(this._area,i));if(o!==undefined&&!1===t.call(this,o,this.get(o),e))break;r>n.length(this._area)&&(r--,i--)}return e||this},keys:function(t){return this.each(function(t,e,i){i.push(t)},t||[])},get:function(t,e){var i,r=n.get(this._area,this._in(t));return"function"==typeof e&&(i=e,e=null),null!==r?n.parse(r,i):null!=e?e:r},getAll:function(t){return this.each(function(t,e,i){i[t]=e},t||{})},transact:function(t,e,i){var n=this.get(t,i),r=e(n);return this.set(t,r===undefined?n:r),this},set:function(t,e,i){var r,o=this.get(t);return null!=o&&!1===i?e:("boolean"!=typeof i&&(r=i),n.set(this._area,this._in(t),n.stringify(e,r))||o)},setAll:function(t,e){var i,n;for(var r in t)n=t[r],this.set(r,n,e)!==n&&(i=!0);return i},add:function(t,e,i){var r=this.get(t);if(r instanceof Array)e=r.concat(e);else if(null!==r){var o=typeof r;if(o===typeof e&&"object"===o){for(var s in e)r[s]=e[s];e=r}else e=r+e}return n.set(this._area,this._in(t),n.stringify(e,i)),e},remove:function(t,e){var i=this.get(t,e);return n.remove(this._area,this._in(t)),i},clear:function(){return this._ns?this.each(function(t){n.remove(this._area,this._in(t))},1):n.clear(this._area),this},clearAll:function(){var t=this._area;for(var e in n.areas)n.areas.hasOwnProperty(e)&&(this._area=n.areas[e],this.clear());return this._area=t,this},_in:function(t){return"string"!=typeof t&&(t=n.stringify(t)),this._ns?this._ns+t:t},_out:function(t){return this._ns?t&&0===t.indexOf(this._ns)?t.substring(this._ns.length):undefined:t}},storage:function(t){return n.inherit(n.storageAPI,{items:{},name:t})},storageAPI:{length:0,has:function(t){return this.items.hasOwnProperty(t)},key:function(t){var e=0;for(var i in this.items)if(this.has(i)&&t===e++)return i},setItem:function(t,e){this.has(t)||this.length++,this.items[t]=e},removeItem:function(t){this.has(t)&&(delete this.items[t],this.length--)},getItem:function(t){return this.has(t)?this.items[t]:null},clear:function(){for(var t in this.items)this.removeItem(t)}}},r=n.Store("local",function(){try{return localStorage}catch(t){}}());r.local=r,r._=n,r.area("session",function(){try{return sessionStorage}catch(t){}}()),r.area("page",n.storage("page")),"function"==typeof i&&i.amd!==undefined?i("store2",[],function(){return r}):t.exports?t.exports=r:(e.store&&(n.conflict=e.store),e.store=r)}(commonjsGlobal,commonjsGlobal&&commonjsGlobal.define)}),Plugin$9=videojs.getPlugin("plugin"),log$8=videojs.log,ContinuePlay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,r.init(),r}return inherits(e,t),e.prototype.init=function(){var t=this.player,e=t.options_,i=void 0;t.ready(videojs.bind(this,function(){(i=e&&e.appID&&e.fileID?e.fileID:this.options.playID)&&(log$8("ContinuePlay initializing"),this.playID=i,this.lastTime=store2.get(this.getStoreKey()),t.off("timeupdate",videojs.bind(this,this.onTimeUpdate)),t.on("timeupdate",videojs.bind(this,this.onTimeUpdate)),t.one(this.getTriggerEvent(),videojs.bind(this,this.onPlay)),!this.options.auto&&t.getChild("ContinuePlayTips")&&t.getChild("ContinuePlayTips").close(),log$8("ContinuePlay initialized"),t.trigger({type:"feature",data:"continue"}))}))},e.prototype.onTimeUpdate=function(t){store2.set(this.getStoreKey(),this.player.currentTime())},e.prototype.onPlay=function(t){var e=this.player;Math.round(this.lastTime)>1&&this.player.duration()>1&&Math.round(this.lastTime)!=Math.round(this.player.duration())?this.options.auto?this.start():e.addChild("ContinuePlayTips",videojs.mergeOptions(this.options,{time:this.lastTime,resumeCallback:videojs.bind(this,this.start)})):"Flash"==e.techName_&&Math.round(this.lastTime)>1&&Math.round(this.lastTime)!=Math.round(this.player.duration())&&!this.player.duration()&&e.one("loadedmetadata",videojs.bind(this,this.onPlay))},e.prototype.start=function(){var t=this.player;t.currentTime(this.lastTime),"Flash"==t.techName_&&t.tech(!0).trigger("seeked"),t.trigger({type:"continueplay",data:{lastTime:this.lastTime}})},e.prototype.generatePlayID=function(){},e.prototype.getStoreKey=function(){return"tcplayer-lpt-"+this.playID},e.prototype.getTriggerEvent=function(){var t=this.player,e=t.options_;return e.autoplay&&"Flash"==t.techName_?"loadedmetadata":"playing"},e}(Plugin$9);videojs.registerPlugin("ContinuePlay",ContinuePlay);var defaults$2={align:"top-left",className:"",content:"",debug:!1,showBackground:!0,attachToControlBar:!1,overlays:[]},Component$12=videojs.getComponent("Component"),dom$1=videojs.dom||videojs,registerPlugin$1=videojs.registerPlugin||videojs.plugin,isNumber=function(t){return"number"==typeof t&&t===t},hasNoWhitespace=function(t){return"string"==typeof t&&/^\S+$/.test(t)},Overlay=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return["start","end"].forEach(function(t){var e=r.options_[t];if(isNumber(e))r[t+"Event_"]="timeupdate";else if(hasNoWhitespace(e))r[t+"Event_"]=e;else if("start"===t)throw new Error('invalid "start" option; expected number or string')}),["endListener_","rewindListener_","startListener_"].forEach(function(t){r[t]=function(i){return e.prototype[t].call(r,i)}}),"timeupdate"===r.startEvent_&&r.on(i,"timeupdate",r.rewindListener_),r.debug('created, listening to "'+r.startEvent_+'" for "start" and "'+(r.endEvent_||"nothing")+'" for "end"'),r.hide(),r}return inherits(e,t),e.prototype.createEl=function(){var t=this.options_,e=t.content,i=t.showBackground?"tcp-overlay-background":"tcp-overlay-no-background",n=dom$1.createEl("div",{className:"\n        tcp-overlay\n        tcp-overlay-"+t.align+"\n        "+t.className+"\n        "+i+"\n        vjs-hidden\n      "});return"string"==typeof e?n.innerHTML=e:videojs.browser.IS_IE8||e instanceof window_1.DocumentFragment?n.appendChild(e):dom$1.appendContent(n,e),n},e.prototype.debug=function(){if(this.options_.debug){for(var t=videojs.log,e=t,i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];t.hasOwnProperty(n[0])&&"function"==typeof t[n[0]]&&(e=t[n.shift()]),e.apply(undefined,["overlay#"+this.id()+": "].concat(n))}},e.prototype.hide=function(){return t.prototype.hide.call(this),this.debug("hidden"),this.debug('bound `startListener_` to "'+this.startEvent_+'"'),this.endEvent_&&(this.debug('unbound `endListener_` from "'+this.endEvent_+'"'),this.off(this.player(),this.endEvent_,this.endListener_)),this.options_.once?(videojs.browser.IS_IOS&&videojs.browser.IS_WECHAT?this.player().hasStarted()||this.player().ready(videojs.bind(this,function(){this.startListener_({type:this.startEvent_})})):this.player().hasStarted()||this.on(this.player(),this.startEvent_,this.startListener_),this):("pause"===this.startEvent_?this.on(this.player(),this.startEvent_,this.startOnPause_):this.on(this.player(),this.startEvent_,this.startListener_),this)},e.prototype.startOnPause_=function(t){var e=this.player();"pause"===t.type?e.seeking()||e.ended()||this.startListener_({type:"pause"}):e.paused()&&this.startListener_({type:"pause"})},e.prototype.shouldHide_=function(t,e){var i=this.options_.end;return isNumber(i)?t>=i:i===e},e.prototype.show=function(){return t.prototype.show.call(this),"pause"===this.startEvent_&&this.off(this.player(),this.startEvent_,this.startOnPause_),this.off(this.player(),this.startEvent_,this.startListener_),this.debug("shown"),this.debug('unbound `startListener_` from "'+this.startEvent_+'"'),this.endEvent_&&(this.debug('bound `endListener_` to "'+this.endEvent_+'"'),this.on(this.player(),this.endEvent_,this.endListener_)),this},e.prototype.shouldShow_=function(t,e){var i=this.options_.start,n=this.options_.end;return isNumber(i)?isNumber(n)?t>=i&&t<n:this.hasShownSinceSeek_?Math.floor(t)===i:(this.hasShownSinceSeek_=!0,t>=i):i===e},e.prototype.startListener_=function(t){var e=this.player().currentTime();this.shouldShow_(e,t.type)&&this.show()},e.prototype.endListener_=function(t){var e=this.player().currentTime();this.shouldHide_(e,t.type)&&this.hide()},e.prototype.rewindListener_=function(t){var e=this.player().currentTime(),i=this.previousTime_,n=this.options_.start,r=this.options_.end;e<i&&(this.debug("rewind detected"),isNumber(r)&&!this.shouldShow_(e)?(this.debug("hiding; "+r+" is an integer and overlay should not show at this time"),this.hasShownSinceSeek_=!1,this.hide()):hasNoWhitespace(r)&&e<n&&(this.debug("hiding; show point ("+n+") is before now ("+e+") and end point ("+r+") is an event"),this.hasShownSinceSeek_=!1,this.hide())),this.previousTime_=e},e}(Component$12);videojs.registerComponent("Overlay",Overlay);var plugin=function(t){var e=this,i=videojs.mergeOptions(defaults$2,t);Array.isArray(this.overlays_)&&this.overlays_.forEach(function(t){e.removeChild(t),e.controlBar&&e.controlBar.removeChild(t),t.dispose()});var n=i.overlays;delete i.overlays,this.overlays_=n.map(function(t){var n=videojs.mergeOptions(i,t);return n.attachToControlBar&&e.controlBar&&-1!==n.align.indexOf("bottom")?e.controlBar.addChild("overlay",n):e.addChild("overlay",n)})};registerPlugin$1("Patch",plugin)
;var Plugin$10=videojs.getPlugin("plugin"),log$9=videojs.log,locations=["start","pause","ended"],startEvent=["loadstart","pause","ended"],endEvent=["play","play","play"],ImagePatch=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.hasInit=!1,r.init(n),r}return inherits(e,t),e.prototype.init=function(t){var e=this,i=this.player,n={showBackground:!1,overlays:[]};Array.isArray(t)&&t.length>0&&(log$9("ImagePatch initializing"),t.forEach(function(t){var i=void 0,r=void 0,o=void 0,s=void 0,a=document.createDocumentFragment();o=document.createElement("a"),o.className="tcp-overlay-link",o.target="_blank",t.link&&(o.href=t.link),t.url&&(s=e.loadImg(unifyProtocol(t.url),videojs.bind(e,e.onImgLoaded)),s.className="tcp-overlay-img",o.appendChild(s),videojs.browser.IS_IE8?(i=document.createElement("div"),i.className="tcp-overlay-table",r=document.createElement("div"),r.className="tcp-overlay-cell",r.appendChild(o),i.appendChild(r),a.appendChild(i)):a.appendChild(o),n.overlays.push({content:a,className:"tcp-image-patch tcp-image-patch-"+locations[t.location],start:startEvent[t.location],end:endEvent[t.location],once:0==t.location,align:"center"}))}),n.overlays.length>0?(i.Patch(n),i.trigger({type:"feature",data:"patch"}),this.hasInit=!0):this.reset(),log$9("ImagePatch initialized"))},e.prototype.loadImg=function(t,e){var i,n,r,o,s,a=new Image;return a.src=t,a.complete?(e&&e.call(e,a),a):(n=a.width,r=a.height,a.onerror=function(){i.end=!0,a=a.onload=a.onerror=null},i=function(){o=a.width,s=a.height,(o!==n||s!==r||o*s>1024)&&(e&&e.call(e,a),i.end=!0)},i(),a.onload=function(){!i.end&&i(),a=a.onload=a.onerror=null},a)},e.prototype.onImgLoaded=function(t){if(videojs.browser.IE_VERSION){var e=parseInt(this.player.el().currentStyle.width),i=parseInt(this.player.el().currentStyle.height);if(t.width>e||t.height>i){var n=e/(t.width/t.height);t.width/t.height>e/i||(t.style.marginTop=(i-n)/2+"px"),t.style.width="100%",t.style.height="auto"}}},e.prototype.reset=function(){this.hasInit&&(log$9("ImagePatch reset"),this.player.Patch(),this.hasInit=!1)},e}(Plugin$10);videojs.registerPlugin("ImagePatch",ImagePatch);var Plugin$11=videojs.getPlugin("plugin"),log$10=videojs.log,ContextMenu=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,log$10("ContextMenu initializing"),r.init(),log$10("ContextMenu initialized"),r}return inherits(e,t),e.prototype.init=function(){this.player.addChild("RightClickPopupMenu",this.options)},e}(Plugin$11);videojs.registerPlugin("ContextMenu",ContextMenu);var Plugin$12=videojs.getPlugin("plugin"),LevelSwitch=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,r.init(),r}return inherits(e,t),e.prototype.init=function(){var t=this.player,e=t.options_.plugins.ContextMenu;e&&e.levelSwitch&&e.levelSwitch.open&&t.addChild("LevelSwitchTips",this.options)},e}(Plugin$12);videojs.registerPlugin("LevelSwitch",LevelSwitch);var Plugin$13=videojs.getPlugin("plugin"),log$12=videojs.log,defaults$3={width:0,height:0,basePath:""},VttThumbnail=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.hasInit=!1,n.vttUrl&&i.ready(videojs.bind(r,function(){this.init(n)})),r}return inherits(e,t),e.prototype.loadTrackFile=function(t){var e=this,i=this.player,n=i.addRemoteTextTrack({src:t.vttUrl,kind:"metadata"},!0);n&&n.addEventListener("load",function(){e.trackInfo=n})},e.prototype.init=function(t){if(!videojs.browser.IS_IOS&&!videojs.browser.IS_ANDROID&&this.player.controlBar&&this.player.controlBar.progressControl){if(log$12("VttThumbnail initializing",t),!this.player.controlBar)return void log$12("VttThumbnail can not initialize without control bar");this.options=t;var e=this.player,i=e.controlBar.progressControl;this.loadTrackFile(t),defaults$3.basePath=parseBasePath(t.vttUrl),this.initUI(),i.on("mouseenter",videojs.bind(this,this.onMouseEnter)),i.on("mousemove",videojs.bind(this,this.onMouseMove)),i.on("mouseleave",videojs.bind(this,this.onMouseOut)),this.hasInit=!0,log$12("VttThumbnail initialized"),e.trigger({type:"feature",data:"thumbnail"})}},e.prototype.initTrackFile=function(t){t.track;this.trackInfo=t},e.prototype.initUI=function(){var t=videojs.dom.createEl("div",{className:"tcp-vtt-thumbnail-container"}),e=videojs.dom.createEl("img",{className:"tcp-vtt-thumbnail-img"});t.appendChild(e),this.containerEl=t,this.imgEl=e,this.player.controlBar.progressControl.el().appendChild(t)},e.prototype.onMouseEnter=function(){this.containerEl.style.display="block"},e.prototype.onMouseMove=function(t){if(!this.trackInfo)return!1;var e=void 0,i=void 0,n=void 0,r=void 0,o=void 0,s=void 0,a=void 0,l=void 0,u=0,c=void 0,h=void 0,p=void 0,d=void 0;for(n=this.player.controlBar.progressControl,r=this.player.controlBar.progressControl.seekBar,o=offsetParent(n.el()).getBoundingClientRect(),s=offsetParent(r.el()).getBoundingClientRect(),e=t.clientX,l=this.player.duration(),a=((e-s.left)/r.width()*l).toFixed(3),c=this.trackInfo.track||this.trackInfo;u<c.cues.length;){if(h=c.cues[u],h.startTime<=a&&h.endTime>=a){p=parseImageLink(h.text);break}u++}if(!p)return this.onMouseOut(),!1;d=e-s.left+p.w/2-r.width(),d=Math.max(0,d),i=e-o.left-p.w/2-d,i=Math.max(r.el().offsetLeft,i),p.left=i,this.setView(p)},e.prototype.onMouseOut=function(){this.containerEl.style.display="none"},e.prototype.setView=function(t){var e=this.containerEl,i=this.imgEl,n=this.options;e.style.width==t.w&&e.style.height==t.h||(e.style.width=t.w+"px",e.style.height=t.h+"px"),e.style.left=t.left+"px",n.imgUrl&&i.src!=n.imgUrl?i.src=n.imgUrl:i.src=t.src,i.style.left=-t.x+"px",i.style.top=-t.y+"px",i.style.clip="rect("+t.y+"px,"+(t.w+t.x)+"px,"+(t.y+t.h)+"px,"+t.x+"px)"},e.prototype.reset=function(){if(this.hasInit){log$12("VttThumbnail reset");var t=this.player,e=t.controlBar.progressControl;e.off("mousemove",videojs.bind(this,this.onMouseMove)),e.off("mouseleave",videojs.bind(this,this.onMouseOut)),this.hasInit=!1}},e}(Plugin$13),getComputedStyle=function(t,e){return function(i){return window.getComputedStyle?window.getComputedStyle(t,e)[i]:t.currentStyle[i]}},offsetParent=function n(t){return"HTML"!==t.nodeName&&"static"===getComputedStyle(t)("position")?n(t.offsetParent):t},parseImageLink=function(t){var e=void 0,i=void 0,n=void 0;if(-1===(i=t.indexOf("#")))return{src:t,w:0,h:0,x:0,y:0};if(e=t.substring(0,i),/^(http:|https:|\/\/)/.test(e)||(e=defaults$3.basePath+e),n=t.substring(i+1),"xywh="!==n.substring(0,5))return{src:e,w:0,h:0,x:0,y:0};var r=n.substring(5).split(",");return{src:e,w:parseInt(r[2]),h:parseInt(r[3]),x:parseInt(r[0]),y:parseInt(r[1])}},parseBasePath=function(t){/^(\/\/)/.test(t)&&(t="https:"+t);var e=/(\w+):\/\/([^\:|\/]+)(\:\d*)?(.*\/)([^#|\?|\n]+)?(#.*)?(\?.*)?/i,i=t.match(e);if(i&&i[2]&&i[4])return"//"+i[2]+i[4]};videojs.registerPlugin("VttThumbnail",VttThumbnail);var Plugin$14=videojs.getPlugin("plugin"),log$13=videojs.log,PlayerMetrics=function(t){function e(i,n){classCallCheck(this,e),log$13("PlayerMetrics initializing");var r=possibleConstructorReturn(this,t.call(this,i));if(!1===i.options_.reportable){var o;return o=!1,possibleConstructorReturn(r,o)}return r.player=i,r.checkPrevData(),r.initData(),r.registerListener(),log$13("PlayerMetrics initialized"),r}return inherits(e,t),e.prototype.checkPrevData=function(){var t=localStorage.getItem("tcplayer_data");if(t){var e=JSON.parse(t);Reporter.reportPlayMetrics(e,function(){localStorage.removeItem("tcplayer_data")})}},e.prototype.initData=function(){this.bytes_token=guid(),this.vodBaseData={uint32_service:2,uint32_platform:3,bytes_version:String(version),uint64_appid:0,bytes_bizid:this.guid("dev_uuid"),bytes_stream_id:"",uint32_module_id:1011,uint32_data_type:1,uint32_command:40303,uint64_data_time:0,bytes_token:this.bytes_token,token:this.bytes_token,dev_uuid:this.guid("dev_uuid"),str_app_name:"",str_app_version:"",str_stream_url:"",str_brand_type:this.getDevice(),str_device_resolution:screen.width+"_"+screen.height,str_device_type:"",str_package_name:"",str_sdk_name:"TCPlayer",str_user_id:this.guid("dev_uuid"),sys_type:this.getSystem(),sys_version:String(this.getSystemVer()),str_user_agent:USER_AGENT,str_browser_version:String(this.getBrowserVer()),str_browser_model:String(this.getBrowser()),u32_app_id:String(this.player.options_.appID),str_fileid:String(this.player.options_.fileID),u32_network_type:this.getNetworkType()},this.liveBaseData={str_user_id:this.guid("dev_uuid"),dev_uuid:this.guid("dev_uuid"),str_session_id:this.bytes_token,str_device_type:"",str_os_info:this.getSystem(),str_package_name:"",u32_network_type:"0xFF"!==this.getNetworkType()?this.getNetworkType():"",u32_server_ip:"",str_stream_url:this.player.cache_.src,u64_timestamp:this.player.startTime,u32_link_type:1,u32_channel_type:1,str_app_version:"",platform:3,uint32_platform:3,str_browser_version:this.getBrowserVer(),str_browser_model:this.getBrowser(),str_user_agent:USER_AGENT,u32_video_drop:"",u32_drop_usage:"",float64_rtt:""},this.features=[],this.afterCanplay=!1,this.videoDuration=0,this.consumeDuration=0,this.playScene=2,this.timing={},this.playStatus={isFirstPlay:!1,pauseDuration:0},this.contentComputTimePoint=[],this.firstFrameDuration=0,this.firstIFrameDuration=0,this.blockInterval=5e3,this.bufferBlock=[],this.u32_drm_type="plain",this.lagStatus={lagCount:0,lagging:!1,lagStart:0,lagEnd:0,lagInterval:500,lagDuration:0,lagAvg:[],isFirstLag:!1,skip:!1},this.resetBlockData()},e.prototype.getComsumeDuration=function(){var t=getTimeStamp()-this.timing.firstPlaying;this.consumeDuration=t-this.lagStatus.lagDuration-this.playStatus.pauseDuration},e.prototype.registerListener=function(){var t=this.player;t.on("loadedmetadata",videojs.bind(this,this.onLoadedmetadata)),t.one("canplay",videojs.bind(this,this.oneCanplay)),t.one("play",videojs.bind(this,this.onePlay)),t.on("play",videojs.bind(this,this.onPlay)),t.on("playing",videojs.bind(this,this.onPlaying)),t.on("seeking",videojs.bind(this,this.onSeeking)),t.on("seeked",videojs.bind(this,this.onSeeked)),t.on("error",videojs.bind(this,this.onError)),t.on("resolutionswitching",videojs.bind(this,this.onResolutionSwitching)),t.on("pause",videojs.bind(this,this.onPause)),t.on("playcgiend",videojs.bind(this,this.onPlaycgiend)),t.on("ended",videojs.bind(this,this.onEnded)),t.on("feature",videojs.bind(this,this.onFeature)),t.on("dispose",videojs.bind(this,this.onDispose)),document.addEventListener("visibilitychange",videojs.bind(this,this.onVisibilityChange)),window.addEventListener("pagehide",videojs.bind(this,this.onPageHide))},e.prototype.onPageHide=function(){this.saveData()},e.prototype.onVisibilityChange=function(){"hidden"===document.visibilityState&&this.saveData()},e.prototype.saveData=function(){this.bytes_token&&3!==this.playScene&&(this.getComsumeDuration(),this.report(2===this.playScene?40304:40102,{},function(t){localStorage.setItem("tcplayer_data",JSON.stringify(t))}))},e.prototype.onLoadedmetadata=function(){this.player.tech_.webrtcProvider?this.playScene=3:this.playScene=this.player.duration()===Infinity?1:2,this.videoDuration=parseInt(this.player.duration())||0,window.performance&&(performance.mark("firstFrameEnd"),performance.measure("firstFrame","firstFrameStart","firstFrameEnd"),this.firstFrameDuration=performance.getEntriesByName("firstFrame")[0].duration),2===this.playScene&&this.report(40303)},e.prototype.oneCanplay=function(){window.performance&&(performance.mark("firstIFrameEnd"),performance.measure("firstIFrame","firstFrameStart","firstIFrameEnd"),this.firstIFrameDuration=performance.getEntriesByName("firstIFrame")[0].duration,1===this.playScene&&this.report(40101))},e.prototype.onePlay=function(){var t=this;this.setTimingData({firstPlay:getTimeStamp()});var e=this.player.el_.firstChild;this.bufferStart=e&&e.buffered&&e.buffered.length>0?e.buffered.start(e.buffered.length-1):0,this.bufferBlock=[this.bufferStart];var i=this.player.getVideoPlaybackQuality();this.recvFrames=i.totalVideoFrames,this.renderedFrames=i.totalVideoFrames-i.droppedVideoFrames,this.blockStartTime=getTimeStamp(),this.blockTimer=setInterval(function(){if(2===t.playScene)return!1;if(t.lagStatus.lagging&&t.lagStatus.lagStart){var i=getTimeStamp()-t.lagStatus.lagStart;i>t.blockInterval?t.blockData.lagBlockDuration=t.blockInterval:t.blockData.lagBlockDuration+=i}t.blockStartTime=getTimeStamp();var n=e&&e.buffered&&e.buffered.length>0?e.buffered.end(e.buffered.length-1):0,r=t.player.getVideoPlaybackQuality(),o=r.totalVideoFrames,s=r.totalVideoFrames-r.droppedVideoFrames;Object.assign(t.blockData,{buffer_avg:parseInt(1e3*(n-t.bufferStart)),recv_frames:o-t.recvFrames,render_frames:s-t.renderedFrames}),t.bufferBlock.push(t.blockData.buffer_avg),t.bufferStart=n,t.recvFrames=o,t.renderedFrames=s,t.report(40100)},this.blockInterval)},e.prototype.onDispose=function(){clearInterval(this.blockTimer)},e.prototype.onPlay=function(){this.playStatus.pauseTime&&(this.playStatus.pauseDuration=this.playStatus.pauseDuration+getTimeStamp()-this.playStatus.pauseTime,this.playStatus.pauseTime=0)},e.prototype.onPlaying=function(){var t=this;if(!this.playStatus.isFirstPlay&&(this.playStatus.isFirstPlay=!0,this.setTimingData({firstPlaying:getTimeStamp()}),this.player.on("waiting",function(){t.lagStatus.lagging||t.player.seeking()||t.lagStatus.skip||(t.lagStatus.lagging=!0,t.lagStatus.lagStart=getTimeStamp())}),this.player.currentSource().keySystems)){var e=this.player.currentSource().keySystems;e.plain&&(this.u32_drm_type="plain"),e.SimpleAES&&(this.u32_drm_type="SimpleAES")}this.lagStatus.lagging&&(this.lagStatus.lagEnd=getTimeStamp())-this.lagStatus.lagStart>this.lagStatus.lagInterval&&(this.lagStatus.lagging=!1,this.lagStatus.lagCount++,this.lagStatus.lagAvg.push(this.lagStatus.lagEnd-this.lagStatus.lagStart),this.lagStatus.lagDuration+=this.lagStatus.lagEnd-this.lagStatus.lagStart,this.lagStatus.lagStart<this.blockStartTime&&(this.blockData.lagBlockDuration=this.lagStatus.lagEnd-this.blockStartTime),this.lagStatus.lagStart>=this.blockStartTime&&(this.blockData.lagBlockDuration+=this.lagStatus.lagEnd-this.lagStatus.lagStart),this.lagStatus.lagEnd=this.lagStatus.lagStart=0,this.lagStatus.isFirstLag||(this.lagStatus.isFirstLag=!0,this.setTimingData({firstLag:getTimeStamp()}))),[60,200,500,1e3].forEach(function(e){t.lagStatus.lagging&&(t.lagStatus.lagEnd=getTimeStamp())-t.lagStatus.lagStart>e&&t.blockData["lagCount_"+e]++}),this.lagStatus.skip=!1},e.prototype.onSeeking=function(){this.contentComputTimePoint.push({type:"end",time:this.player.currentTime()})},e.prototype.onSeeked=function(){this.contentComputTimePoint.push({type:"start",time:this.player.currentTime()})},e.prototype.onError=function(t){this.getComsumeDuration();var e={u64_err_code:t.data.code,str_err_info:t.data.message||(defaults$1.errors[t.data.code]||{}).message},i=(this.player.cache_.src||"").indexOf("vod")>-1||this.player.options_.fileID;t&&t.data&&t.data.code&&(2===this.playScene&&i?this.report(40304,e):this.afterCanplay?this.report(40102,e):this.report(40101,e))},e.prototype.onResolutionSwitching=function(){this.lagStatus.skip=!0},e.prototype.onPause=function(){this.playStatus.pauseTime=getTimeStamp(),this.lagStatus.lagging&&(this.lagStatus.skip=!0)},e.prototype.onPlaycgiend=function(t){var e=this,i=t.data;i.result&&0==i.result.code&&(this.playStatus.requestID=i.result.requestId,setTimeout(function(){if(e.player.currentSources().length>0&&e.player.currentSources()[0].keySystems){var t=e.player.currentSources()[0].keySystems;t["com.widevine.alpha"]&&e.reportMTA("drm",{initialized:"Widevine"}),t["com.apple.fps.1_0"]&&e.reportMTA("drm",{initialized:"FairPlay"})}else e.player.options_.plugins.DRM&&e.player.options_.plugins.DRM.token&&e.reportMTA("drm",{initialized:"SimpleAES"})},0)),this.setTimingData({cgiStart:t.data.startTime,cgiEnd:t.data.time})},e.prototype.onEnded=function(){this.getComsumeDuration(),1===this.playScene?this.report(40102):this.report(40304)},e.prototype.onFeature=function(t){-1==this.features.indexOf(t.data)&&this.features.push(t.data)},e.prototype.guid=function(t){var e=localStorage.getItem(t);return e||(e=guid(),localStorage.setItem(t,e)),e},e.prototype.report=function(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},i=arguments[2];if(3===this.playScene)return!1;t&&this["report_"+t](e,i)},e.prototype.report_40101=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},e={uint64_data_time:getTimeStampBySecond(),str_stream_url:this.player.cache_.src,u64_timestamp:this.player.startTime,u32_dns_time:"",u32_connect_server_time:parseInt(this.firstFrameDuration),u32_video_decode_type:0,u32_first_frame_down:parseInt(this.firstFrameDuration),u32_first_video_decode_time:parseInt(this.firstIFrameDuration),u32_first_i_frame:parseInt(this.firstIFrameDuration),u32_first_audio_frame_down:parseInt(this.firstFrameDuration),u32_first_audio_render_time:parseInt(this.firstIFrameDuration),u64_err_code:t.u64_err_code||"",str_err_info:t.str_err_info||""},i={app_id:0,data:[Object.assign({},this.liveBaseData,e,t)],module_id:1005,command:40101};Reporter.reportPlayMetrics(i)},e.prototype.report_40100=function(t){var e=this,i=this.player||{},n={u32_cpu_usage:"",u32_app_cpu_usage:"",u32_avg_memory:"",u32_avg_cpu_usage:"",str_stream_url:i.cache_&&i.cache_.src,uint64_data_time:getTimeStampBySecond(),u32_recv_av_diff_time:0,u32_play_av_diff_time:0,u64_playtime:getTimeStamp()-this.timing.firstPlaying,u32_audio_decode_type:2,u32_audio_block_count:this.blockData.lagCount_500,u32_audio_cache_time:this.blockData.buffer_avg,u32_audio_drop:"",u32_video_decode_type:0,u32_video_recv_fps:this.blockData.recv_frames,u32_fps:this.blockData.render_frames,u32_video_cache_time:this.blockData.buffer_avg,u32_avg_cache_count:0,u32_video_block_count:this.blockData.lagCount_1000,u32_avg_net_speed:"",u32_video_light_block_count:this.blockData.lagCount_200,u32_video_large_block_count:this.blockData.lagCount_500,u32_audio_jitter_60ms_count:this.blockData.lagCount_60,u32_video_decode_fail:"",u32_audio_decode_fail:"",u32_avg_video_bitrate:0,u32_avg_audio_bitrate:0,u32_block_usage:1e3*parseInt(this.blockData.lagBlockDuration/this.blockInterval)},r={app_id:0,data:[Object.assign({},this.liveBaseData,n,t)],module_id:1005,command:40100};Reporter.reportPlayMetrics(r,function(){e.resetBlockData()})},e.prototype.report_40102=function(){var t=this,e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},i=arguments[1],n=this.player||{},r=this.lagStatus.lagAvg.length>0?this.lagStatus.lagAvg.reduce(function(t,e){return t+e},0)/this.lagStatus.lagAvg.length:0,o=this.bufferBlock.length>0?this.bufferBlock.reduce(function(t,e){return t+e},0)/this.bufferBlock.length:0,s=n.getVideoPlaybackQuality&&n.getVideoPlaybackQuality()||{},a=s.totalVideoFrames/e.duration,l={uint64_data_time:getTimeStampBySecond(),str_stream_url:n.cache_&&n.cache_.src,u64_timestamp:getTimeStamp(),u32_avg_cpu_usage:"",u32_avg_memory:"",u64_begin_timestamp:n.startTime,u32_result:this.consumeDuration,u64_err_code:e.u64_err_code,u32_speed_cnt:0,u32_avg_cache_time:o,u32_max_load:Math.max.apply(Math,this.lagStatus.lagAvg),u32_audio_block_time:this.lagStatus.lagDuration,u32_avg_load:r,u32_load_cnt:this.lagStatus.lagAvg.length||0,u32_nodata_cnt:this.lagStatus.lagAvg.length||0,u32_first_i_frame:this.firstIFrameDuration,u32_video_width:n.width_,u32_video_height:n.height_,u32_video_avg_fps:a,u32_avg_block_time:r,u64_block_count:this.lagStatus.lagAvg.length||0,u32_video_block_time:this.lagStatus.lagDuration,u64_jitter_cache_max:0,u64_block_duration_max:Math.max.apply(Math,this.lagStatus.lagAvg),u64_jitter_cache_avg:0,u32_ip_count_quic:"",u32_connect_count_quic:"",u32_connect_count_tcp:"",u32_is_real_time:"",u32_first_frame_black:""},u={app_id:0,data:[Object.assign({},this.liveBaseData,l)],module_id:1005,command:40102};if(i)return i(u);Reporter.reportPlayMetrics(u,function(){t.bytes_token=null,localStorage.removeItem("tcplayer_data")})},e.prototype.report_40303=function(){this.setTimingData({initEnd:getTimeStamp()});var t={msg_client_ip:{uint32_ip:""},uint32_service:this.playScene,uint64_data_time:getTimeStampBySecond(),str_stream_url:this.player.cache_.src,uint32_command:40303},e={app_id:0,data:[Object.assign({},this.vodBaseData,t)],module_id:1011,command:40303};Reporter.reportPlayMetrics(e)},e.prototype.report_40304=function(){var t=this,e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},i=arguments[1],n=this.lagStatus.lagAvg.length>0?this.lagStatus.lagAvg.reduce(function(t,e){return t+e},0)/this.lagStatus.lagAvg.length:0,r={uint64_data_time:getTimeStampBySecond(),uint32_command:40304,u32_timeuse:String(parseInt(this.consumeDuration/1e3)||0),u32_videotime:String(this.videoDuration),u32_avg_load:String(n),u32_load_cnt:String(this.lagStatus.lagCount),u32_max_load:this.lagStatus.lagAvg.length>0?String(Math.max.apply(Math,this.lagStatus.lagAvg)):String(0),u32_avg_block_time:String(this.lagStatus.lagDuration),u32_player_type:String(4),u32_dns_time:"",u32_tcp_did_connect:"",u32_first_video_packet:String(parseInt(this.firstFrameDuration)),u32_first_i_frame:String(parseInt(this.firstIFrameDuration)),u32_server_ip:"",u32_drm_type:this.u32_drm_type,u32_playmode:String(1),u64_err_code:String(e.u64_err_code||""),str_err_info:String(e.str_err_info||""),u32_video_decode_type:String(0),u32_speed:this.player&&String(100*this.player.playbackRate())||"1000"},o={app_id:0,data:[Object.assign({},this.vodBaseData,r)],module_id:1011,command:40304};if(i)return i(o);Reporter.reportPlayMetrics(o,function(){t.bytes_token=null,localStorage.removeItem("tcplayer_data")})},e.prototype.setTimingData=function(t){this.timing=videojs.mergeOptions(this.timing,t)},e.prototype.reset=function(){this.playStatus={isFirstPlay:!1}},e.prototype.resetBlockData=function(){this.blockData={lagCount_60:0,lagCount_200:0,lagCount_500:0,lagCount_1000:0,lagBlockDuration:0,buffer_avg:0,recv_frames:0,render_frames:0}},e.prototype.reportMTA=function(t,e){},e.prototype.getSystem=function(){return IS_IOS?"ios":IS_ANDROID?"android":IS_WIN?"win":IS_MAC?"mac":"other"},e.prototype.getSystemVer=function(){var t=this.getSystem();return"ios"==t?IOS_VERSION:"android"==t?ANDROID_VERSION:"win"==t?WIN_VER:"mac"==t?MAC_VER:void 0},e.prototype.getBrowser=function(){return IS_FIREFOX?"firefox":IS_EDGE?"edge":IS_MQQB?"mqq":IS_QQB?"qq":IS_TBS?"tbs":IS_CHROME?"chrome":IE_VERSION?"ie":IS_SAFARI?"safari":"other"},e.prototype.getBrowserVer=function(){var t=this.getBrowser();return"firefox"==t?FIREFOX_VER:"edge"==t?EDGE_VER:"chrome"==t?CHROME_VER:"ie"==t?IE_VERSION:"safari"==t?SAFARI_VER:"qq"==t||"mqq"==t?QQ_VER:"tbs"==t?TBS_VERSION:"other"},e.prototype.getDevice=function(){return IS_IOS?"IPHONE":IS_HUAWEI?"HUAWEI":IS_XIAOMI?"XIAOMI":IS_OPPO?"OPPO":IS_VIVO?"VIVO":IS_SX?"SUMSUNG":IS_CP?"COOLPAD":IS_ONE?"ONEPLUS":IS_ZX?"ZTE":"OTHER"},e.prototype.getMediaType=function(){return this.player.src()&&/\/(.+)\.(.+)\?|\/(.+)\.(.+)/.test(this.player.src()),RegExp.$2||RegExp.$4},e.prototype.getNetworkType=function(){var t=navigator.userAgent,e=t.match(/NetType\/\w+/)?t.match(/NetType\/\w+/)[0]:"NetType/other";e=e.toLowerCase().replace("nettype/","");var i;switch(e){case"wifi":i="1";break;case"4g":i="2";break;case"3g":case"3gnet":i="3";break;case"2g":i="4";break;default:i="0xFF"}return i},e}(Plugin$14);videojs.registerPlugin("PlayerMetrics",PlayerMetrics);var Plugin$15=videojs.getPlugin("plugin"),log$14=videojs.log,ProgressMarker=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return IS_IOS||IS_ANDROID||i.ready(videojs.bind(r,r.init)),r}return inherits(e,t),e.prototype.init=function(){var t=this.player;if(!t.controlBar||!this.player.controlBar.progressControl)return void log$14("ProgressMarker can not initialize without control bar or progress control");log$14("ProgressMarker initializing");var e=t.controlBar.getChild("ProgressControl").getChild("SeekBar");e.getChild("ProgressMarkerGenerator")&&e.removeChild("ProgressMarkerGenerator"),t.options_.dots&&t.options_.plugins.ProgressMarker&&(e.addChild("ProgressMarkerGenerator"),log$14("ProgressMarker initialized"),t.trigger({type:"feature",data:"marker"}))},e}(Plugin$15);videojs.registerPlugin("ProgressMarker",ProgressMarker);var Component$15=videojs.getComponent("Component"),PlayListItem=function(t){function e(i,n){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.createEl=function(){var t=this.options_,e=videojs.dom.createEl("div",{className:"tcp-playlist-item"}),i=videojs.dom.createEl("div",{className:"tcp-playlist-item-video",innerHTML:t.img?'<img src="'+t.img+'" class="tcp-playlist-item-img">':'<img src="https://imgcache.qq.com/open_proj/proj_qcloud_v2/mc_2014/video/console/v2/css/img/vod/default-cover.png" height="100" class="tcp-playlist-item-img">'}),n=videojs.dom.createEl("div",{className:"tcp-playlist-item-duration",textContent:this.timeFormat(t.duration)});i.appendChild(n);var r=videojs.dom.createEl("div",{className:"tcp-playlist-item-desc",textContent:t.text});return e.appendChild(i),e.appendChild(r),this.bindEvent(e),e},e.prototype.bindEvent=function(t){var e=this;videojs.on(t,["click"],function(){e.player_.trigger({type:"playItem",data:e.options_})})},e.prototype.timeFormat=function(t){var e="",i=Math.floor(t/3600);0!=i&&(t-=3600*i,i<10&&(i="0"+i),e+=i+":");var n=Math.floor(t/60);return n<10&&(n="0"+n),t-=60*n,t<10&&(t="0"+t),e+=n+":"+t},e}(Component$15);videojs.registerComponent("PlayListItem",PlayListItem);var Component$16=videojs.getComponent("Component"),PlayListTitle=function(t){function e(i,n){return classCallCheck(this,e),possibleConstructorReturn(this,t.call(this,i,n))}return inherits(e,t),e.prototype.createEl=function(){var t=this.options_;return videojs.dom.createEl("div",{className:"tcp-playlist-title",textContent:t.title})},e}(Component$16);videojs.registerComponent("PlayListTitle",PlayListTitle);var Component$14=videojs.getComponent("Component"),PlayListBox=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.init(),r.activeIndex=-1,r}return inherits(e,t),e.prototype.createEl=function(){return videojs.dom.createEl("div",{className:"tcp-playlist-box"})},e.prototype.init=function(){var t=this,e=this.options_.data;this.addChild("PlayListTitle",{title:this.options_.title}),e.forEach(function(e,i){e.index=i,t.addChild("PlayListItem",e)}),this.player_.on("ready",function(){t.options_.loop&&(t.activeIndex=0,t.player_.on("ended",videojs.bind(t,function(){t.player_.trigger({type:"playItem"})})))}),this.player_.on("playItem",function(i){var n=i.data,r=void 0;t.activeIndex=n?n.index:(t.activeIndex+1)%e.length,r=e[t.activeIndex],t.player_.loadVideoByID({fileID:r.fileID,appID:r.appID,psign:r.psign}),t.player_.one("loadedmetadata",videojs.bind(t,function(){t.player_.play()})),document.querySelectorAll(".tcp-playlist-item").forEach(function(t){videojs.dom.removeClass(t,"tcp-playlist-item-active")});var o=document.querySelectorAll(".tcp-playlist-item")[t.activeIndex];videojs.dom.addClass(o,"tcp-playlist-item-active")})},e}(Component$14);videojs.registerComponent("PlayListBox",PlayListBox);var Component$17=videojs.getComponent("Component"),PlayListToggle=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.status=0,r}return inherits(e,t),e.prototype.createEl=function(){var t=videojs.dom.createEl("div",{className:"tcp-playlist-toggle",textContent:"<"});return this.bindEvent(t),t},e.prototype.bindEvent=function(t){var e=this;videojs.on(t,["click"],function(i){e.status=!e.status;var n=document.querySelector(".tcp-playlist");e.status?(t.textContent=">",videojs.dom.addClass(n,"tcp-playlist-open"),videojs.dom.removeClass(n,"tcp-playlist-close")):(videojs.dom.addClass(n,"tcp-playlist-close"),videojs.dom.removeClass(n,"tcp-playlist-open"),t.textContent="<")})},e}(Component$17);videojs.registerComponent("PlayListToggle",PlayListToggle);var Component$13=videojs.getComponent("Component"),PlayList$1=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.init(),r}return inherits(e,t),e.prototype.createEl=function(){return videojs.dom.createEl("div",{className:"tcp-playlist tcp-playlist-close"})},e.prototype.init=function(){this.addChild("PlayListToggle"),this.addChild("PlayListBox",this.options_)},e}(Component$13);videojs.registerComponent("PlayList",PlayList$1);var Button$5=videojs.getComponent("Button"),PlayNextButton=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i,n));return r.addClass(),r}return inherits(e,t),e.prototype.addClass=function(){videojs.dom.addClass(this.el_,"vjs-play-next")},e.prototype.update=function(){},e.prototype.handleClick=function(t){this.player_.trigger({type:"playItem"})},e}(Button$5);PlayNextButton.prototype.controlText_="Play Next",videojs.registerComponent("PlayNextButton",PlayNextButton);var Plugin$16=videojs.getPlugin("plugin"),log$15=videojs.log,PlayList=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return IS_IOS||IS_ANDROID?possibleConstructorReturn(r):(r.options=n,n&&n.data instanceof Array&&n.data.length>0&&i.ready(videojs.bind(r,r.init)),r)}return inherits(e,t),e.prototype.init=function(){log$15("PlayList initializing"),this.player.addChild("PlayList",this.options),this.player.controlBar.addChild("PlayNextButton",{},1),log$15("PlayList initialized"),this.player.trigger({type:"feature",data:"playlist"})},e}(Plugin$16);videojs.registerPlugin("PlayList",PlayList);var Plugin$17=videojs.getPlugin("plugin"),log$16=videojs.log,HLSToken=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return log$16("HLSToken",i,n),r.init(n),r}return inherits(e,t),e.prototype.init=function(t){log$16("HLSToken init",t,this.player);var e=this.player;t.token&&(this.options=t,e.on("playcgiend",videojs.bind(this,function(t){this.modifyData(t.data)})))},e.prototype.modifyData=function(t){var e=this;if(log$16("HLSToken modifyData",t),0===t.result.code){var i=t.result.videoInfo;if(i.masterPlayList){var n=i.masterPlayList.url.split("/");n[n.length-1]="voddrm.token."+this.options.token+"."+n[n.length-1],i.masterPlayList.url=n.join("/")}i.transcodeList&&i.transcodeList.length>0&&(i.transcodeList=i.transcodeList.map(function(t){var i=t.url.split("/");return i[i.length-1]="voddrm.token."+e.options.token+"."+i[i.length-1],t.url=i.join("/"),t}))}},e}(Plugin$17);videojs.registerPlugin("HLSToken",HLSToken);var getMessageContents=function(t){var e=(new window_1.DOMParser).parseFromString(String.fromCharCode.apply(null,new Uint16Array(t)),"application/xml"),i=e.getElementsByTagName("HttpHeaders")[0],n={};if(i)for(var r=i.getElementsByTagName("name"),o=i.getElementsByTagName("value"),s=0;s<r.length;s++)n[r[s].childNodes[0].nodeValue]=o[s].childNodes[0].nodeValue;var a=e.getElementsByTagName("Challenge")[0],l=void 0;return a&&(l=window_1.atob(a.childNodes[0].nodeValue)),{headers:n,message:l}},requestPlayreadyLicense=function(t,e,i){var n=getMessageContents(e),r=n.headers,o=n.message;videojs.xhr({uri:t,method:"post",headers:r,body:o,responseType:"arraybuffer"},i)},getSupportedKeySystem=function(t){var e=void 0;return Object.keys(t).forEach(function(i){var n={},r=t[i].audioContentType,o=t[i].videoContentType;r&&(n.audioCapabilities=[{contentType:r}]),o&&(n.videoCapabilities=[{contentType:o}]),e=e?e["catch"](function(t){return window_1.navigator.requestMediaKeySystemAccess(i,[n])}):window_1.navigator.requestMediaKeySystemAccess(i,[n])}),e},makeNewRequest=function(t){var e=t.mediaKeys,i=t.initDataType,n=t.initData,r=t.options,o=t.getLicense,s=t.removeSession,a=t.eventBus,l=e.createSession()
;l.addEventListener("message",function(t){o(r,t.message).then(function(t){return l.update(t)})["catch"](videojs.log.error.bind(videojs.log.error,"failed to get and set license"))},!1),l.addEventListener("keystatuseschange",function(t){var e=!1;l.keyStatuses.forEach(function(i,n){switch(a.trigger({keyId:n,status:i,target:l,type:"keystatuschange"}),i){case"expired":e=!0;break;case"internal-error":videojs.log.warn('Key status reported as "internal-error." Leaving the session open since we don\'t have enough details to know if this error is fatal.',t)}}),e&&l.close().then(function(){s(n)})},!1),l.generateRequest(i,n)["catch"](videojs.log.error.bind(videojs.log.error,"Unable to create or initialize key session"))},addSession=function(t){var e=t.video,i=t.initDataType,n=t.initData,r=t.options,o=t.getLicense,s=t.removeSession,a=t.eventBus;e.mediaKeysObject?makeNewRequest({mediaKeys:e.mediaKeysObject,initDataType:i,initData:n,options:r,getLicense:o,removeSession:s,eventBus:a}):e.pendingSessionData.push({initDataType:i,initData:n})},setMediaKeys=function(t){var e=t.video,i=t.certificate,n=t.createdMediaKeys,r=t.options,o=t.getLicense,s=t.removeSession,a=t.eventBus;e.mediaKeysObject=n,i&&n.setServerCertificate(i);for(var l=0;l<e.pendingSessionData.length;l++){var u=e.pendingSessionData[l];makeNewRequest({mediaKeys:e.mediaKeysObject,initDataType:u.initDataType,initData:u.initData,options:r,getLicense:o,removeSession:s,eventBus:a})}return e.pendingSessionData=[],e.setMediaKeys(n)},defaultPlayreadyGetLicense=function(t){return function(e,i,n){requestPlayreadyLicense(t,i,function(t,e,i){if(t)return void n(t);n(null,i)})}},defaultGetLicense=function(t){return function(e,i,n){videojs.xhr({uri:t,method:"POST",responseType:"arraybuffer",body:i,headers:{"Content-type":"application/octet-stream"}},function(t,e,i){if(t)return void n(t);n(null,i)})}},promisifyGetLicense=function(t,e){return function(i,n){return new Promise(function(r,o){t(i,n,function(t,i){e&&e.trigger("licenserequestattempted"),t&&o(t),r(i)})})}},standardizeKeySystemOptions=function(t,e){if("string"==typeof e&&(e={url:e}),!e.url&&!e.getLicense)throw new Error("Neither URL nor getLicense function provided to get license");return e.url&&!e.getLicense&&(e.getLicense="com.microsoft.playready"===t?defaultPlayreadyGetLicense(e.url):defaultGetLicense(e.url)),e},standard5July2016=function(t){var e=t.video,i=t.initDataType,n=t.initData,r=t.options,o=t.removeSession,s=t.eventBus,a=Promise.resolve();if("undefined"==typeof e.mediaKeysObject){e.mediaKeysObject=null,e.pendingSessionData=[];var l=void 0,u=void 0;if(!(a=getSupportedKeySystem(r.keySystems)))return videojs.log.error("No supported key system found"),Promise.resolve();a=a.then(function(t){return new Promise(function(i,n){if(e.keySystem=t.keySystem,u=standardizeKeySystemOptions(t.keySystem,r.keySystems[t.keySystem]),!u.getCertificate)return void i(t);u.getCertificate(r,function(e,r){if(e)return void n(e);l=r,i(t)})})}).then(function(t){return t.createMediaKeys()}).then(function(t){return setMediaKeys({video:e,certificate:l,createdMediaKeys:t,options:r,getLicense:promisifyGetLicense(u.getLicense,s),removeSession:o,eventBus:s})})["catch"](videojs.log.error.bind(videojs.log.error,"Failed to create and initialize a MediaKeys object"))}return a.then(function(){addSession({video:e,initDataType:i,initData:n,options:r,getLicense:e.keySystem?promisifyGetLicense(standardizeKeySystemOptions(e.keySystem,r.keySystems[e.keySystem]).getLicense,s):null,removeSession:o,eventBus:s})})},stringToUint16Array=function(t){for(var e=new ArrayBuffer(2*t.length),i=new Uint16Array(e),n=0;n<t.length;n++)i[n]=t.charCodeAt(n);return i},uint8ArrayToString=function(t){return String.fromCharCode.apply(null,new Uint16Array(t.buffer))},getHostnameFromUri=function(t){var e=document_1.createElement("a");return t=t.match(/.*(skd\:\/\/.+)/i)[1],e.href=t,e.hostname},arrayBuffersEqual=function(t,e){if(t===e)return!0;if(t.byteLength!==e.byteLength)return!1;for(var i=new DataView(t),n=new DataView(e),r=0;r<i.byteLength;r++)if(i.getUint8(r)!==n.getUint8(r))return!1;return!0},arrayBufferFrom=function(t){return t instanceof Uint8Array||t instanceof Uint16Array?t.buffer:t},FAIRPLAY_KEY_SYSTEM="com.apple.fps.1_0",concatInitDataIdAndCertificate=function(t){var e=t.initData,i=t.id,n=t.cert;"string"==typeof i&&(i=stringToUint16Array(i));var r=0,o=new ArrayBuffer(e.byteLength+4+i.byteLength+4+n.byteLength),s=new DataView(o);new Uint8Array(o,r,e.byteLength).set(e),r+=e.byteLength,s.setUint32(r,i.byteLength,!0),r+=4;var a=new Uint16Array(o,r,i.length);return a.set(i),r+=a.byteLength,s.setUint32(r,n.byteLength,!0),r+=4,new Uint8Array(o,r,n.byteLength).set(n),new Uint8Array(o,0,o.byteLength)},addKey=function(t){var e=t.video,i=t.contentId,n=t.initData,r=t.cert,o=t.options,s=t.getLicense,a=t.eventBus;return new Promise(function(t,l){if(e.webkitKeys||e.webkitSetMediaKeys(new window_1.WebKitMediaKeys(FAIRPLAY_KEY_SYSTEM)),!e.webkitKeys)return void l("Could not create MediaKeys");var u=e.webkitKeys.createSession("video/mp4",concatInitDataIdAndCertificate({id:i,initData:n,cert:r}));if(!u)return void l("Could not create key session");u.contentId=i,u.addEventListener("webkitkeymessage",function(t){s(o,i,t,function(t,e){if(a&&a.trigger("licenserequestattempted"),t)return void l(t);u.update(new Uint8Array(e))})}),u.addEventListener("webkitkeyadded",function(e){t(e)}),u.addEventListener("webkitkeyerror",function(t){l(t)})})},defaultGetCertificate=function(t){return function(e,i){videojs.xhr({uri:t,responseType:"arraybuffer"},function(t,e,n){if(t)return void i(t);i(null,new Uint8Array(n))})}},defaultGetContentId=function(t,e){return getHostnameFromUri(uint8ArrayToString(e))},defaultGetLicense$1=function(t){return function(e,i,n,r){videojs.xhr({uri:t,method:"POST",responseType:"arraybuffer",body:n.message,headers:{"Content-type":"application/x-www-form-urlencoded"}},function(t,e,i){if(t)return void r(t);r(null,i)})}},fairplay=function(t){var e=t.video,i=t.initData,n=t.options,r=t.eventBus,o=n.keySystems[FAIRPLAY_KEY_SYSTEM],s=o.getCertificate||defaultGetCertificate(o.certificateUri),a=o.getContentId||defaultGetContentId,l=o.getLicense||defaultGetLicense$1(o.licenseUri);return new Promise(function(t,e){s(n,function(i,n){if(i)return void e(i);t(n)})}).then(function(t){return addKey({video:e,cert:t,initData:i,getLicense:l,options:n,contentId:a(n,i),eventBus:r})})["catch"](function(t){r.player_.error({code:3,message:t})})},PLAYREADY_KEY_SYSTEM="com.microsoft.playready",addKeyToSession=function(t,e,i,n){var r=t.keySystems[PLAYREADY_KEY_SYSTEM];if("function"==typeof r.getKey)return void r.getKey(t,i.destinationURL,i.message.buffer,function(t,i){if(t)return void videojs.log.error("Unable to get key: "+t);e.update(i)});"string"==typeof r&&(r={url:r});var o=r.url||i.destinationURL;requestPlayreadyLicense(o,i.message.buffer,function(t,i){if(n&&n.trigger("licenserequestattempted"),t)return void videojs.log.error("Unable to request key from url: "+o);e.update(new Uint8Array(i.body))})},createSession=function(t,e,i,n){var r=t.msKeys.createSession("video/mp4",e);if(!r)return void videojs.log.error("Could not create key session.");r.addEventListener("mskeymessage",function(t){addKeyToSession(i,r,t,n)}),r.addEventListener("mskeyerror",function(t){videojs.log.error("Unexpected key error from key session with code: "+r.error.code+" and systemCode: "+r.error.systemCode)})},msPrefixed=function(t){var e=t.video,i=t.initData,n=t.options,r=t.eventBus;e.msKeys&&delete e.msKeys;try{e.msSetMediaKeys(new window_1.MSMediaKeys(PLAYREADY_KEY_SYSTEM))}catch(o){return void videojs.log.error("Unable to create media keys for PlayReady key system. Error: "+o.message)}createSession(e,i,n,r)},Plugin$18=videojs.getPlugin("plugin"),hasSession=function(t,e){for(var i=0;i<t.length;i++)if(t[i].initData){var n=arrayBufferFrom(t[i].initData),r=arrayBufferFrom(e);if(arrayBuffersEqual(n,r))return!0}return!1},removeSession=function(t,e){for(var i=0;i<t.length;i++)if(t[i].initData===e)return void t.splice(i,1)},handleEncryptedEvent=function(t,e,i,n){if(!e||!e.keySystems)return Promise.resolve();var r=t.initData;return getSupportedKeySystem(e.keySystems).then(function(o){var s=o.keySystem;if(e.keySystems[s]&&e.keySystems[s].pssh&&(r=e.keySystems[s].pssh),!hasSession(i,r)&&r)return i.push({initData:r}),standard5July2016({video:t.target,initDataType:t.initDataType,initData:r,options:e,removeSession:removeSession.bind(null,i),eventBus:n})})},handleWebKitNeedKeyEvent=function(t,e,i){if(e.keySystems&&e.keySystems[FAIRPLAY_KEY_SYSTEM]&&t.initData)return fairplay({video:t.target,initData:t.initData,options:e,eventBus:i})},handleMsNeedKeyEvent=function(t,e,i,n){if(e.keySystems&&e.keySystems[PLAYREADY_KEY_SYSTEM]&&!i.reduce(function(t,e){return t||e.playready},!1)){var r=t.initData;e.keySystems[PLAYREADY_KEY_SYSTEM]&&e.keySystems[PLAYREADY_KEY_SYSTEM].pssh&&(r=e.keySystems[PLAYREADY_KEY_SYSTEM].pssh),r&&(i.push({playready:!0,initData:r}),msPrefixed({video:t.target,initData:r,options:e,eventBus:n}))}},getOptions=function(t){return videojs.mergeOptions(t.currentSource(),t.eme.options)},setupSessions=function(t){var e=t.src();e!==t.eme.activeSrc&&(t.eme.activeSrc=e,t.eme.sessions=[])},onPlayerReady=function(t){"video"===t.$(".vjs-tech").tagName.toLowerCase()&&t.tech_&&(setupSessions(t),t.tech_.el_.addEventListener("encrypted",function(e){if(t.currentSource().keySystems&&t.currentSource().keySystems["com.widevine.alpha"]&&"application/dash+xml"===t.currentSource().type)return!1;setupSessions(t),handleEncryptedEvent(e,getOptions(t),t.eme.sessions,t.tech_)}),t.tech_.el_.addEventListener("webkitneedkey",function(e){setupSessions(t),handleWebKitNeedKeyEvent(e,getOptions(t),t.tech_)}),videojs.browser.IS_EDGE||t.tech_.el_.addEventListener("msneedkey",function(e){setupSessions(t),handleMsNeedKeyEvent(e,getOptions(t),t.eme.sessions,t.tech_)}))},DRM=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,i.ready(function(){return onPlayerReady(i)}),i.eme={initializeMediaKeys:function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:function(){},r=videojs.mergeOptions(i.currentSource(),n,t),o={initDataType:"cenc",initData:null,target:i.tech_.el_};setupSessions(i),i.tech_.el_.setMediaKeys?handleEncryptedEvent(o,r,i.eme.sessions,i.tech_).then(function(){return e()})["catch"](function(t){return e(t)}):i.tech_.el_.msSetMediaKeys&&(handleMsNeedKeyEvent(o,r,i.eme.sessions,i.tech_),e())},options:n},r}return inherits(e,t),e.prototype.setOptions=function(t){this.options=t,this.player.options_.plugins.DRM=t},e}(Plugin$18),registerPlugin$2=videojs.registerPlugin||videojs.plugin;registerPlugin$2("DRM",DRM);var Plugin$19=videojs.getPlugin("plugin"),dom$2=videojs.dom;if(!window.requestAnimationFrame){var lastTime=0;window.requestAnimationFrame=function(t){var e=(new Date).getTime(),i=Math.max(0,16.7-(e-lastTime)),n=window.setTimeout(function(){t(e+i)},i);return lastTime=e+i,n}}window.cancelAnimationFrame||(window.cancelAnimationFrame=function(t){clearTimeout(t)});var DynamicWatermark=function(t){function e(i,n){classCallCheck(this,e);var r=possibleConstructorReturn(this,t.call(this,i));return r.options=n,n.content&&r.init(),r}return inherits(e,t),e.prototype.dispose=function(){this.stopAnimation(),this.player.el().removeChild(this.containerElement),t.prototype.dispose.call(this)},e.prototype.init=function(){var t=this,e=this.player;this.initUI(),this.initStyle(),e.on("loadedmetadata",function(){t.calculateStyle(),t.setStyle(),dom$2.removeClass(t.containerElement,"vjs-hidden")}),e.one("play",function(){t.startAnimation()}),e.on(["fullscreenchange","playerresize"],function(){setTimeout(function(){t.calculateStyle()},200)})},e.prototype.initUI=function(){var t=this.player,e=dom$2.createEl("div",{className:"tcp-dynamic-watermark-container vjs-hidden"}),i=dom$2.createEl("div",{innerHTML:encodeHTML(this.options.content),className:"tcp-dynamic-watermark-content"});if(e.appendChild(i),t.el().appendChild(e),this.containerElement=e,this.markContentElement=i,"image"===this.options.type){var n=document.createElement("img");this.options.width&&(n.style.width=this.options.width),this.options.height&&(n.style.height=this.options.height),n.setAttribute("src",this.options.content),this.markContentElement.innerHTML="",this.markContentElement.appendChild(n)}},e.prototype.initStyle=function(){this.style={containerWidth:"",containerHeight:"",containerLeft:"",containerTop:"",contentLeft:80*Math.random(),contentTop:80*Math.random(),margin:0,padding:0,display:"block",visibility:"visible",opacity:.2,position:"absolute",color:"rgba(255, 255, 255)"}},e.prototype.calculateStyle=function(){var t=this.player,e=this.style,i=t.videoWidth(),n=t.videoHeight(),r=dom$2.getBoundingClientRect(t.el()),o=r.width,s=r.height,a=(o/s).toFixed(2),l=(i/n).toFixed(2),u=void 0,c=void 0;l<a?(c=s,u=c*l):l>a?(u=o,c=u/l):l==a&&(u=o,c=s),e.containerLeft=(o-u)/2/o*100,e.containerTop=(s-c)/2/s*100,e.containerWidth=u/o*100,e.containerHeight=c/s*100},e.prototype.setStyle=function(){var t=this.containerElement,e=this.markContentElement,i=this.style;t.style.left=i.containerLeft+"%",t.style.top=i.containerTop+"%",t.style.width=i.containerWidth+"%",t.style.height=i.containerHeight+"%",t.style.display=e.style.display=i.display,t.style.visibility=e.style.visibility=i.visibility,t.style.opacity=e.style.opacity=i.opacity,t.style.position=e.style.position=i.position,t.style.margin=e.style.margin=i.margin,t.style.padding=e.style.padding=i.padding,e.style.left=this.options.left||i.contentLeft+"%",e.style.top=this.options.top||i.contentTop+"%",this.options.right&&(e.style.right=this.options.right),this.options.bottom&&(e.style.bottom=this.options.bottom),e.style.color=this.options.color||i.color,t.style.opacity=e.style.opacity=this.options.opacity||i.opacity,"text"===this.options.type&&(e.style.fontSize=this.options.fontSize||"12px",e.innerHTML=encodeHTML(this.options.content))},e.prototype.setContent=function(t){this.options.content=t},e.prototype.startAnimation=function(){var t=this;if(!this.rafID){var e=this.player,i=this.style,n=this.containerElement,r=this.markContentElement,o=1,s=1,a=Math.random(),l=function u(){try{if(!t.isPauseAnimation){var l=Math.min(1,0===t.options.speed?0:t.options.speed?t.options.speed:.2),c=dom$2.getBoundingClientRect(n),h=dom$2.getBoundingClientRect(r),p=h.left-c.left,d=h.top-c.top;p+=l*o*a,d+=l*s*(1-a),p+h.width>c.width?(o=-1,a=Math.random()):p<0&&(o=1,a=Math.random()),d+h.height>c.height?(s=-1,a=Math.random()):d<0&&(s=1,a=Math.random()),p=Math.min(c.width-h.width,p),d=Math.min(c.height-h.height,d),i.contentLeft=p/c.width*100,i.contentTop=d/c.height*100,t.setStyle()}}catch(f){e.$(".tcp-dynamic-watermark-container")?e.$(".tcp-dynamic-watermark-content")||n.appendChild(r):e.el()&&e.el().appendChild(n)}if(t.isStopAnimation)return t.isStopAnimation=!1,cancelAnimationFrame(t.rafID),void(t.rafID=null);0!==t.options.speed&&requestAnimationFrame(u)};this.rafID=requestAnimationFrame(l)}},e.prototype.resumeAnimation=function(){this.isPauseAnimation=!1},e.prototype.pauseAnimation=function(){this.isPauseAnimation=!0},e.prototype.stopAnimation=function(){this.isStopAnimation=!0},e}(Plugin$19);videojs.registerPlugin("DynamicWatermark",DynamicWatermark);var defaultConfig$1={enableStashBuffer:!1},Html5FlvJS=function(){function t(e,i,n){var r=this;classCallCheck(this,t);var o=new flvjs.createPlayer({isLive:!1,url:e.src,type:"flv"},videojs.mergeOptions(defaultConfig$1,n.flvConfig));this.tech=i,o.on(flvjs.Events.ERROR,videojs.bind(this,this.onError));for(var s in flvjs.Events)!function(t){o.on(flvjs.Events[t],videojs.bind(r,function(e){this.onEvent(t,e)}))}(s);this.flv=o,this._id=(new Date).getTime(),o.attachMediaElement(i.el()),o.load()}return t.prototype.onError=function(t,e,i){var n=this.tech.player();t===flvjs.ErrorTypes.NETWORK_ERROR&&n.error({code:2,source:i}),t===flvjs.ErrorTypes.MEDIA_ERROR&&n.error({code:3,source:i})},t.prototype.onEvent=function(t,e){this.tech.player().trigger({type:"FLVJS_EVENT",data:{type:t,data:e}})},t.prototype.dispose=function(){this.tech.player().pause();try{this.flv.destroy()}catch(t){}this.tech.flvProvider=null},t}(),flvTypeRE=/^video\/flv$/i,flvExtRE=/\.flv/i,flvProtoRE=/^(ws:\/\/)/i,FlvSourceHandler={name:"flvSourceHandler",canHandleSource:function(t){return flvTypeRE.test(t.type)?"probably":flvExtRE.test(t.src)||flvProtoRE.test(t.src)?"maybe":""},handleSource:function(t,e,i){return e.flvProvider&&e.flvProvider.dispose(),e.flvProvider=new Html5FlvJS(t,e,i),e.flvProvider},canPlayType:function(t){if(flvTypeRE.test(t))return"probably"}};TCPlayer.mountFlvProvider=mountFlvProvider,mountFlvProvider();var defaultConfig$2={receiveVideo:!0,receiveAudio:!0,showLog:!1},WebRTCProvider=function(){function t(e,i,n){classCallCheck(this,t),this.tech=i,this.playerMetrics=this.tech.player().PlayerMetrics();var r=videojs.mergeOptions(defaultConfig$2,n.webrtcConfig),o=new TXLivePlayer;o.setPlayerView(i.el()),o.setConfig(r),o.setPlayListener({onPlayEvent:videojs.bind(this,this.onPlayEvent),onPlayStats:videojs.bind(this,this.onPlayStats),onPlayReport:videojs.bind(this,this.onPlayReport)}),o.startPlay(e.src),this.webrtcPlayer=o}return t.prototype.onPlayStats=function(t){this.tech.player().trigger({type:"webrtcstats",data:t})},t.prototype.onPlayEvent=function(t,e){var i=this.tech.player();if(t<-2e3)return-2003!==t&&(-2005===t||-2001===t?(this.switchPlaySource(),!1):(i.error({code:t,source:e}),!1));i.trigger({type:"webrtcevent",data:{code:t,data:e}})},t.prototype.onPlayReport=function(t){40101===t.uint32_command&&this.playerMetrics.report_40101(t),40100===t.uint32_command&&this.playerMetrics.report_40100(t),40102===t.uint32_command&&this.playerMetrics.report_40102(t)},t.prototype.dispose=function(){this.webrtcPlayer&&(this.webrtcPlayer.setPlayListener({onPlayEvent:function(){},onPlayStats:function(){}}),this.webrtcPlayer.stopPlay(),this.webrtcPlayer.setPlayerView(videojs.dom.createEl("video"))),this.webrtcPlayer=null},t.prototype.setSource=function(t){var e=this;this.webrtcPlayer.startPlay(t.src),this.tech.player().one("webrtcevent",function(t){-1002===t.data.code&&e.tech.player().play()})},t.prototype.recovery=function(){var t=this,e=this.tech.player(),i=void 0;e.one("waiting",function(){i=setTimeout(function(){e.one("webrtcevent",function(i){if(1004===i.data.code){t.webrtcPlayer&&t.webrtcPlayer.startPlay(e.src()),e.play();var n=function r(t){1002===t.data.code&&(e.play(),e.off("webrtcevent",r))};e.on("webrtcevent",n)}}),t.webrtcPlayer&&t.webrtcPlayer.stopPlay()},2e3)}),e.one("playing",function(){i&&clearTimeout(i)})},t.prototype.switchPlaySource=function(){var t=this.tech.player(),e=this.tech.currentSource_.src,i=this.convertProtocol(e);t.bigPlayButton&&t.bigPlayButton.hide(),t.posterImage&&t.posterImage.hide(),t.src(i);var n=videojs.browser.IS_IOS||videojs.browser.IS_ANDROID?"loadeddata":"loadedmetadata",r=function o(){t.play(),t.one("progress",function(){t.play(),t.off(n,o)})};t.on(n,r)},t.prototype.convertProtocol=function(t){var e=[];if(t.indexOf(".sdp")>-1)t=t.replace(".sdp",".flv"),e.push(t.replace(".sdp",".m3u8"));else{var i=t.replace("webrtc://","https://").replace("?",".flv?");-1===i.indexOf("?")&&-1===i.indexOf(".flv")&&(i+=".flv"),hlsSrc=t.replace("webrtc://","https://").replace("?",".m3u8?"),-1===hlsSrc.indexOf("?")&&-1===hlsSrc.indexOf(".m3u8")&&(hlsSrc+=".m3u8"),e.push(hlsSrc)}return e},t}(),webrtcTypeRE=/^webrtc/i,webrtcExtRE=/\.sdp/i,webrtcProtoRE=/^(webrtc:\/\/)/i,sourceHandler={name:"webrtcSourceHandler",canHandleSource:function(t){return webrtcTypeRE.test(t.type)?"probably":webrtcExtRE.test(t.src)||webrtcProtoRE.test(t.src)?"maybe":""},handleSource:function(t,e,i){return e.webrtcProvider&&e.webrtcProvider.dispose(),e.webrtcProvider=new WebRTCProvider(t,e,i),e.webrtcProvider},canPlayType:function(t){if(webrtcTypeRE.test(t))return"probably"}};mountWebRTCProvider();for(var Tech$2=videojs.getComponent("Tech"),Dom$1=videojs.dom,Url$1=videojs.url,createTimeRange=videojs.createTimeRange,mergeOptions$1=videojs.mergeOptions,navigator$2=window_1&&window_1.navigator||{},Flash=function(t){function e(i,n,r){classCallCheck(this,e);var o=possibleConstructorReturn(this,t.call(this,i,n,r));return n.source&&o.ready(function(){this.setSource(n.source)},!0),n.startTime&&o.ready(function(){this.load(),this.play(),this.currentTime(n.startTime)},!0),window_1.videojs=window_1.videojs||{},window_1.videojs.Flash=window_1.videojs.Flash||{},window_1.videojs.Flash.onReady=e.onReady,window_1.videojs.Flash.onEvent=e.onEvent,window_1.videojs.Flash.onError=e.onError,o.on("seeked",function(){this.lastSeekTarget_=undefined}),o}return inherits(e,t),e.prototype.createEl=function(){var t=this.options_;t.swf||(t.swf=unifyProtocol("//imgcache.qq.com/open/qcloud/video/tcplayer/player.swf"));var i=t.techId,n=mergeOptions$1({readyFunction:"videojs.Flash.onReady",eventProxyFunction:"videojs.Flash.onEvent",errorEventProxyFunction:"videojs.Flash.onError",autoplay:t.autoplay,preload:t.preload,loop:t.loop,muted:t.muted},t.flashVars),r=mergeOptions$1({wmode:"opaque",bgcolor:"#000000"},t.params),o=mergeOptions$1({id:i,name:i,"class":"vjs-tech"},t.attributes);return this.el_=e.embed(t.swf,n,r,o),this.el_.tech=this,this.el_},e.prototype.play=function(){this.ended()&&this.setCurrentTime(0),this.el_.vjs_play()},e.prototype.pause=function(){this.el_.vjs_pause()},e.prototype.src=function(t){return t===undefined?this.currentSrc():this.setSrc(t)},e.prototype.setSrc=function(t){var e=this;t=Url$1.getAbsoluteURL(t),this.el_.vjs_src(t),this.autoplay()&&this.setTimeout(function(){return e.play()},0)},e.prototype.seeking=function(){return this.lastSeekTarget_!==undefined},e.prototype.setCurrentTime=function(e){var i=this.seekable();i.length&&(e=e>i.start(0)?e:i.start(0),e=e<i.end(i.length-1)?e:i.end(i.length-1),this.lastSeekTarget_=e,this.trigger("seeking"),this.el_.vjs_setProperty("currentTime",e),t.prototype.setCurrentTime.call(this))},e.prototype.currentTime=function(){return this.seeking()?this.lastSeekTarget_||0:this.el_.vjs_getProperty("currentTime")},e.prototype.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.vjs_getProperty("currentSrc")},e.prototype.duration=function(){if(0===this.readyState())return NaN;var t=this.el_.vjs_getProperty("duration");return t>=0?t:Infinity},e.prototype.load=function(){this.el_.vjs_load()},e.prototype.poster=function(){this.el_.vjs_getProperty("poster")},e.prototype.setPoster=function(){},e.prototype.seekable=function(){var t=this.duration();return 0===t?createTimeRange():createTimeRange(0,t)},e.prototype.buffered=function(){var t=this.el_.vjs_getProperty("buffered");return 0===t.length?createTimeRange():createTimeRange(t[0][0],t[0][1])},e.prototype.supportsFullScreen=function(){return!1},e.prototype.enterFullScreen=function(){return!1},e.prototype.getVideoPlaybackQuality=function(){var t=this.el_.vjs_getProperty("getVideoPlaybackQuality");return window_1.performance&&"function"==typeof window_1.performance.now?t.creationTime=window_1.performance.now():window_1.performance&&window_1.performance.timing&&"number"==typeof window_1.performance.timing.navigationStart&&(t.creationTime=window_1.Date.now()-window_1.performance.timing.navigationStart),t},e}(Tech$2),_readWrite=["rtmpConnection","rtmpStream","preload","defaultPlaybackRate","playbackRate","autoplay","loop","controls","volume","muted","defaultMuted"],_readOnly=["networkState","readyState","initialTime","startOffsetTime","paused","ended","videoWidth","videoHeight"],_api=Flash.prototype,i$3=0;i$3<_readWrite.length;i$3++)_createGetter(_readWrite[i$3]),_createSetter(_readWrite[i$3]);for(var _i$1=0;_i$1<_readOnly.length;_i$1++)_createGetter(_readOnly[_i$1]);Flash.isSupported=function(){return Flash.version()[0]>=10},Tech$2.withSourceHandlers(Flash),Flash.nativeSourceHandler={},Flash.nativeSourceHandler.canPlayType=function(t){return t in Flash.formats?"maybe":""},Flash.nativeSourceHandler.canHandleSource=function(t,e){var i=void 0;return i=t.type?t.type.replace(/;.*/,"").toLowerCase():function(t){var e=Url$1.getFileExtension(t);return e?"video/"+e:""}(t.src),Flash.nativeSourceHandler.canPlayType(i)},Flash.nativeSourceHandler.handleSource=function(t,e,i){e.setSrc(t.src)},Flash.nativeSourceHandler.dispose=function(){},Flash.registerSourceHandler(Flash.nativeSourceHandler),Flash.formats={"video/flv":"FLV","video/x-flv":"FLV","video/mp4":"MP4","video/m4v":"MP4"},Flash.onReady=function(t){var e=Dom$1.$("#"+t),i=e&&e.tech;i&&i.el()&&Flash.checkReady(i)},Flash.checkReady=function(t){t.el()&&(t.el().vjs_getProperty?t.triggerReady():this.setTimeout(function(){Flash.checkReady(t)},50))},Flash.onEvent=function(t,e){var i=Dom$1.$("#"+t).tech,n=Array.prototype.slice.call(arguments,2);i.setTimeout(function(){i.trigger(e,n)},1)},Flash.onError=function(t,e){var i=Dom$1.$("#"+t).tech;if("srcnotfound"===e)return i.error(4);i.error("FLASH: "+e)},Flash.version=function(){var t="0,0,0";try{t=new window_1.ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version").replace(/\D+/g,",").match(/^,?(.+),?$/)[1]}catch(e){try{navigator$2.mimeTypes["application/x-shockwave-flash"].enabledPlugin&&(t=(navigator$2.plugins["Shockwave Flash 2.0"]||navigator$2.plugins["Shockwave Flash"]).description.replace(/\D+/g,",").match(/^,?(.+),?$/)[1])}catch(e){}}return t.split(",")},Flash.embed=function(t,e,i,n){var r=Flash.getEmbedCode(t,e,i,n);return Dom$1.createEl("div",{innerHTML:r}).childNodes[0]},Flash.getEmbedCode=function(t,e,i,n){var r="",o="",s="";return e&&Object.getOwnPropertyNames(e).forEach(function(t){r+=t+"="+e[t]+"&amp;"}),i=mergeOptions$1({movie:t,flashvars:r,allowScriptAccess:"always",allowNetworking:"all"},i),Object.getOwnPropertyNames(i).forEach(function(t){o+='<param name="'+t+'" value="'+i[t]+'" />'}),n=mergeOptions$1({data:t,width:"100%",height:"100%"},n),Object.getOwnPropertyNames(n).forEach(function(t){s+=t+'="'+n[t]+'" '}),'<object type="application/x-shockwave-flash" '+s+">"+o+"</object>"},FlashRtmpDecorator(Flash),Tech$2.getTech("Flash")?(videojs.log.warn("Not using videojs-flash as it appears to already be registered"),videojs.log.warn("videojs-flash should only be used with video.js@6 and above")):videojs.registerTech("Flash",Flash);var FlashlsSourceHandler={},mpegurlRE$1=/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i;FlashlsSourceHandler.canPlayType=function(t){return mpegurlRE$1.test(t)?"maybe":""},FlashlsSourceHandler.canHandleSource=function(t,e){return"maybe"===FlashlsSourceHandler.canPlayType(t.type)},FlashlsSourceHandler.handleSource=function(t,e,i){e.setSrc(t.src)},FlashlsSourceHandler.dispose=function(){},videojs.getTech("Flash").registerSourceHandler(FlashlsSourceHandler,0),FlashlsSourceHandler.VERSION="__VERSION__";var Play="播放",Pause="暂停",LIVE="直播",Loaded="加载完毕",Progress="进度",Fullscreen="全屏",Mute="静音",Unmute="取消静音",Subtitles="字幕",Captions="内嵌字幕",Chapters="节目段落",Descriptions="描述",Close="关闭",Replay="重播",Text="文字",White="白",Black="黑",Red="红",Green="绿",Blue="蓝",Yellow="黄",Magenta="紫红",Cyan="青",Background="背景",Window="视窗",Transparent="透明",Opaque="不透明",None="无",Raised="浮雕",Depressed="压低",Uniform="均匀",Dropshadow="下阴影",Casual="舒适",Script="手写体",Reset="重启",Done="完成",auto="自动",Mirror="镜像",CN={Play:Play,Pause:Pause,LIVE:LIVE,Loaded:Loaded,Progress:Progress,Fullscreen:Fullscreen,Mute:Mute,Unmute:Unmute,Subtitles:Subtitles,Captions:Captions,Chapters:Chapters,Descriptions:Descriptions,Close:Close,Replay:Replay,Text:Text,White:White,Black:Black,Red:Red,Green:Green,Blue:Blue,Yellow:Yellow,Magenta:Magenta,Cyan:Cyan,Background:Background,Window:Window,Transparent:Transparent,Opaque:Opaque,None:None,Raised:Raised,Depressed:Depressed,Uniform:Uniform,Dropshadow:Dropshadow,Casual:Casual,Script:Script,Reset:Reset,Done:Done,auto:auto,Mirror:Mirror,"Current Time":"当前时间","Duration Time":"时长","Remaining Time":"剩余时间","Stream Type":"媒体流类型","Non-Fullscreen":"退出全屏","Playback Rate":"播放速度","subtitles off":"关闭字幕","captions off":"关闭内嵌字幕","Close Modal Dialog":"关闭弹窗","descriptions off":"关闭描述","Audio Track":"音轨","You aborted the media playback":"视频播放被终止","A network error caused the media download to fail part-way.":"网络错误导致视频下载中途失败。","The media could not be loaded, either because the server or network failed or because the format is not supported.":"视频因格式不支持或者服务器或网络的问题无法加载。","The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":"由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。","No compatible source was found for this media.":"无法找到此视频兼容的源或者当前环境无法播放该视频。","The media is encrypted and we do not have the keys to decrypt it.":"视频已加密，无法解密。","Play Video":"播放视频","Modal Window":"弹窗","This is a modal window":"这是一个弹窗","This modal can be closed by pressing the Escape key or activating the close button.":"可以按ESC按键或启用关闭按钮来关闭此弹窗。",", opens captions settings dialog":", 开启标题设置弹窗",", opens subtitles settings dialog":", 开启字幕设置弹窗",", opens descriptions settings dialog":", 开启描述设置弹窗",", selected":", 选择","captions settings":"字幕设定","Audio Player":"音频播放器","Video Player":"视频播放器","Progress Bar":"进度小节","Volume Level":"音量","subtitles settings":"字幕设定","descriptions settings":"描述设定","Semi-Transparent":"半透明","Font Size":"字体尺寸","Text Edge Style":"字体边缘样式","Font Family":"字体库","Proportional Sans-Serif":"比例无细体","Monospace Sans-Serif":"单间隔无细体","Proportional Serif":"比例细体","Monospace Serif":"单间隔细体","Small Caps":"小型大写字体","restore all settings to the default values":"恢复全部设定至预设值","Caption Settings Dialog":"字幕设定视窗","Beginning of dialog window. Escape will cancel and close the window.":"开始对话视窗。离开会取消及关闭视窗","End of dialog window.":"结束对话视窗","Request timed out.":"请求超时，请稍后再试。","Could not download the video.":"无法加载视频，请检查网络。","Server is not respond.":"服务器请求失败，请稍后再试。","Server respond error data.":"服务器返回数据有误，请稍后再试。","Last time play at ":"上次看到 ","Resume play":"恢复播放","Powered by Tencent Cloud.":"腾讯云提供技术支持","Rise an internal exception when playing HLS.":"播放 HLS 时出现内部异常。","Authentication failed.":"防盗链参数鉴权失败。","Server failed.":"媒体服务器错误。","Get file error.":"媒体服务器获取文件错误。","The media file does not exist. Please check if the fileID is correct.":"媒体文件不存在，请检查 fileID 是否正确。","No video transcoding information found.":"没有找到视频转码信息。","The trial duration is illegal. The trial duration must be within the video duration.":"试看时长不合法，试看时长要在视频时长范围内。","Param pcfg is not unique.":"pcfg 不唯一。","The license has expired. Please check whether the expiration time setting is reasonable.":"license 过期，请检查过期时间设置是否合理。","Did not find an adaptive stream that can be played.":"没有找到可以播放的自适应码流，<a href='https://cloud.tencent.com/document/product/266/34071' style='color: white;' target='_blank'>查看文档</a>。","Invalid request format, please check the request format.":"请求格式不合法，请检查请求格式。","AppID is not exist, Please check if the AppID is correct.":"AppID 不存在，请检查 AppID 是否正确。","Without anti-leech information.":"没带防盗链检测。","psign check failed.":"播放参数 psign 校验失败，<a href='https://cloud.tencent.com/document/product/266/45554' style='color: white' target='_blank'>查看文档</a>。","Other errors.":"其他错误。","Internal error.":"内部错误。","Video statistic":"视频统计信息","Play Next":"下一个","Current browser not support play this stream, please select another one.":"无法找到此视频兼容的源或者当前环境无法播放该视频，请选择其他视频播放。","Server respond error data.(eg. stream not exist)":"媒体服务器获取数据异常，可能该视频不存在，请选择其他视频播放。","Video play failed, please refresh to start play again.":"视频播放器失败，请刷新并重新播放。","Connection to the server has failed and the number of connection retries has exceeded the set value.":"媒体服务器连接异常，并达到最大重试次数，请检查网络是否正常并刷新重试","Video decoding failure.":"视频解码失败，请选择其他视频播放。"},log=videojs.log;if(Function.prototype.bind&&"object"==("undefined"==typeof console?"undefined":_typeof(console))&&"object"==_typeof(console.log))for(var logFns=["log","info","warn","error","assert","dir","clear","profile","profileEnd"],i$1=0;i$1<logFns.length;i$1++){var method=logFns[i$1]
;console[method]=Function.prototype.call.bind(console[method],console)}return videojs.addLanguage("zh-CN",CN),Object.keys(videojs).forEach(function(t){TCPlayer[t]=videojs[t]}),TCPlayer});
