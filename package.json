{"name": "vite-project", "private": true, "version": "0.0.0", "scripts": {"start": "vite --open --mode mock", "dev": "vite --open --mode development", "pro": "vite --open --mode product", "dev:test": "vite --open --mode test", "dev:pro": "vite --open --mode product", "build": "vite build --mode development", "build:test": "vite build --mode test", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.0.8", "@vitejs/plugin-vue-jsx": "^1.3.10", "axios": "^0.27.2", "element-plus": "^2.2.9", "moment": "^2.29.4", "nprogress": "^0.2.0", "pinia": "^2.0.15", "pinia-use-persist": "^0.0.21", "qrcode.vue": "^3.3.3", "sass": "^1.53.0", "swiper": "^8.3.1", "tcadapter": "^0.0.8", "vite-svg-loader": "^3.4.0", "vue": "^3.2.25", "vue-router": "^4.1.2"}, "devDependencies": {"@vitejs/plugin-vue": "^2.3.3", "vite": "^2.9.9"}}